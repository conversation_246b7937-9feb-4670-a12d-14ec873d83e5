#!/bin/bash
# Clean script for CALCIFER on Linux

echo "========================================"
echo "CALCIFER Clean Script for Linux"
echo "========================================"

echo "Cleaning build directory..."
if [ -d "build" ]; then
    rm -rf build
    echo "Build directory removed."
else
    echo "Build directory not found."
fi

echo "Cleaning object files..."
find . -name "*.o" -type f -delete
find . -name "*.obj" -type f -delete
find . -name "*.mod" -type f -delete
find . -name "*.exe" -type f -delete
find . -name "*.lib" -type f -delete
find . -name "*.a" -type f -delete

echo
echo "========================================"
echo "Clean completed!"
echo "========================================"
echo 