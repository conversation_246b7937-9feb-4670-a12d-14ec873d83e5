	SUBROUTINE set_side_outlets(NX,SOR,NB<PERSON><PERSON><PERSON>,INDMA<PERSON>LE,DIMEN,LONGZONES,&
	                   & NBTRANCHZONES)

! **************************************************************************
! Traitement des sorties definies dans le .lim
! **************************************************************************

	USE modlim

	IMPLICIT NONE
	
	INTEGER :: NBZONES
	INTEGER, INTENT(IN) :: NX
	
	REAL,INTENT(INOUT),DIMENSION(NX)::SOR, DIMEN
	REAL, INTENT(IN),DIMENSION(nbzones):: longzones
	INTEGER, INTENT(IN),DIMENSION(nbzones+1)::INDM<PERSON><PERSON>LE
	INTEGER, DIMENSION(nbzones):: NBTRAN<PERSON>ZONES

	CHARACTER*2 :: A

	INTEGER, PARAMETER :: DIM=100
	INTEGER :: i,L
	INTEGER :: l1,l2
	INTEGER :: iz
	INTEGER :: NCARNEAUX
	INTEGER, DIMENSION(nbzones):: Zone
	REAL, DIMENSION(DIM) :: MCARNEAU
	
	REAL :: SMALL,CALC1

!cth Impression
      INTEGER :: ires(3)
      COMMON/impr/ires

!
! Lecture du fichier carneaux.don
!--------------------------------
	SMALL=0.0001
	i=0
	MCARNEAU=0.
	SOR=0.

        A=' '
        A='v1'

        if (nsortie.eq.0) then
          NCARNEAUX=NBZONES
          do i=1,NCARNEAUX
            zone(i)=i
            MCARNEAU(i)=0.0
          enddo
        else if (nsortie.ge.1) then
          NCARNEAUX=nsortie
          do i=1,NCARNEAUX
            zone(i)=idsortie(i)
            MCARNEAU(i)=dsortie(i)
          enddo
        endif

	  L=1
	  DO I=1,NCARNEAUX
            IZ=ZONE(I)
	    L1=INDMAILLE(IZ)+1
	    L2=INDMAILLE(IZ)+NBTRANCHZONES(IZ)
	    DO L=L1,L2
	      SOR(L)=SOR(L)+ MCARNEAU(I)*DIMEN(L)/LONGZONES(IZ)
	      IF (L.EQ.NX+1)  Print*,'Probleme Zone Carneaux : L > NX'
	    ENDDO
	  ENDDO
!
! Calcul du debit total des carneaux
!-----------------------------------
	calc1=0.
	DO i=1,NX
	  calc1=calc1+SOR(i)
	ENDDO
!
! Fin de la routine
!------------------

        if (ires(1).ge.1) then
          print *,' CARNEAUX : NSORTIE = ',nsortie
          print *,' CARNEAUX : DEBIT TOTAL = ',calc1
          print *,' CARNEAUX : NCARNEAUX = ',NCARNEAUX
          do i=1,NCARNEAUX
            print *,i,zone(i), MCARNEAU(i)
          enddo
        endif

        return

!cth Erreur ouverture
9997    continue
        print *,' Erreur ouverture fichier carneaux.don '
        stop ' MIEUX VAUT S ARRETER '
	END		
