@echo off
REM Clean script for CALCIFER on Windows 11

echo ========================================
echo CALCIFER Clean Script for Windows 11
echo ========================================

echo Cleaning build directory...
if exist build (
    rmdir /s /q build
    echo Build directory removed.
) else (
    echo Build directory not found.
)

echo Cleaning object files...
for /r . %%f in (*.o *.obj *.mod *.exe *.lib *.a) do (
    if exist "%%f" (
        del "%%f"
        echo Removed: %%f
    )
)

echo.
echo ========================================
echo Clean completed!
echo ========================================
echo.
pause 