        subroutine write_data_comb()
!        
!*****************************************************************
!
!	Modifications : (05/03) Y. <PERSON>
!					Remplacement de residus.out par atmo.cvg
!
!*****************************************************************
!

        USE modTwoDRad
        USE modshared

	IMPLICIT NONE
!
! Declaration des variables
!--------------------------

        INTEGER :: longcar
        INTEGER :: i,j
        INTEGER :: ideb,ifin,ipas
        REAL :: MO2,MN2,MCO2,MAr,MH2O,R,nm3h
        PARAMETER(MO2=0.032,MN2=0.028,MCO2=0.044,MAr=0.040,MH2O=0.018,R=8.3143)
        CHARACTER*130 filename, comline

        REAL :: sommol,sommas,diffsom

!cth AJOUTER 14 decembre 2006 car il n y avait pas de conversion molaire
!cth pour la fraction de fuel
        REAL :: PCI
        REAL :: WM
        REAL :: R_O2_FUEL,R_CO2_FUEL,R_H2O_FUEL
        REAL :: R_H2O_CO2, R_CO2_O2, R_N2_O2
        REAL :: XN2
        REAL :: INVWM
        COMMON/combustible/PCI,WM,R_O2_FUEL,R_CO2_FUEL&
     &  ,R_H2O_FUEL,XN2,INVWM,R_H2O_CO2,R_CO2_O2,R_N2_O2
!cth

	INTEGER :: ires(3)
        COMMON/impr/ires

!cth Ecriture dans l orientation de la charge
        if (code_calcul.eq.code_four_only) then
          ideb=nx
          ifin=1
          ipas=-1
        else
          ideb=nx
          ifin=1
          ipas=-1
        endif

!
! Ecriture geometrie
!-------------------------------------------------------

        if(code_charge.eq.CODE_SMR)then

	  filename=casename(1:lcasename)//'_'//moduname(code_max)(1:longcar(moduname(code_max)))//'.mesh'
          open(unit=27,file=filename)
          comline = 'CELLS POSITION AND DIMENSIONS FROM TOP [m] '
  	  write(27,90) cversout, comline
          write(27,150) ' Iter  X         DX        DY        DZ'
          j=0
          do i=ideb,ifin,ipas
            j=j+1
            write(27,200) j,lxmax-x(i),dx(i),largfour(i),hautfour(i)
          enddo
          close(unit=27)

        else if (ires(1).ge.1) then

	  filename=casename(1:lcasename)//'_'//moduname(code_max)(1:longcar(moduname(code_max)))//'.mesh'
	  open(unit=27,file=filename)
          comline= 'Set furnace geometry '
	  write(27,90) cversout, comline
          write(27,150) ' Iter  DX      LFOUR    LSOLE    HFOUR'
          j=0
          do i=ideb,ifin,ipas
            j=j+1
            write(27,200) j,dx(i),largfour(i),largsole(i),hautfour(i)
          enddo
          close(unit=27)

        endif

!
! Ecriture debits
!-------------------------------------------------------

        if(code_charge.eq.CODE_SMR)then

	  filename=casename(1:lcasename)//'_'//moduname(code_max)(1:longcar(moduname(code_max)))//'.flow'
          open(unit=27,file=filename)
          comline = 'FLOWRATE OF FLUE GASES THROW FACES [Nm3/h] '
	  write(27,90) cversout, comline
          write(27,150) ' Iter    X[m]  F_burner  F_rising F_leaving' 
          j=0
          do i=ideb,ifin,ipas
            j=j+1
            nm3h=R*273.15*3600./(WMfum(i)*101325)
            write(27,113) j,lxmax-x(i),mb(i)*nm3h,dp(i)*nm3h,sor(i)*nm3h
          enddo
          close(unit=27)

        else if (ires(1).ge.1) then

	  filename=casename(1:lcasename)//'_'//moduname(code_max)(1:longcar(moduname(code_max)))//'.flow'
          open(unit=27,file=filename)
          comline = 'Mass Flowrate of flue gases [Kg/s] '
	  write(27,90) cversout, comline
          write(27,150) ' Iter  m_burner   m_degas   m_rec   m_flow   m_out'
          j=0
          do i=ideb,ifin,ipas
            j=j+1
            write(27,110) j,lxmax-x(i),mb(i),deg(i),dr(i),dp(i),sor(i)
          enddo
          close(unit=27)

        endif

!
! Ecriture temperature et flux globaux
!-------------------------------------------------------

        if(code_charge.eq.CODE_SMR)then

	  filename=casename(1:lcasename)//'_'//moduname(code_max)(1:longcar(moduname(code_max)))//'.temp'
          open(unit=27,file=filename)
          comline= 'COMBUSTION CHAMBER WALL TEMPERATURE PROFILES [K]'
	  write(27,90) cversout, comline
          write(27,150) ' Iter     X[m]  T_fluegas  T_refract    T_tubes  T_ceiling    T_floor'
          j=0
          do i=ideb,ifin,ipas
            j=j+1
            write(27,100) j,lxmax-x(i),tgi1(i),TpN1(i),tps1(i),TpW1,tpe1
          enddo
          close(unit=27)

	  filename=casename(1:lcasename)//'_'//moduname(code_max)(1:longcar(moduname(code_max)))//'.flux'
          open(unit=27,file=filename)
          comline= 'COMBUSTION CHAMBER WALL HEAT FLUX PROFILES [W/m2]'
	  write(27,90) cversout, comline
          write(27,150) ' Iter     X[m]  q_refract    q_tubes  q_ceiling    q_floor'
          j=0
          do i=ideb,ifin,ipas
            j=j+1
            write(27,100) j,lxmax-x(i),ftotpN(i),ftotps(i),ftotpW,ftotpE
          enddo
          close(unit=27)

        else

 	  filename=casename(1:lcasename)//'_'//moduname(code_max)(1:longcar(moduname(code_max)))//'.temp'
          open(unit=27,file=filename)
          comline = 'COMBUSTION CHAMBER WALL TEMPERATURE PROFILES [K]'
	  write(27,90) cversout, comline
          write(27,150) ' I    X(i)  T_gases  T_Crown  T_glass   T_inletw   T_outletW'
          j=0
          do i=ideb,ifin,ipas
            j=j+1
            write(27,100) j,lxmax-x(i),tgi1(i),TpN1(i),tps1(i),TpW1,tpe1
          enddo
          close(unit=27)

	  filename=casename(1:lcasename)//'_'//moduname(code_max)(1:longcar(moduname(code_max)))//'.flux'
	  open(unit=27,file=filename)
          comline= 'COMBUSTION CHAMBER WALL HEAT FLUX PROFILES [W/m2]'
	  write(27,90) cversout, comline
          write(27,150) ' I  X(i)  q_Crown  Q_glass   Q_inletw   Q_outletW'
          j=0
          do i=ideb,ifin,ipas
            j=j+1
            write(27,100) j,lxmax-x(i),ftotpN(i),ftotps(i),ftotpW,ftotpE
          enddo
          close(unit=27)
        endif

!
! Ecriture flux et puissance radiative
!-----------------------------------

        if(code_charge.eq.CODE_SMR)then

	  filename=casename(1:lcasename)//'_'//moduname(code_max)(1:longcar(moduname(code_max)))//'.flux_rad'
          open(unit=27,file=filename)
	  write(27,90) cversout, comline
          comline= 'WALL RADIATIVE HEAT FLUX PROFILES [W/m2]'
          write(27,150) ' Iter    X[m]  qr_refract   qr_tubes qr_ceiling   qr_floor'
          j=0
          do i=ideb,ifin,ipas
            j=j+1
            write(27,100) j,lxmax-x(i),fradpn(i),fradps(i),fradpw,fradpe
          enddo
          close(unit=27)

        else if (ires(1).ge.1) then
	  filename=casename(1:lcasename)//'_'//moduname(code_max)(1:longcar(moduname(code_max)))//'.flux_rad'
          open(unit=27,file=filename)
	  comline= 'WALL RADIATIVE HEAT FLUX PROFILES [W/m2]'
	  write(27,90) cversout, comline
          write(27,150) ' Iter  PUISSV    PUISS    DPUISS    FNORD    FSUD    FWEST    FEST'
          j=0
          do i=ideb,ifin,ipas
            j=j+1
            write(27,100) j,puissrad(i),puisstr(i),dprsdt(i),fradpn(i),fradps(i),fradpw,fradpe
          enddo
          close(unit=27)
        endif

!
! Ecriture flux convectif
!-----------------------------------

        if(code_charge.eq.CODE_SMR)then

	  filename=casename(1:lcasename)//'_'//moduname(code_max)(1:longcar(moduname(code_max)))//'.flux_cv'
          open(unit=27,file=filename)
          comline= 'WALL CONVECTIVE HEAT FLUX PROFILES [W/m2]'
	  write(27,90) cversout, comline
          write(27,150) ' Iter     X[m] qc_refract   qc_tubes qc_ceiling&
         &   qc_floor   hc_tubes[W/m2/K]'
          j=0
          do i=ideb,ifin,ipas
            j=j+1
            write(27,100) j,lxmax-x(i),fccpN(i),fccps(i),fccpW,fccpE,hccps(i)
          enddo
          close(unit=27)

        else if (ires(1).ge.1) then

	  filename=casename(1:lcasename)//'_'//moduname(code_max)(1:longcar(moduname(code_max)))//'.flux_cv'
          open(unit=27,file=filename)
	  comline= 'WALL CONVECTIVE HEAT FLUX PROFILES [W/m2]'
	  write(27,90) cversout, comline
          write(27,150) ' Iter  FNORD    FSUD    FWEST    FEST'
          j=0
          do i=ideb,ifin,ipas
            j=j+1
            write(27,100) j,fccpN(i),fccps(i),fccpW,fccpE
          enddo
          close(unit=27)

        endif

!
! Ecriture composition et proprietes du gaz
!-----------------------------------

        if(code_charge.eq.CODE_SMR)then

	  filename=casename(1:lcasename)//'_'//moduname(code_max)(1:longcar(moduname(code_max)))//'.gas'
          open(unit=27,file=filename)
	  comline= 'FLUE GASES MOLAR FRACTIONS AND PHYSICAL PROPERTIES'
	  write(27,90) cversout, comline
          write(27,150) ' Iter  X[m]  N2           O2           CO2          &
          &H2O          AR           Fuel         Rho[kg/m3]   &
          &Vis[Pa.s]    Cp[J/kg/K]   Hgaz[J/kg]'
          j=0
          do i=ideb,ifin,ipas
            j=j+1
            write(27,303) j,lxmax-x(i),yn(i)*WMfum(i)/MN2,yo(i)*WMfum(i)/MO2&
          &,yco2(i)*WMfum(i)/MCO2,yh2o(i)*WMfum(i)/MH2O,yar(i)*WMfum(i)/MAr&
          &,yf(i)*WMfum(i)/WM,rhogaz(i),visgaz(i),cpgaz(i),hgaz(i)
!cth PAS DE CONVERSION MOLAIRE ???          &,yf(i),rhogaz(i),visgaz(i),cpgaz(i),hgaz(i)
          enddo
          close(unit=27)

          j=0
          do i=ideb,ifin,ipas
            j=j+1
            sommas=yn(i)+yo(i)+yco2(i)+yh2o(i)+yar(i)+yf(i)
            sommol= &
          &   + yn(i)*WMfum(i)/MN2 + yo(i)*WMfum(i)/MO2 &
          &   + yco2(i)*WMfum(i)/MCO2 + yh2o(i)*WMfum(i)/MH2O &
          &   + yar(i)*WMfum(i)/MAr + yf(i)*WMfum(i)/WM
            diffsom=max((1.0-sommol),(1.0-sommas))
            if (abs(diffsom).gt.1.0e-5) then
              print *,' WARNING : FRACTION : ',i,j,sommas,sommol,diffsom
            endif
            
          enddo

        else if (ires(1).ge.1) then
         
	  filename=casename(1:lcasename)//'_'//moduname(code_max)(1:longcar(moduname(code_max)))//'.gas'
          open(unit=27,file=filename)
          comline= 'FLUE GASES PHYSICAL PROPERTIES'
	  write(27,90) cversout, comline
          write(27,150) ' Iter   Rho   Vis   Cp   Hgaz'
          j=0
          do i=ideb,ifin,ipas
            j=j+1
            write(27,300) j,rhogaz(i),visgaz(i),cpgaz(i),hgaz(i)
          enddo
          close(unit=27)

        endif
 90     format('# ',a8,' # ',a80) 
100     format(i4,15(1x,f10.2))
101     format(15(1x,f10.2))
110     format(i4,15(1x,f9.5))
113     format(i4,15(1x,f9.2))
150     format(1x,a)
200     format(i4,15(1x,f9.4))
300     format(i4,15(1x,e12.5))
303     format(i4,1x,f7.2,15(1x,e12.5))

!
! Fin du programme 
!-----------------
        end
