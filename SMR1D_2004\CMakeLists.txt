# SMR1D_2004 CMakeLists.txt
# SMR 2004 module executable

# Find all source files
file(GLOB SMR2004_SOURCES "*.f90")
file(GLOB SMR2004_F77_SOURCES "*.f")

# Main executable sources
set(SMR2004_MAIN_SOURCES
    main_smr.f90
    modules_smr.f90
    # Old files
    SMRcalcul.f90
    SMRannexe.f90
    chamel2.f90
    dopk.f90
    hmel2.f90
    properties.f90
    # New files
    Absorp.f90
    CndMix.f90
    DetCond.f90
    DetCp.f90
    DetDm.f90
    DetVis.f90
    EmissTot.f90
    VisMix.f90
    init_tubes.f90
    mesh_tubes_settings.f90
    write_data_tubes.f90
    write_restart_tubes.f90
    residu_tubes.f90
    solve_tubes.f90
    set_temp_comb.f90
    set_plot_values.f90
    pgs_htas.f90
    hougen_watson.f90
    read_par_tubes.f90
    read_geo_tubes.f90
    read_phy_tubes.f90
    read_bc_tubes.f90
    read_bc_comb_tubes.f90
    test_props.f90
    put_tagcvs_tubes.f90
    write_bal_tubes.f90
)

# Test executable sources
set(SMR2004_TEST_SOURCES
    main_test_props.f90
    modules_smr.f90
    # Include all other sources for test
    ${SMR2004_MAIN_SOURCES}
)

# Create main executable
add_executable(smr1d_2004 ${SMR2004_MAIN_SOURCES})

# Create test executable
add_executable(smrprops_2004 ${SMR2004_TEST_SOURCES})

# Set module directory
set_target_properties(smr1d_2004 smrprops_2004 PROPERTIES
    Fortran_MODULE_DIRECTORY ${CMAKE_Fortran_MODULE_DIRECTORY}
)

# Include directories
target_include_directories(smr1d_2004 PRIVATE
    ${CMAKE_Fortran_MODULE_DIRECTORY}
)

target_include_directories(smrprops_2004 PRIVATE
    ${CMAKE_Fortran_MODULE_DIRECTORY}
)

# Compiler definitions
target_compile_definitions(smr1d_2004 PRIVATE
    SMRCHARGE
    $<$<BOOL:${WIN32}>:WIN32>
    $<$<BOOL:${WIN32}>:_WINDOWS>
)

target_compile_definitions(smrprops_2004 PRIVATE
    SMRCHARGE
    $<$<BOOL:${WIN32}>:WIN32>
    $<$<BOOL:${WIN32}>:_WINDOWS>
)

# Link dependencies
target_link_libraries(smr1d_2004
    athena1d
    smrprops
    usbkey
    modules
)

target_link_libraries(smrprops_2004
    athena1d
    smrprops
    usbkey
    modules
)

# Fortran compiler flags for fixed format files
if(WIN32)
    set_source_files_properties(${SMR2004_F77_SOURCES} PROPERTIES
        Fortran_FORMAT FIXED
        COMPILE_FLAGS "/extend_source:132"
    )
else()
    set_source_files_properties(${SMR2004_F77_SOURCES} PROPERTIES
        Fortran_FORMAT FIXED
        COMPILE_FLAGS "-FI -extend-source"
    )
endif()

message(STATUS "SMR1D_2004: Main sources: ${SMR2004_MAIN_SOURCES}")
message(STATUS "SMR1D_2004: F77 sources: ${SMR2004_F77_SOURCES}") 