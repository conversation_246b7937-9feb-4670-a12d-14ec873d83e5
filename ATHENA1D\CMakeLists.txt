# ATHENA1D CMakeLists.txt
# Furnace module - creates static libraries

# Find all source files
file(GLOB ATHENA_SOURCES "*.f90")
file(GLOB ATHENA_F77_SOURCES "*.f")

# Separate sources for different libraries
set(ATHENA_MAIN_SOURCES
    arguments.f90
    cleanline.f90
    compute_convec_comb.f90
    compute_fuel.f90
    compute_gas_temp.f90
    compute_line_intersection.f90
    compute_modpre.f90
    compute_radiation_interp.f90
    compute_rad_fluxes_rays.f90
    compute_rad_pwr_rays.f90
    compute_wsgg.f90
    decode_line_int.f90
    decode_line_real.f90
    decode_string.f90
    extract_version.f90
    fct_conductivity.f90
    fct_density.f90
    fct_viscosity.f90
    get_code_infile.f90
    get_code_inline.f90
    get_compo_comb.f90
    get_dimmesh_comb.f90
    get_dimzone_comb.f90
    get_err_coupling.f90
    get_exhaust_comb.f90
    get_flow_comb.f90
    get_fume_comb.f90
    get_geo_comb.f90
    get_index_code.f90
    get_interf_field.f90
    get_mesh_comb.f90
    get_name_inline.f90
    get_phy_comb.f90
    get_pos_index.f90
    get_puissg_comb.f90
    get_puissz_comb.f90
    get_temp_comb.f90
    get_zone_comb.f90
    init_comb.f90
    init_coupling.f90
    init_interf.f90
    init_temp_comb.f90
    interp_interf_flux.f90
    interp_interf_temp.f90
    set_boundary_condition.f90
    set_dim_meshes.f90
    set_angular_meshes.f90
    set_burners_inlet.f90
    set_side_outlets.f90
    set_reversed_gas.f90
    solve_mass_balance.f90
    solve_radiation.f90
    solve_comb.f90
    solve_init_comb.f90
)

set(ATHENA_STD_SOURCES
    chamel.f90
    hmel.f90
)

set(ATHENA_SMR_SOURCES
    chamel.f90
    hmel.f90
)

# Create main ATHENA1D library
add_library(athena1d STATIC ${ATHENA_MAIN_SOURCES})

# Create STD properties library
add_library(stdprops STATIC ${ATHENA_STD_SOURCES})

# Create SMR properties library  
add_library(smrprops STATIC ${ATHENA_SMR_SOURCES})

# Set module directory for all libraries
set_target_properties(athena1d stdprops smrprops PROPERTIES
    Fortran_MODULE_DIRECTORY ${CMAKE_Fortran_MODULE_DIRECTORY}
)

# Include directories
target_include_directories(athena1d PUBLIC
    ${CMAKE_Fortran_MODULE_DIRECTORY}
)

target_include_directories(stdprops PUBLIC
    ${CMAKE_Fortran_MODULE_DIRECTORY}
)

target_include_directories(smrprops PUBLIC
    ${CMAKE_Fortran_MODULE_DIRECTORY}
)

# Compiler definitions
target_compile_definitions(stdprops PRIVATE
    $<$<BOOL:${WIN32}>:WIN32>
    $<$<BOOL:${WIN32}>:_WINDOWS>
)

target_compile_definitions(smrprops PRIVATE
    SMRCHARGE
    $<$<BOOL:${WIN32}>:WIN32>
    $<$<BOOL:${WIN32}>:_WINDOWS>
)

# Link dependencies
target_link_libraries(athena1d modules)
target_link_libraries(stdprops modules)
target_link_libraries(smrprops modules)

message(STATUS "ATHENA1D: Main sources: ${ATHENA_MAIN_SOURCES}")
message(STATUS "ATHENA1D: STD sources: ${ATHENA_STD_SOURCES}")
message(STATUS "ATHENA1D: SMR sources: ${ATHENA_SMR_SOURCES}") 