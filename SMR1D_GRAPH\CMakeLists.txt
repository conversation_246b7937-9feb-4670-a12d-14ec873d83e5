# SMR1D_GRAPH CMakeLists.txt
# SMR with graphics module executable

# Find all source files
file(GLOB SMRGRAPH_SOURCES "*.f90")
file(GLOB SMRGRAPH_F77_SOURCES "*.f")

# Main executable sources
set(SMRGRAPH_MAIN_SOURCES
    main_smr.f90
    modules_smr.f90
    # Old files
    bindif.f
    dffs.f
    dfopr.f
    etacol.f
    heatef.f
    iniparp.f
    jcobi.f
    loco.f
    molfr.f
    molfr1.f
    outputip.f
    outputsu.f
    parameter.f
    radau.f
    reactor.f
    result.f
    rgkt.f
    rightm.f
    speed.f
    smr_gent_tubes.f
    thercon.f
    twinve.f
    visco.f
    vites.f
    # New files
    bilans_smr.f90
    deteta_tubes.f90
    detflux_tubes.f90
    detlam_tubes.f90
    det_temp_2d.f90
    init_data_al.f90
    init_tubes.f90
    init_geo_tubes.f90
    init_gent_tubes.f90
    init_cst_keq.f90
    init_props_tubes.f90
    compute_keq_p.f90
    compute_keq_t.f90
    approche_reac.f90
    mesh_settings_tubes.f90
    read_par_tubes.f90
    read_geo_tubes.f90
    read_phy_tubes.f90
    read_bc_tubes.f90
    read_flux_tubes.f90
    integ_flux_tubes.f90
    repart_temp_tubes.f90
    residu_tubes.f90
    solve_tubes.f90
    write_data_tubes.f90
    write_example_tubes.f90
    write_restart_tubes.f90
    test_props.f90
    put_tagcvs_tubes.f90
    init_graph_smr.f90
    draw_graph_smr.f90
    write_bal_tubes.f90
)

# Create executable
add_executable(smr1d_graph ${SMRGRAPH_MAIN_SOURCES})

# Set module directory
set_target_properties(smr1d_graph PROPERTIES
    Fortran_MODULE_DIRECTORY ${CMAKE_Fortran_MODULE_DIRECTORY}
)

# Include directories
target_include_directories(smr1d_graph PRIVATE
    ${CMAKE_Fortran_MODULE_DIRECTORY}
    ${CMAKE_SOURCE_DIR}/GRAPH
)

# Compiler definitions
target_compile_definitions(smr1d_graph PRIVATE
    SMRCHARGE
    $<$<BOOL:${WIN32}>:WIN32>
    $<$<BOOL:${WIN32}>:_WINDOWS>
)

# Link dependencies
target_link_libraries(smr1d_graph
    athena1d
    stdprops
    usbkey
    graph
    modules
)

# Fortran compiler flags for fixed format files
if(WIN32)
    set_source_files_properties(${SMRGRAPH_F77_SOURCES} PROPERTIES
        Fortran_FORMAT FIXED
        COMPILE_FLAGS "/extend_source:132"
    )
else()
    set_source_files_properties(${SMRGRAPH_F77_SOURCES} PROPERTIES
        Fortran_FORMAT FIXED
        COMPILE_FLAGS "-FI -extend-source"
    )
endif()

message(STATUS "SMR1D_GRAPH: Main sources: ${SMRGRAPH_MAIN_SOURCES}")
message(STATUS "SMR1D_GRAPH: F77 sources: ${SMRGRAPH_F77_SOURCES}") 