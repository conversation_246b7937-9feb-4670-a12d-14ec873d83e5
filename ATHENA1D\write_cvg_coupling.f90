
      SUBROUTINE write_cvg_coupling(itercoupt,errtsom,errtmax,indtmax,errfsom,errfmax,indfmax,&
                                  &tfsortie,tvsortie)
      USE modshared

      IMPLICIT NONE

      INTEGER :: itercoupt
      REAL :: errtsom,errtmax,tvsortie
      INTEGER :: indtmax
      REAL :: errfsom,errfmax,tfsortie
      INTEGER :: indfmax
      CHARACTER*130 filename

      filename=casename(1:lcasename)//'_coupling.rcvg'
      OPEN(unit=14,file=filename,POSITION = 'APPEND')
      WRITE(14,100)itercoupt,errtsom,errtmax,float(indtmax)/100.,&
                            &errfsom,errfmax,float(indfmax)/100.,&
                            &tfsortie,tvsortie
100   format(i6,10(1x,E11.4))
      CLOSE(14)

      END
