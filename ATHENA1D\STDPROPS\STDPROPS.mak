# Microsoft Developer Studio Generated NMAKE File, Based on STDPROPS.dsp
!IF "$(CFG)" == ""
CFG=STDPROPS - Win32 Release
!MESSAGE No configuration specified. Defaulting to STDPROPS - Win32 Release.
!ENDIF 

!IF "$(CFG)" != "STDPROPS - Win32 Release" && "$(CFG)" != "STDPROPS - Win32 Debug"
!MESSAGE Invalid configuration "$(CFG)" specified.
!MESSAGE You can specify a configuration when running NMAKE
!MESSAGE by defining the macro CFG on the command line. For example:
!MESSAGE 
!MESSAGE NMAKE /f "STDPROPS.mak" CFG="STDPROPS - Win32 Release"
!MESSAGE 
!MESSAGE Possible choices for configuration are:
!MESSAGE 
!MESSAGE "STDPROPS - Win32 Release" (based on "Win32 (x86) Static Library")
!MESSAGE "STDPROPS - Win32 Debug" (based on "Win32 (x86) Static Library")
!MESSAGE 
!ERROR An invalid configuration is specified.
!ENDIF 

!IF "$(OS)" == "Windows_NT"
NULL=
!ELSE 
NULL=nul
!ENDIF 

CPP=cl.exe
F90=df.exe
RSC=rc.exe

!IF  "$(CFG)" == "STDPROPS - Win32 Release"

OUTDIR=.\../../LIBRARIES/WINDOWS
INTDIR=.\Release
# Begin Custom Macros
OutDir=.\../../LIBRARIES/WINDOWS
# End Custom Macros

ALL : "$(OUTDIR)\STDPROPS.lib"


CLEAN :
	-@erase "$(INTDIR)\chamel.obj"
	-@erase "$(INTDIR)\hmel.obj"
	-@erase "$(OUTDIR)\STDPROPS.lib"

"$(OUTDIR)" :
    if not exist "$(OUTDIR)/$(NULL)" mkdir "$(OUTDIR)"

"$(INTDIR)" :
    if not exist "$(INTDIR)/$(NULL)" mkdir "$(INTDIR)"

F90_PROJ=/alignment:dcommons /compile_only /fpp /include:"../../MODULES/WINDOWS" /libs:qwin /nologo /real_size:64 /traceback /warn:nofileopt /module:"../../MODULES/WINDOWS" /object:"Release/" 
F90_OBJS=.\Release/
BSC32=bscmake.exe
BSC32_FLAGS=/nologo /o"$(OUTDIR)\STDPROPS.bsc" 
BSC32_SBRS= \
	
LIB32=link.exe -lib
LIB32_FLAGS=/nologo /out:"$(OUTDIR)\STDPROPS.lib" 
LIB32_OBJS= \
	"$(INTDIR)\chamel.obj" \
	"$(INTDIR)\hmel.obj"

"$(OUTDIR)\STDPROPS.lib" : "$(OUTDIR)" $(DEF_FILE) $(LIB32_OBJS)
    $(LIB32) @<<
  $(LIB32_FLAGS) $(DEF_FLAGS) $(LIB32_OBJS)
<<

!ELSEIF  "$(CFG)" == "STDPROPS - Win32 Debug"

OUTDIR=.\../../DEBUG/EXEC/WINDOWS
INTDIR=.\Debug

ALL : "..\..\DEBUG\LIBRARIES\WINDOWS\STDPROPS_debug.lib"


CLEAN :
	-@erase "$(INTDIR)\chamel.obj"
	-@erase "$(INTDIR)\hmel.obj"
	-@erase "..\..\DEBUG\LIBRARIES\WINDOWS\STDPROPS_debug.lib"

"$(OUTDIR)" :
    if not exist "$(OUTDIR)/$(NULL)" mkdir "$(OUTDIR)"

"$(INTDIR)" :
    if not exist "$(INTDIR)/$(NULL)" mkdir "$(INTDIR)"

F90_PROJ=/alignment:dcommons /compile_only /fpp /include:"../../MODULES/WINDOWS" /libs:qwin /nologo /real_size:64 /traceback /warn:nofileopt /module:"../../MODULES/WINDOWS" /object:"Debug/" 
F90_OBJS=.\Debug/
BSC32=bscmake.exe
BSC32_FLAGS=/nologo /o"../../DEBUG/LIBRARIES/WINDOWS/STDPROPS_debug.bsc" 
BSC32_SBRS= \
	
LIB32=link.exe -lib
LIB32_FLAGS=/nologo /out:"../../DEBUG/LIBRARIES/WINDOWS/STDPROPS_debug.lib" 
LIB32_OBJS= \
	"$(INTDIR)\chamel.obj" \
	"$(INTDIR)\hmel.obj"

"..\..\DEBUG\LIBRARIES\WINDOWS\STDPROPS_debug.lib" : "$(OUTDIR)" $(DEF_FILE) $(LIB32_OBJS)
    $(LIB32) @<<
  $(LIB32_FLAGS) $(DEF_FLAGS) $(LIB32_OBJS)
<<

!ENDIF 

CPP_PROJ=/nologo /ML /W3 /GX /O2 /D "WIN32" /D "NDEBUG" /D "_WINDOWS" /D "_MBCS" /Fp"$(INTDIR)\STDPROPS.pch" /YX /Fo"$(INTDIR)\\" /Fd"$(INTDIR)\\" /FD /c 

.c{$(INTDIR)}.obj::
   $(CPP) @<<
   $(CPP_PROJ) $< 
<<

.cpp{$(INTDIR)}.obj::
   $(CPP) @<<
   $(CPP_PROJ) $< 
<<

.cxx{$(INTDIR)}.obj::
   $(CPP) @<<
   $(CPP_PROJ) $< 
<<

.c{$(INTDIR)}.sbr::
   $(CPP) @<<
   $(CPP_PROJ) $< 
<<

.cpp{$(INTDIR)}.sbr::
   $(CPP) @<<
   $(CPP_PROJ) $< 
<<

.cxx{$(INTDIR)}.sbr::
   $(CPP) @<<
   $(CPP_PROJ) $< 
<<

.SUFFIXES: .fpp

.for{$(F90_OBJS)}.obj:
   $(F90) $(F90_PROJ) $<  

.f{$(F90_OBJS)}.obj:
   $(F90) $(F90_PROJ) $<  

.f90{$(F90_OBJS)}.obj:
   $(F90) $(F90_PROJ) $<  

.fpp{$(F90_OBJS)}.obj:
   $(F90) $(F90_PROJ) $<  


!IF "$(NO_EXTERNAL_DEPS)" != "1"
!IF EXISTS("STDPROPS.dep")
!INCLUDE "STDPROPS.dep"
!ELSE 
!MESSAGE Warning: cannot find "STDPROPS.dep"
!ENDIF 
!ENDIF 


!IF "$(CFG)" == "STDPROPS - Win32 Release" || "$(CFG)" == "STDPROPS - Win32 Debug"
SOURCE=..\chamel.f90

"$(INTDIR)\chamel.obj" : $(SOURCE) "$(INTDIR)"
	$(F90) $(F90_PROJ) $(SOURCE)


SOURCE=..\hmel.f90

"$(INTDIR)\hmel.obj" : $(SOURCE) "$(INTDIR)"
	$(F90) $(F90_PROJ) $(SOURCE)



!ENDIF 

