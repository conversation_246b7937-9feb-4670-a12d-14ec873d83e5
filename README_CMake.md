# CALCIFER - CMake Build System

## Tổng quan

Dự án CALCIFER đã được chuyển đổi từ Makefile sang CMake để hỗ trợ cross-platform compilation, đặc biệt là Windows 11 và Linux.

## Yêu cầu hệ thống

### Windows 11
1. **Intel OneAPI HPC Toolkit** (bao gồm Intel Fortran Compiler)
   - T<PERSON>i từ: https://www.intel.com/content/www/us/en/developer/tools/oneapi/hpc-toolkit-download.html
   - Phiên bản khuyến nghị: 2023.2.0 hoặc mới hơn

2. **CMake**
   - Tải từ: https://cmake.org/download/
   - Hoặc cài đặt qua winget: `winget install Kitware.CMake`

3. **Visual Studio Build Tools** (tùy chọn)
   - Nếu muốn sử dụng MSVC linker
   - Cài đặt qua winget: `winget install Microsoft.VisualStudio.2022.BuildTools`

### Linux (OpenSUSE)
1. **Intel OneAPI HPC Toolkit**
   - T<PERSON><PERSON> từ: https://www.intel.com/content/www/us/en/developer/tools/oneapi/hpc-toolkit-download.html
   - Hoặc cài đặt qua package manager

2. **CMake**
   ```bash
   sudo zypper install cmake
   ```

## Cách build

### Windows 11
```cmd
# Sử dụng script tự động
build.bat

# Hoặc build thủ công
mkdir build
cd build
cmake .. -G "NMake Makefiles" -DCMAKE_BUILD_TYPE=Release
cmake --build . --config Release
```

### Linux
```bash
# Sử dụng script tự động
chmod +x build.sh
./build.sh

# Hoặc build thủ công
mkdir build
cd build
cmake .. -DCMAKE_BUILD_TYPE=Release
cmake --build . --config Release
```

## Cấu trúc output

Sau khi build thành công, các file sẽ được tạo trong thư mục `build/`:

```
build/
├── EXEC/
│   ├── WINDOWS/          # Windows executables
│   └── Linux_x86_64/     # Linux executables
├── LIBRARIES/
│   ├── WINDOWS/          # Windows static libraries
│   └── Linux_x86_64/     # Linux static libraries
└── MODULES/
    ├── WINDOWS/          # Windows Fortran modules
    └── Linux_x86_64/     # Linux Fortran modules
```

## Các executable được tạo

- `glass1d.exe` - Module thủy tinh
- `smr1d.exe` - Module SMR (Steam Methane Reforming)
- `smr1d_2004.exe` - Module SMR phiên bản 2004
- `smr1d_graph.exe` - Module SMR với giao diện đồ họa
- `smrprops.exe` - Test properties cho SMR

## Các thư viện được tạo

- `libathena1d.a` - Thư viện lò nung chính
- `libstdprops.a` - Thư viện properties chuẩn
- `libsmrprops.a` - Thư viện properties SMR
- `libusbkey.a` - Thư viện USB key
- `libgraph.a` - Thư viện đồ họa
- `libmodules.a` - Thư viện modules chung

## Cấu hình CMake

### Build types
- `Release` - Build tối ưu hóa (mặc định)
- `Debug` - Build debug với thông tin debug

### Compiler flags

#### Windows (Intel Fortran)
- `/real_size:64` - Sử dụng REAL*8
- `/traceback` - Thêm thông tin traceback
- `/warn:nofileopt` - Tắt warning về file optimization
- `/O2` - Tối ưu hóa level 2
- `/fast` - Tối ưu hóa nhanh

#### Linux (Intel Fortran)
- `-auto` - Tự động allocation
- `-O2` - Tối ưu hóa level 2
- `-r8` - Sử dụng REAL*8
- `-g` - Thêm debug info (Debug build)

## Troubleshooting

### Lỗi thường gặp

1. **Intel Fortran Compiler không tìm thấy**
   ```
   ERROR: Intel Fortran Compiler (ifort) not found!
   ```
   **Giải pháp**: Cài đặt Intel OneAPI HPC Toolkit và thêm vào PATH

2. **CMake không tìm thấy**
   ```
   ERROR: CMake not found!
   ```
   **Giải pháp**: Cài đặt CMake và thêm vào PATH

3. **Lỗi module dependencies**
   ```
   Fatal Error: Can't open module file 'modxxx.mod'
   ```
   **Giải pháp**: Đảm bảo thứ tự build đúng (modules trước, sau đó libraries, cuối cùng executables)

4. **Lỗi linking trên Windows**
   ```
   LINK : fatal error LNK1104: cannot open file 'xxx.lib'
   ```
   **Giải pháp**: Đảm bảo tất cả dependencies đã được build thành công

### Debug build

Để build với debug info:
```cmd
# Windows
cmake .. -G "NMake Makefiles" -DCMAKE_BUILD_TYPE=Debug
cmake --build . --config Debug

# Linux
cmake .. -DCMAKE_BUILD_TYPE=Debug
cmake --build . --config Debug
```

## Migration từ Makefile

### Thay đổi chính
1. **Build system**: Makefile → CMake
2. **Compiler**: Intel Fortran (ifort) cho cả Windows và Linux
3. **Module handling**: Tự động quản lý dependencies
4. **Cross-platform**: Hỗ trợ Windows và Linux từ cùng source code

### Tương thích
- Tất cả source code Fortran giữ nguyên
- Các module và dependencies được bảo toàn
- Output files có cùng tên và cấu trúc

## Hỗ trợ

Nếu gặp vấn đề, vui lòng:
1. Kiểm tra yêu cầu hệ thống
2. Chạy build với verbose output: `cmake --build . --verbose`
3. Kiểm tra log lỗi chi tiết
4. Liên hệ team phát triển với thông tin lỗi đầy đủ 