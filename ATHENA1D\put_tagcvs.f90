      subroutine put_tagcvs(iargcode,cargtag,cargrev,cargvers)
!****************************************************************************
!
! Envoi des infos de version : TAG et REV CVS
! Retour de la version 
! Le four et la charge doivent appeler cette routine avant init_coupling
!
!****************************************************************************

      USE modshared

      implicit none

!
! Declaration des variables
!--------------------------
      INTEGER :: iargcode
      CHARACTER*(*) cargtag
      CHARACTER*(*) cargrev
      CHARACTER*(*) cargvers

      CHARACTER*10 clocfull
      INTEGER :: longcar
      INTEGER :: longarg

!cth Affectation
      longarg=longcar(cargtag)
      ALLTAG(iargcode)(1:longarg)=cargtag(1:longarg)
      longarg=longcar(cargrev)
      ALLREVISION(iargcode)(1:longarg)=cargrev(1:longarg)

      CALL extract_version(cargtag,cargvers,clocfull)

      return

      end
