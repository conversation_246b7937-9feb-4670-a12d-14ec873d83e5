cmake_minimum_required(VERSION 3.16)
project(CALCIFER VERSION 1.0.0 LANGUAGES Fortran C)

# Set Fortran standard
set(CMAKE_Fortran_STANDARD 90)
set(CMAKE_Fortran_STANDARD_REQUIRED ON)

# Platform detection
if(WIN32)
    set(PLA<PERSON>ORM "WINDOWS")
    set(CMAKE_Fortran_COMPILER_ID "Intel")
    # Intel Fortran compiler flags for Windows
    set(CMAKE_Fortran_FLAGS "${CMAKE_Fortran_FLAGS} /real_size:64 /traceback /warn:nofileopt")
    set(CMAKE_Fortran_FLAGS_DEBUG "${CMAKE_Fortran_FLAGS_DEBUG} /debug:full /d_lines /warn:declarations")
    set(CMAKE_Fortran_FLAGS_RELEASE "${CMAKE_Fortran_FLAGS_RELEASE} /O2 /fast")
else()
    set(PLATFORM "Linux_x86_64")
    set(CMAKE_Fortran_FLAGS "${CMAKE_Fortran_FLAGS} -auto -O2 -r8")
    set(CMAKE_Fortran_FLAGS_DEBUG "${CMAKE_Fortran_FLAGS_DEBUG} -g")
    set(CMAKE_Fortran_FLAGS_RELEASE "${CMAKE_Fortran_FLAGS_RELEASE} -O2")
endif()

# Create output directories
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/EXEC/${PLATFORM})
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/LIBRARIES/${PLATFORM})
set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/LIBRARIES/${PLATFORM})

# Module output directory
set(CMAKE_Fortran_MODULE_DIRECTORY ${CMAKE_BINARY_DIR}/MODULES/${PLATFORM})

# Add subdirectories
add_subdirectory(MODULES)
add_subdirectory(ATHENA1D)
add_subdirectory(USBKEY)
add_subdirectory(GRAPH)
add_subdirectory(GLASS1D)
add_subdirectory(SMR1D)
add_subdirectory(SMR1D_2004)
add_subdirectory(SMR1D_GRAPH)

# Set global include directories
include_directories(${CMAKE_Fortran_MODULE_DIRECTORY})

# Global compiler definitions
if(WIN32)
    add_compile_definitions(WIN32 _WINDOWS)
else()
    add_compile_definitions(LINUX)
endif()

# Print configuration info
message(STATUS "Platform: ${PLATFORM}")
message(STATUS "Fortran Compiler: ${CMAKE_Fortran_COMPILER}")
message(STATUS "Fortran Flags: ${CMAKE_Fortran_FLAGS}")
message(STATUS "Module Directory: ${CMAKE_Fortran_MODULE_DIRECTORY}") 