	SUBROUTINE read_bcd_file(filename,nx,x,temp)

! ***********************************************************************
!
! Lecture et interpolation d un champ de temperature fichier .ldt
!
! ***********************************************************************
        
        USE modshared

	IMPLICIT NONE

        CHARACTER*(*) filename
	INTEGER :: nx
	REAL, dimension(nx) :: x,temp

	INTEGER :: i
        INTEGER :: lname
        CHARACTER*130 line
        INTEGER :: nxlu
        REAL, allocatable,dimension(:) :: xpos,valtemp
        INTEGER :: ierror
!cth Pour sens du profil (charge ou four)
        INTEGER :: inverse
        INTEGER :: ikelvin
        CHARACTER*7 ,PARAMETER :: cinverse='REVERSE'
        CHARACTER*6 ,PARAMETER :: ckelvin='KELVIN'
        REAL :: coordmax

        INTEGER :: longcar

          lname=longcar(filename)
          open(unit=99,file=filename,STATUS='OLD',ERR=9997)

          read(99,*,ERR=9998,END=9999) nxlu
          read(99,100,ERR=9998,END=9999) line
100       format(a)

          allocate(xpos(nxlu),stat=ierror)
          if (ierror.ne.0) go to 9000
          allocate(valtemp(nxlu),stat=ierror)
          if (ierror.ne.0) go to 9000

!cth Pour decalage celsius
          ikelvin=index(line,ckelvin)
!cth Pour sens du profil
          inverse=index(line,cinverse)

          if (inverse.eq.0) then
            do i=1,nxlu
              read(99,*,ERR=9998,END=9999) xpos(i),valtemp(i)
            enddo
          else
            do i=nxlu,1,-1
              read(99,*,ERR=9998,END=9999) xpos(i),valtemp(i)
            enddo
            coordmax=xpos(1)
            do i=1,nxlu
              xpos(i)=coordmax-xpos(i)
            enddo
          endif

          close(unit=99)

          if (ikelvin.eq.0) then
            do i=1,nxlu
              valtemp(i)=valtemp(i)+273.15
            enddo
          endif

!cth Interpolation sur maillage 
          call value_mesh_interpolation(nxlu,xpos,valtemp,nx,x,temp)

          deallocate(xpos)
          deallocate(valtemp)

        return

9000    CONTINUE
!cth Erreur allocation
        PRINT *,' READ_BCD_FILE : '
        PRINT *,' Erreur allocation : ',ierror
        print *,' Nombre de valeurs : ',nxlu
        STOP ' MIEUX VAUT S ARRETER '

9997    CONTINUE
!cth Erreur ouverture fichier
        PRINT *,' READ_BCD_FILE : '
        PRINT *,' Erreur ouverture fichier : ',filename(1:lname)
        STOP ' MIEUX VAUT S ARRETER '

9998    CONTINUE
!cth Erreur lecture
        PRINT *,' READ_BCD_FILE : '
        PRINT *,' Erreur lecture sur : ',filename(1:lname)
        STOP ' MIEUX VAUT S ARRETER '

9999    CONTINUE
!cth Fin de fichier
        PRINT *,' READ_BCD_FILE : '
        PRINT *,' Fin de fichier sur : ',filename(1:lname)
        STOP ' MIEUX VAUT S ARRETER '

	END
