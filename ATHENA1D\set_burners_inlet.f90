	SUBROUTINE set_burners_inlet(nbzonesmax,NX,mb,Tbr,&
	&  yN,yH2O,yO,yCO2,yAr,yF,GRIDX,WM1,dimen,&
	&  qzone,nbzones,longzones,&
	&  indmaille,nbtranchzone,mbruleurz,mcomb,&
	&  hbr,puissbrul,puissbruz,puisspci,&
	&  nposbrul,brulzone,&
	&  qdefent,qdefsor,&
	&  ncombesp,noxyesp,&
	&  maxoxydant,nboxydant,&
	&  xcombmol,xcombmas,xoxymol,xsoxymol,xoxymas,xsoxymas,&
	&  humid,thumid,oxymas,oxysmas,&
	&  dvcombreal,dvcombglo,&
	&  dvoxyreal,dvoxyglo,dvsoxyreal,dvsoxyglo,exoxyglo,&
	&  dvfumeereal,dvfumeeglo,&
	&  qoxydant,qhumid,qoxymas,fracomb_grid,longzone_grid)
!**********************************************************************
!
!	NAME		:	set_burners_inlet.f90
!
!	AUTHOR	:	Fouad AMMOURI 19/01/2000
!				Olivier LOUEDIN 18/10/2000
!				Jerome ANFRAY 07/03/02
!				Youssef Joumani 04/03
!
!	FUNCTION	:	mb, TBR,yN,yH2O,yO,yCO2 table calculation for each zone
!
!	INPUTS	:
!
!	OUTPUTS	:
!
!	NOTES		:	Not melange possible
!				Dynamic routine
!
!**********************************************************************
!
	USE modespece
	USE modlim
	USE modshared

	IMPLICIT NONE
!
!Declaration des variables
!-------------------------
	CHARACTER*2 version
	CHARACTER*130 filename
	logical :: qzone
	INTEGER longcar
	INTEGER NX,nbzonesmax
	INTEGER nbzones
	INTEGER, DIMENSION(nbzonesmax) :: zonebruleur,nbbruleur
	INTEGER, DIMENSION(nbzonesmax) :: nbtranchzone
	INTEGER, DIMENSION(nbzonesmax) :: indmaille
	REAL, DIMENSION (NX) ::  dimen
	REAL, DIMENSION (nbzonesmax) :: Longzones
	REAL, DIMENSION(NX+1) :: GRIDX

	REAL,DIMENSION(NX):: TBR,yN,yH2O,yO,yCO2,yAr,yF,WM1
	REAL,DIMENSION(NX):: mb,Tmel
	REAL,DIMENSION(NX):: hbr
	REAL :: puissbrul

	LOGICAL, DIMENSION(nbzonesmax) :: qdefent,qdefsor
	INTEGER :: nposbrul
	INTEGER :: brulzone(*)
	INTEGER :: ncombesp,noxyesp
	INTEGER :: maxoxydant,nboxydant
	REAL :: xcombmol(ncodesp),xcombmas(ncodesp)
	REAL :: xoxymol(ncodesp,maxoxydant),xsoxymol(ncodesp,maxoxydant)
	REAL :: xoxymas(ncodesp,maxoxydant),xsoxymas(ncodesp,maxoxydant)
	REAL :: humid,thumid,oxymas,oxysmas
	REAL :: dvcombreal,dvcombglo,dvfumeereal,dvfumeeglo
	REAL :: dvoxyreal,dvoxyglo,dvsoxyreal,dvsoxyglo,exoxyglo
	LOGICAL :: qoxydant,qhumid,qoxymas
	REAL, DIMENSION(NX) :: fracomb_grid, longzone_grid !ccri

	REAL born1,born2, small
	REAL :: somme,som,rrealglo
	INTEGER :: somi
	REAL :: hmel,chamel
	INTEGER, PARAMETER :: DIM=100
	INTEGER :: i,j,k,l,ipara,igaz,m
	REAL, DIMENSION(nbzonesmax) :: Tairbrul,mbruleur,totaldimen
	REAL, DIMENSION(nbzonesmax) :: ybN,mbruleurz
	REAL, DIMENSION(nbzonesmax) :: ybH2O,ybO,ybCO2,ybAr,ybF
	REAL, DIMENSION(nbzonesmax) :: xbH2O,xbO,xbCO2,xbAr,xbF,xbN
	REAL, DIMENSION(nbzonesmax):: hentalox,hentalpara,hentalcomb,cpmel
	REAL, DIMENSION(nbzones):: hentalmoy,hentalmel
	REAL, DIMENSION(nbzones):: puissbruz
	REAL, DIMENSION(nbzones):: puisspci
	REAL, DIMENSION(nbzones):: fracomb0

	REAL :: DIFF, dDIFF
	REAL :: he,hs1
	REAL :: Ts1c
	REAL :: Ts,hoxy,hcomb,OM,a
	REAL :: reac0,ynul,yunite
	REAL :: small2
	REAL, DIMENSION (nbzonesmax) :: XBRULEUR,YOXYH2O,YOXYCO2
	REAL, DIMENSION (nbzonesmax) :: YOXYAR
	REAL, DIMENSION (nbzonesmax) :: Tcomb,mcomb,Toxy,moxy
	REAL, DIMENSION (nbzonesmax) :: yoxyO2,yoxyN2,Reparpuiss,RO
	REAL, DIMENSION (nbzonesmax) :: mcomb2,moxy2,mpara2	
!
	REAL, DIMENSION (nbzonesmax) :: yparaN2,yparaO2,yparaCO2
	REAL, DIMENSION (nbzonesmax) :: yparaH2O,yparaAR,tpara,mpara

	REAL, PARAMETER :: zero = 0.0, ud6 = 1.0e6
	INTEGER :: degasOK, infilOK
	REAL :: sum1, sum2, sum3, sum4
	REAL :: qvco2, qvh2o, pwrdegas, tdegas, qvn2, qvo2, pwrinfil, tinfil
	REAL, DIMENSION (nbzonesmax) :: qvcomb, qvoxy, pwrcomb, pwroxy, pwrtot, chemrap
!
	REAL :: mcombglo,moxyglo
	REAL :: mcombtot,moxytot
	REAL :: moxysto
	REAL :: XOO2,XOH2O,XOCO2,XOAR,XON2
	REAL :: YOO2,YOH2O,YOCO2,YOAR,YON2
	REAL, DIMENSION(nbzonesmax) :: frazonereel
	REAL :: Psa,Psb,Psc,Ps_H2O
	REAL :: FH2O,FAIRSEC,FAIRTOT
	REAL ::  pwrztothbr,pwrztotpci,mfitot,mfztot

	INTEGER :: izone
	REAL :: debcvol,debo2vol,debovol

	REAL :: MO2,MCO2,MAr,MH2O,MN2,R,hpara 
	PARAMETER(MO2=0.032,MN2=0.028,MCO2=0.044,MAr=0.040,MH2O=0.018,R=8.3143)

	!cth Impression
	INTEGER :: ires(3)
	COMMON/impr/ires

	REAL :: REACFOUR, REACBRUL
	COMMON/combustion/reacbrul,reacfour
	REAL :: PCI,WM,R_O2_FUEL,R_CO2_FUEL
	REAL :: R_H2O_FUEL,R_N2_FUEL,INVWM,R_H2O_CO2,R_CO2_O2,R_N2_O2
	COMMON/combustible/PCI,WM,R_O2_FUEL,R_CO2_FUEL&
	&  ,R_H2O_FUEL,R_N2_FUEL,INVWM,R_H2O_CO2,R_CO2_O2,R_N2_O2
!
! Initialisation de variables
!-----------------------------
	small        =  5.e-6
	small2       =  5.e-6 !5e-6 cyou
	reac0        =  0.
	mbruleurz    =  0.
	mbruleur     =  0.
	nbbruleur    =  1
	mcomb        =  0.
	mcombglo        =  0.
	moxy         =  0.
	moxysto      =  0.
	moxyglo      =  0.
	mcomb2       =  0.
	moxy2        =  0.
	mpara2       =  0.
	tpara        =  273.15
	mpara        =  0.
	yparaN2      =  0.
	yparaO2      =  0.
	yparaCO2     =  0.
	yparaH2O     =  0.
	yparaAR      =  0.
	puissbruz    =  0.
	puissbrul    =  0.
	hentalox     =  0.
	mb           =  0.
	TBR          =  273.15
	yN           =  0.
	yH2O         =  0.
	yO           =  0.
	yCO2         =  0.
	yAr          =  0.
	yF           =  0.
	Tcomb        =  273.15
	Toxy         =  273.15
	XBRULEUR     =  0.
	YOXYH2O      =  0.
	YOXYCO2      =  0.
	YOXYAR       =  0.
	yoxyO2       =  0.
	yoxyN2       =  0.
	Tairbrul     =  273.15
	ybN          =  0.
	ybH2O        =  0.
	ybO          =  0.
	ybCO2        =  0.
	ybAr         =  0.
	ybF          =  0.
	xbH2O        =  0.
	xbO          =  0.
	xbCO2        =  0.
	xbAr         =  0.
	xbF          =  0.
	xbN          =  0.
	Reparpuiss   =  0.
	RO           =  0.
	nbtranchzone =  0
	indmaille    =  0
	ynul   =0.
	yunite =1.
	version=' '
	version='v1'
!cth DEVRAIT ETRE DANS ROUTINE SPECIFIQUE
!cth OXYDANT COMME POUR COMBUSTIBLE
!cth + CONVERSION POUR INFO EN MOLAIRE ET EN MASSE
!cth Composition oxy seche et reelle
!cth Suivant la composition donnee dans .phy
!cth ==> H2O ori doit etre nul ==> VERIF A METTRE
	if (qoxydant) then
!cth Conversion masse --> molaire pour calcul humid et sec
		do igaz=1,nboxydant
			if (qoxymas) then
				if (qhumid) then
					YOO2=xsoxymas(icodo2,igaz)
					YOCO2=xsoxymas(icodco2,igaz)
					YOAR=xsoxymas(icodar,igaz)
					YON2=xsoxymas(icodn2,igaz)
					oxysmas=(YOO2/MO2)+(YOCO2/MCO2)+(YOAR/MAR)+(YON2/MN2)
					oxysmas=1.0/oxysmas
					xsoxymol(icodo2,igaz)=xsoxymas(icodo2,igaz)*oxysmas/MO2
					xsoxymol(icodco2,igaz)=xsoxymas(icodco2,igaz)*oxysmas/MCO2
					xsoxymol(icodar,igaz)=xsoxymas(icodar,igaz)*oxysmas/MAR
					xsoxymol(icodn2,igaz)=xsoxymas(icodn2,igaz)*oxysmas/MN2
				else
					YOO2=xoxymas(icodo2,igaz)
					YOCO2=xoxymas(icodco2,igaz)
					YOH2O=xoxymas(icodh2o,igaz)
					YOAR=xoxymas(icodar,igaz)
					YON2=xoxymas(icodn2,igaz)
					oxymas=(YOO2/MO2)+(YOCO2/MCO2)+(YOAR/MAR)+(YON2/MN2)+(YOH2O/MH2O)
					oxymas=1.0/oxymas
					xoxymol(icodo2,igaz)=xoxymas(icodo2,igaz)*oxymas/MO2
					xoxymol(icodco2,igaz)=xoxymas(icodco2,igaz)*oxymas/MCO2
					xoxymol(icodh2o,igaz)=xoxymas(icodh2o,igaz)*oxymas/MH2O
					xoxymol(icodar,igaz)=xoxymas(icodar,igaz)*oxymas/MAR
					xoxymol(icodn2,igaz)=xoxymas(icodn2,igaz)*oxymas/MN2
				endif
			endif
			if (qhumid) then
				Psa    = 11.7790     ! Constants in Antoine's Equ. for Ps(H2O)
				Psb    = 3885.6980
				Psc    = -42.980
				Ps_H2O = EXP( Psa - Psb / (thumid + Psc) )  ! In bar
				FH2O   = humid*1.e-2 * Ps_H2O / 1.0130  ! Amount of Water
				FAIRSEC   = 1.0
				FAIRTOT   = FAIRSEC+FH2O
				do i=1,ncodesp
					xoxymol(i,igaz)=xsoxymol(i,igaz)*FAIRSEC/FAIRTOT
				enddo
				xoxymol(icodh2o,igaz)=FH2O/FAIRTOT
			else
				FH2O=xoxymol(icodh2o,igaz)
				FAIRSEC=1.0-FH2O
				FAIRTOT=1.0
				do i=1,ncodesp
					xsoxymol(i,igaz)=xoxymol(i,igaz)*FAIRTOT/FAIRSEC
				enddo
				xsoxymol(icodh2o,igaz)=0.0
			endif
!cth APRIORI DANS ROUTINE OXYDANT CAR PHYSIQUE
!cth TOUTE COMPOSITION ET CONVERSION
!cth Conversion composition oxydant reel en massique
			XOO2=xoxymol(icodo2,igaz)
			XOCO2=xoxymol(icodco2,igaz)
			XOH2O=xoxymol(icodh2o,igaz)
			XOAR=xoxymol(icodar,igaz)
			XON2=xoxymol(icodn2,igaz)
			oxymas=MH2O*XOH2O+MO2*xoO2+MCO2*XOCO2+MAr*XOAR+MN2*XON2
			xoxymas(icodo2,igaz)=xoxymol(icodo2,igaz)*MO2 / oxymas
			xoxymas(icodco2,igaz)=xoxymol(icodco2,igaz)*MCO2 / oxymas
			xoxymas(icodh2o,igaz)=xoxymol(icodh2o,igaz)*MH2O / oxymas
			xoxymas(icodar,igaz)=xoxymol(icodar,igaz)*MAR / oxymas
			xoxymas(icodn2,igaz)=xoxymol(icodn2,igaz)*MN2 / oxymas
			YOO2=xoxymas(icodo2,igaz)
			YOCO2=xoxymas(icodco2,igaz)
			YOH2O=xoxymas(icodh2o,igaz)
			YOAR=xoxymas(icodar,igaz)
			YON2=xoxymas(icodn2,igaz)
			XOO2=xsoxymol(icodo2,igaz)
			XOCO2=xsoxymol(icodco2,igaz)
			XOAR=xsoxymol(icodar,igaz)
			XON2=xsoxymol(icodn2,igaz)
			oxysmas=MO2*xoO2+MCO2*XOCO2+MAr*XOAR+MN2*XON2
			xsoxymas(icodo2,igaz)=xsoxymol(icodo2,igaz)*MO2 / oxysmas
			xsoxymas(icodco2,igaz)=xsoxymol(icodco2,igaz)*MCO2 / oxysmas
			xsoxymas(icodar,igaz)=xsoxymol(icodar,igaz)*MAR / oxysmas
			xsoxymas(icodn2,igaz)=xsoxymol(icodn2,igaz)*MN2 / oxysmas
		enddo
	else
		print *,' BRULEUR : PAS OXYDANT DANS .phy '
	endif
!cth MAIS SURTOUT DOIT ETRE FAIT DANS ROUTINE ENTREE_COMBUS
!cth ET AVOIR INFO PAR ZONE
!cth Debit et info reaffecter par entree si global
	if (qcombglo) then
!cth       Bidouille pour repartition a Guillaume
!cth En fait moins bidouille car on ne le fait
!cth que si les zones n ont pas ete defini dans .geo
!cth et donc ont ete creees automatiquement
        do i=1,nbzones !ccri  To keep fracomb before redistribution to the neighboring zone
            fracomb0(i)=fracomb(i)
        enddo   
		if (.not.qzone) then
			izone=brulzone(1)
			qfracomb(izone-1)=.true.
			qdefcomb(izone-1)=.true.
			fracomb(izone-1)=crepcomb(izone)*fracomb(izone)
			fracomb(izone)=(1.-crepcomb(izone))*fracomb(izone)
			izone=brulzone(nposbrul)
			qfracomb(izone+1)=.true.
			qdefcomb(izone+1)=.true.
			fracomb(izone+1)=crepcomb(izone)*fracomb(izone)
			fracomb(izone)=(1.-crepcomb(izone))*fracomb(izone)
		endif
		do i=1,nbzones
			if (.not.qfracomb(i)) then
				print *,' BRULEUR : COMBUS DEBIT GLOBAL ET LOCAL ?? '
				print *,' DEBIT GLOBAL : ',qcombglo,dcombglo
				print *,' ZONE : ',i
				print *,' FRACTION LOCAL : ',qfracomb(i),fracomb(i)
				stop ' MIEUX VAUT S ARRETER '
			endif
		enddo
!
		do i=1,nbzones
			debcomb(i)=fracomb(i)*dcombglo
			tempcomb(i)=tcombglo
			qmcomb(i)=qmcombglo
		enddo
!cth Debit massique de combustible global
		if (qmcombglo) then
			mcombglo=dcombglo
		else
			mcombglo=dcombglo*WM*101325./(R*273.15*3600.)
		endif
		endif

!cth A PRIORI PAS VRAIMENT BESOIN DU GLOBAL
!cth MAIS SURTOUT DOIT ETRE FAIT DANS ROUTINE ENTREE_OXYDANT
!cth ET AVOIR INFO PAR ZONE
!cth Passage debit global a local si global
!cth Mais aussi exprime en air sec ou humid
		if (qoxyglo) then
			if (neoxy.eq.0) then
				neoxy=necomb
				do i=1,neoxy
				  idoxy(i)=idcomb(i)
				  ifoxy(i)=ifcomb(i)
				  desoxy(i)=descomb(i)
				enddo
				do i=1,nbzones
				  qdefoxy(i)=qdefcomb(i)
				  fraoxy(i)=fracomb(i)
				  qfraoxy(i)=qfracomb(i)
				enddo
			endif
			do i=1,nbzones
				if (.not.qfraoxy(i)) then
					print *,' BRULEUR : OXYDANT DEBIT GLOBAL ET LOCAL ?? '
					print *,' DEBIT GLOBAL : ',qoxyglo,doxyglo,eoxyglo
					print *,' ZONE : ',i
					print *,' FRACTION LOCAL : ',qfraoxy(i),fraoxy(i)
					stop ' MIEUX VAUT S ARRETER '
				endif
			enddo
			do i=1,nbzones
				deboxy(i)=fraoxy(i)*doxyglo
				exeoxy(i)=eoxyglo
				tempoxy(i)=toxyglo
				qeoxy(i)=qeoxyglo
				qsoxy(i)=qsoxyglo
				qmoxy(i)=qmoxyglo
			enddo
!cth Debit massique de combustible global
			if (qmoxyglo) then
				moxyglo=doxyglo
				if (qsoxyglo) moxyglo=moxyglo*(xsoxymol(icodo2,1)/xoxymol(icodo2,1))
			else
				moxyglo=doxyglo*oxymas*101325./(R*273.15*3600.)
				if (qsoxyglo) moxyglo=moxyglo*(xsoxymol(icodo2,1)/xoxymol(icodo2,1))
			endif
		endif

!cth DOIT ETRE FAIT DANS ROUTINE ENTREE_OXYDANT
!cth ET AVOIR INFO PAR ZONE
!cth Passage en debit massique
!cth Cas exces ou debit
!cth Cas air sec ou humid
		do i=1,nbzones
			igaz=indoxy(i)
			if (qeoxy(i)) then
				if (qmcomb(i)) then
				  debcvol=debcomb(i)/(wm*101325.)*(R*273.15*3600.)
				else
				  debcvol=debcomb(i)
				endif
				debo2vol=debcvol*r_o2_fuel
				debovol=debo2vol/xoxymol(icodo2,igaz)
				deboxy(i)=debovol*(1.0+exeoxy(i)*1.0e-2)
				qmoxy(i)=.false.
			else if (qsoxy(i)) then
				deboxy(i)=deboxy(i)*(xsoxymol(icodo2,igaz)/xoxymol(icodo2,igaz))
			endif
		enddo

!cth Affectation par zone bruleur
!cth Avec Conversion debit en masse si necessaire
		qdefent=.false.
		qdefsor=.false.
		do izone=1,nbzones
			qdefent(izone)=(qdefent(izone).or.qdefcomb(izone))
		enddo
		do izone=1,nbzones
			qdefent(izone)=(qdefent(izone).or.qdefoxy(izone))
		enddo
		do izone=1,nbzones
			qdefent(izone)=(qdefent(izone).or.qdefpara(izone))
		enddo
		do i=1,nsortie
			izone=idsortie(i)
			qdefsor(izone)=qdefsortie(izone)
		enddo
		do i=1,nbzones
			if (qmcomb(i)) then
				mcomb(i)=debcomb(i)
			else
				mcomb(i)=debcomb(i)*WM*101325./(R*273.15*3600.)
			endif
			tcomb(i)=tempcomb(i)
			if (qmoxy(i)) then
				moxy(i)=deboxy(i)
			else
				moxy(i)=deboxy(i)*oxymas*101325./(R*273.15*3600.)
			endif
			toxy(i)=tempoxy(i)
			if (qdefoxy(i)) then
				igaz=indoxy(i)
				YOXYO2(I)=xoxymas(icodo2,igaz)
				YOXYCO2(I)=xoxymas(icodco2,igaz)
				YOXYH2O(I)=xoxymas(icodh2o,igaz)
				YOXYAR(I)=xoxymas(icodar,igaz)
				YOXYN2(I)=xoxymas(icodn2,igaz)
			endif
			if (qdefpara(i)) then
				mpara(i)=debpara(i)
				tpara(i)=temppara(i)
				YPARAO2(I)=xmaspara(icodo2,i)
				YPARACO2(I)=xmaspara(icodco2,i)
				YPARAH2O(I)=xmaspara(icodh2o,i)
				YPARAAR(I)=xmaspara(icodar,i)
				YPARAN2(I)=xmaspara(icodn2,i)
			endif
		enddo
!cth Debit globaux
		mcombtot=0.0
		moxytot=0.0
		do i=1,nbzones
			mcombtot=mcombtot+mcomb(i)
			moxytot=moxytot+moxy(i)
		enddo
		dvcombreal=mcombtot/(wm*101325.)*(R*273.15*3600.)
		if (qcombglo) then
			dvcombglo=mcombglo/(wm*101325.)*(R*273.15*3600.)
		else
			dvcombglo=dvcombreal
		endif
			rrealglo=dvcombglo/dvcombreal
			dvoxyreal=moxytot/(oxymas*101325.)*(R*273.15*3600.)
		if (qoxyglo) then
			dvoxyglo=moxytot/(oxymas*101325.)*(R*273.15*3600.)
		else
			dvoxyglo=dvoxyreal
		endif
			dvsoxyreal=dvoxyreal*(xoxymol(icodo2,1)/xsoxymol(icodo2,1))
			dvsoxyglo=dvoxyglo*(xoxymol(icodo2,1)/xsoxymol(icodo2,1))
		if (qeoxyglo) then
			exoxyglo=eoxyglo
		else
			debo2vol=dvcombglo*r_o2_fuel
			debovol=debo2vol/xoxymol(icodo2,1)
			exoxyglo=(dvoxyglo/debovol-1.0)*1.0e+2
		endif
!
		if (qcombglo) then
			if (abs((mcombglo-mcombtot)/mcombtot).gt.1.0e-5) then
				print *,' BURNERS : DEBIT COMBU GLOBAL ?? '
				print *,mcombglo,mcombtot,abs((mcombglo-mcombtot)/mcombtot)
				print *,dcombglo
			endif
		endif
		if (qoxyglo) then
			if (abs((moxyglo-moxytot)/moxytot).gt.1.0e-5) then
				print *,' BURNERS : DEBIT OXYDANT GLOBAL ?? '
				print *,moxyglo,moxytot,abs((moxyglo-moxytot)/moxytot)
				print *,doxyglo,eoxyglo
			endif
		endif

		do i=1,nbzones
			frazonereel(i)=mcomb(i)/mcombtot
		enddo

		if (ires(1).ge.1) then
			print *,' STOECHIO : ',pci,r_o2_fuel,r_h2o_fuel,r_co2_fuel
			print *,' COMBUS : ',necomb,nbzones
			do i=1,nbzones
				print *,i,debcomb(i),fracomb(i),tempcomb(i),irepcomb(i)
			enddo
			print *,' OXYDANT : ',neoxy,nbzones
			do i=1,nbzones
				print *,i,deboxy(i),fraoxy(i),exeoxy(i),tempoxy(i),irepoxy(i)
			enddo
			print *,' FRACTION COMBUS : ',ncombesp,wm
			do i=1,ncodesp
				print *,i,xcombmol(i),xcombmas(i)
			enddo
			print *,' FRACTION OXY : ',noxyesp,oxymas,oxysmas
			do igaz=1,nboxydant
				print *,' OXYDANT : ',igaz
				do i=1,ncodesp
					print *,i,xoxymol(i,igaz),xsoxymol(i,igaz),xoxymas(i,igaz),xsoxymas(i,igaz)
				enddo
			enddo
			if (nepara.ne.0) then
			print *,' FRACTION PARASITE : ',nepara,nbzones
				do ipara=1,nbzones
					print *,' ENTREE PARASITE : ',ipara,nesppara(ipara)
					do i=1,ncodesp
						print *,i,xmolpara(i,ipara),xmaspara(i,ipara)
					enddo
				enddo
			endif
			print *,' FRACTION MAS OXY ZONE : '
			do i=1,nbzones
				print *,i,YOXYO2(I),YOXYH2O(I),YOXYCO2(I),YOXYAR(I),YOXYN2(I)
			enddo
			print *,' DEBIT MAS PARASITE ZONE : '
			do i=1,nbzones
				print *,i,mpara(i),tpara(i)
			enddo
			print *,' FRACTION MAS PARASITE ZONE : '
			do i=1,nbzones
				print *,i,YPARAO2(I),YPARAH2O(I),YPARACO2(I),YPARAAR(I),YPARAN2(I)
			enddo
			print *,' EXCES AIR : ',dvoxyglo,moxysto,dvsoxyglo,eoxyglo
			print *,' DEBIT AIR : ',dvoxyglo,moxysto,dvsoxyglo,doxyglo
			print *,' BURNERS COMB VOL : ',dvcombreal,dvcombglo
			print *,' BURNERS : ',mcombglo,moxyglo,tcombglo,toxyglo
			do i=1,nbzones
				print *,i,mcomb(i),moxy(i),tcomb(i),toxy(i)
			enddo
			print *,' BRULEURS REEL : ',mcombtot,moxytot,tcombglo,toxyglo
			print *,' ZONE INIT : ',nposbrul,nbzones
			do i=1,nbzones
				print *,i,fracomb(i),brulzone(i)
			enddo
			print *,' ZONE REEL : ',nposbrul,nbzones
			do i=1,nbzones
				print *,i,frazonereel(i),brulzone(i)
			enddo
		endif

!	
! Calcul de la composition du melange oxy+comb
!---------------------------------------------

!cth SI PROPRE PLUS D OPTION POUR CALCUL DU MELANGE
!cth CAR INFOS D ARRIVEE DOIVENT TOUJOURS ETRE LES MEMES
!cth A VERIFIER QUELLES INFOS ?
	if (version.ne.'v2') then
		do i=1,nbzones
			if (qdefent(i)) then
			  mbruleur(i)  = mcomb(i)+moxy(i)+mpara(i)     
			  a = max(mbruleur(i),small2)
			  ybF(i)  = mcomb(i)  / a
			  ybO(i)  = (yoxyO2(i) *moxy(i) + yparaO2(i) *mpara(i))/a
			  ybCO2(i)= (yoxyCO2(i)*moxy(i) + yparaCO2(i)*mpara(i))/a
			  ybH2O(i)= (yoxyH2O(i)*moxy(i) + yparaH2O(i)*mpara(i))/a
			  ybAr(i) = (yoxyAR(i) *moxy(i) + yparaAR(i) *mpara(i))/a
			  ybN(i)  = 1.-ybF(i)-ybO(i)-ybCO2(i)-ybH2O(i)-ybAr(i)     
			  if (ybN(i).lt.0.0) ybN(i)=0.0
			  if (yoxyO2(i).eq.1.0.and.mpara(i).le.small2) then
			    ybF(i)  = mcomb(i)  / a
			    ybN(i)  = 0.
			    ybO(i)  = 1. - ybF(i)
			    ybCO2(i)= 0.
			    ybH2O(i)= 0.
			    ybAr(i) = 0.
			  endif
			endif
		enddo
!cth Init fraction des zones sans entrees
	somme=0.
	DO I=1,nbzones
		nbbruleur(i) = 1
		mbruleur(i)  = mcomb(i)+moxy(i)+mpara(i)     
		somme=somme+mbruleur(i)	
		if (moxy(i).gt.0.0) then
			hoxy=hmel(reac0,yoxyN2(i),yoxyO2(i),yoxyCO2(i),yoxyH2O(i),&
	&		yoxyAR(i),ynul,Toxy(i))
		else
			hoxy=0.0
		endif
		hentalox(i) = hoxy
		if (mcomb(i).gt.0.0) then
			hcomb=hmel(reac0,ynul,ynul,ynul,ynul,ynul,yunite,Tcomb(i))
		else
			hcomb=0.0
		endif
		hentalcomb(i)=hcomb
		if (mpara(i).gt.0.0) then
			hpara=hmel(reac0,yparaN2(i),yparaO2(i),yparaCO2(i),yparaH2O(i),&
	&		yparaAR(i),ynul,Tpara(i))
		else
			hpara=0.0
		endif
		hentalpara(i)=hpara
		if (mbruleur(i).gt.0.0) then
			he=(moxy(i)*hoxy+mcomb(i)*hcomb+mpara(i)*hpara)/mbruleur(i)
			Ts=(moxy(i)*Toxy(i)+mcomb(i)*Tcomb(i)+mpara(i)*Tpara(i))/mbruleur(i)
		else
			he=0.0
			Ts=273.15
		endif
		hentalmoy(i)=he
		Ts1c  = 0.
		if (toxy(i).eq.tcomb(i).and.mpara(i).lt.small2) then
		   Tairbrul(i) = toxy(i)
		   goto 56
		endif
		if (mbruleur(i).gt.0.0) then
			DO WHILE ( abs((Ts-Ts1c)/Ts) > small2)
			  Ts1c = Ts
			  hs1  = hmel(reac0,ybN(i),ybO(i),ybCO2(i),ybH2O(i),ybAr(i),ybF(i),Ts1c)
			  DIFF = hs1-he
			  dDIFF= chamel(reac0,ybN(i),ybO(i),ybCO2(i),ybH2O(i),ybAr(i),ybF(i),Ts1c)
			  Ts   = Ts1c-DIFF/dDIFF
			ENDDO
		endif
55		Tairbrul(i)=  Ts
56		Continue
		cpmel(i) = chamel(reac0,ybN(i),ybO(i),ybCO2(i),ybH2O(i),ybAr(i),ybF(i),Tairbrul(i))
		hentalmel(i)=hmel(reac0,ybN(i),ybO(i),ybCO2(i),ybH2O(i),ybAr(i),ybF(i),Tairbrul(i))
	ENDDO
	somme=0.
	ENDIF

	if(mbruleur(1).eq.zero) then
		print *,' BRULEUR : DEBIT NUL DANS LA PREMIERE MAILLE '
		stop ' MIEUX VAUT S ARRETER '
	endif
!
!initialisation par tranche
!--------------------------
!cth ICI C EST DE LA GEOMETRIE
!cth DOIT ETRE DANS ROUTINE GEO_ZONE_BRUL
!cth POSITION, DEFINITION ET TABLE DE CORRESPONDANCE
!cth POS BRUL ZONE MAILLE NOEUD
!cth A VERIFIER QUELLES INFOS ?
	do i =1,nbzones
		zonebruleur(i) = i
	enddo	
	somi = 0
	born1 = 0. 
	born2 = longzones(1) 
	do j = 1,nbzones-1          
		l = 0
		do k=1,nx+1
			if (gridx(k)>born1.or.abs(gridx(k)-born1)<small) then
				if (gridx(k)<born2.or.abs(gridx(k)-born2)<small) l = l+1
			endif 
		enddo
		nbtranchzone(j) = l-1
		somi = somi + nbtranchzone(j)         
		born1 = born2
		born2 = born2 + longzones(j+1)          
	enddo

	nbtranchzone(nbzones) = nx-somi
	indmaille(1) = 0        
	do i = 2,nbzones
		indmaille(i) = indmaille(i-1) + nbtranchzone(i-1)
	enddo
	indmaille(nbzones+1)=nx

	j=0 
	do i = 1,nbzones
		totaldimen(i) = 0.0d0
		do k = 1,nbtranchzone(i)
			j = j + 1
			totaldimen(i) = totaldimen(i) + dimen(j)
             		longzone_grid(j)=longzones(i)  !ccri for hcv in the Callidus correlation
		enddo
	enddo
!ccri Recovering fracomb0(zone) in fracomb_grid(tranche)
        j=0
        do i=1,nbzones
          do k=1,nbtranchzone(i)
             j=j+1
             fracomb_grid(j)=fracomb0(i)    !ccri for hcv in the Callidus correlation
          enddo
        enddo
!cth SI PROPRE PLUS D OPTION POUR CALCUL DU MELANGE
!cth CAR INFOS D ARRIVEE DOIVENT TOUJOURS ETRE LES MEMES
!cth A VERIFIER QUELLES INFOS ?
	do i = 1,nbzones 
		l = zonebruleur(i)
		j = indmaille(l)
		som = 0.
		IF (version.ne.'Ab') Then
			do k = 1,nbtranchzone(l)
				j=j+1     
				yN(J)  = ybN(i)
				yH2O(J)= ybH2O(i)
				yO(J)  = ybO(i)
				yCO2(J)= ybCO2(i)
				yAr(J) = ybAr(i)
				yF(J)  = ybF(i)
				mb(J)  = mb(J)+nbbruleur(i)*mbruleur(i)*dimen(j)/longzones(l)
				som    = som + mb(j)
				TBR(J) = Tairbrul(i)
				tmel(j)=Tairbrul(i)
			enddo
		endif
	enddo
!ccri Calcul debit bruleur par maille, avant la redistribution
	DO i=1,NX
!		fracomb_grid(i)=fracomb_grid(i)*sum(mb)/9.
	ENDDO
!cth Calcul puissance hors combustible par zone
	puissbruz=0.
	do j=1,nbzones
		puissbruz(j)= puissbruz(j)+ moxy(j)*hentalox(j)+mpara(j)*hentalpara(j)
	enddo
!cth Calcul puissance PCI par zone
	do j = 1, nbzones
		puissPCI(j) = PCI*mcomb(j)
	enddo
!
!Calcul des temperatures adiabatiques de combustion
!Calcul des temperatures adiabatiques de combustion par maille
!--------------------------------------------------
	DO i=1,NX
		IF (mb(i).gt.0.0) THEN
			Ts=2000.	  
			he=hmel(reac0,yN(i),yO(i),yCO2(i),yH2O(i),yAr(i),yF(i),TBR(i))
			Ts1c = 0.
			DO WHILE ( abs((Ts-Ts1c)/Ts) > small2 )
				Ts1c = Ts
				hs1  = hmel(reacbrul,yN(i),yO(i),yCO2(i),yH2O(i),yAr(i),yF(i),Ts1c)
				DIFF = hs1-he
				dDIFF= chamel(reacbrul,yN(i),yO(i),yCO2(i),yH2O(i),yAr(i),yF(i),Ts1c)
				Ts   = Ts1c-DIFF/dDIFF
			ENDDO
			TBR(i)  = Ts
		ELSE
			TBR(i) = zero
		ENDIF
	ENDDO
!              
!Calcul du debit total du combustible
!------------------------------------
	dvfumeereal=0.
	DO i=1,NX 
		WM1(i) = 1.0/(yN(i)/MN2+yO(i)/MO2+yCO2(i)/MCO2+yH2O(i)/MH2O+yAr(i)/MAr+yF(i)/WM) 
		dvfumeereal=dvfumeereal+mb(i)*R*273.15*3600./(WM1(i)*101325)
	ENDDO
	dvfumeeglo=dvfumeereal*rrealglo

!cth Calcul enthalpie bruleurs par tranche
!cth        et puissance totale
	puissbrul=0.0
	mfitot = 0.0d0
	DO i=1,NX 
		hbr(i)=hmel(reacbrul,yN(i),yO(i),yCO2(i),yH2O(i),&
	&      yAr(i),yF(i),Tbr(i))
		puissbrul=puissbrul+mb(i)*hbr(i)
		mfitot = mfitot + mb(i)
	ENDDO
	if(ires(1).eq.1) then
		filename=casename(1:lcasename)//'_'//moduname(code_max)(1:longcar(moduname(code_max)))//'.brn_dat'
		open(44,file=filename)
		pwrztothbr = 0.0d0
		pwrztotpci = 0.0d0
		mfztot = 0.0d0
		do i=1,nbzones
			l = zonebruleur(i)
			j = indmaille(l)+1
			write(44,*) mcomb(i)+moxy(i)+mpara(i),(mcomb(i)+moxy(i)+mpara(i))*hbr(j),&
	&		moxy(i)*hentalox(i)+mcomb(i)*PCI+mpara(i)*hentalpara(i)
			pwrztothbr = pwrztothbr + (mcomb(i)+moxy(i)+mpara(i))*hbr(j)
			pwrztotpci = pwrztotpci + moxy(i)*hentalox(i)+mcomb(i)*PCI+mpara(i)*hentalpara(i) 
			mfztot = mfztot + (mcomb(i)+moxy(i)+mpara(i))
		enddo
		write(44,*) ' pwrztothbr  pwrztotpci  pwritothbr(=puissbrul) '
		write(44,*) pwrztothbr,pwrztotpci,puissbrul
		write(44,*) ' mfitot   mfztot '
		write(44,*) mfitot,mfztot 
		write(44,*) '  '
		write(44,*) ' Infos bruleurs par zone '
		write(44,*) '  Iter  Long  Pos   Nbmail   indmail   Zonebr  Totdim'
		som=0.0
		do i=1,nbzones
			som=som+longzones(i)
			write(44,*) i,longzones(i),som,nbtranchzone(i),indmaille(i),zonebruleur(i),totaldimen(i)
		enddo
		write(44,*) '  '         
		write(44,*) ' Infos bruleurs par zone '
		write(44,*) '  Iter  Mbr  Tcomb   YFUEL'
		do i=1,nbzones
			write(44,*) i,mcomb(i),tcomb(i),ybf(i)
		enddo
		write(44,*) '  '
		write(44,*) ' Infos bruleurs par zone '
		write(44,*) '  Iter  Moxy  Toxy   YN  YO   YCO2   YH2O   YAR '
		do i=1,nbzones
			write(44,*) i,moxy(i),toxy(i),YOXYN2(I),YOXYO2(I),yoxyCO2(i),YOXYH2O(I),YOXYAR(I)
		enddo
		write(44,*) '  '
		write(44,*) ' Infos bruleurs par zone '
		write(44,*) '  Iter  Mpara  Tpara   YN  YO   YCO2   YH2O   YAR '
		do i=1,nbzones
			write(44,*) i,mpara(i),tpara(i),YparaN2(I),YparaO2(I),yparaCO2(i),YparaH2O(I),YparaAR(I)
		enddo
		write(44,*) '  '
		write(44,*) ' Infos bruleurs par zone '
		write(44,*) '  Iter  Mbr  Tbr   YN  YO   YCO2   YH2O   YAR   YFUEL'
		do i=1,nbzones
			write(44,*) i,mbruleur(i),Tairbrul(i),ybN(i),ybO(i),ybCO2(i),ybH2O(i),ybAr(i),ybF(i)
		enddo
		write(44,*) '  '
		write(44,*) ' Infos bruleurs '
		write(44,*) '  Iter  Mbr  Hbr    Tbr   WM1    YN  YO   YCO2   YH2O   YAR  YFUEL'
		do i=1,nx
			write(44,*) i,mb(i),hbr(i),Tbr(i),WM1(i),yN(i),yO(i),yCO2(i),yH2O(i), yAr(i),yF(i)
		enddo
		close(unit=44)
	endif
!
300	format('Bruleur ',i1,2x,'Tmelange=',1pe11.4,' K')
301	format('# ',a8,' # MASS FLOW AND POWER INLET BY BURNER ZONE')
311	format(f10.4,6x,f10.4,5x,f10.4,3x,f10.4,3x,f10.4,'      RICH O2')
312	format(f10.4,6x,f10.4,5x,f10.4,3x,f10.4,3x,f10.4,'      STOECHI')
313	format(f10.4,6x,f10.4,5x,f10.4,3x,f10.4,3x,f10.4,'      POOR O2')
302	format(f10.4,6x,f10.4,5x,f10.4,3x,f10.4,3x,f10.4)
322	format(16x,f10.4,18x,f10.4)
303	format(f8.2,6x,f8.2,6x,f8.2,6x,f6.2,6x,f6.1)
304	format(f8.2,6x,f6.2,6x,f6.1)
!
	filename=casename(1:lcasename)//'_'//moduname(code_max)(1:longcar(moduname(code_max)))//'.brn'
	open(44,file=filename)

	write(44,301) cversout 
	write(44,*) '----------------------------------------------------------------------------'
	write(44,*) ' BURNERS DATA '
	write(44,*) '----------------------------------------------------------------------------'
	write(44,*) 'Fuel(Nm3/h)  Oxidant(Nm3/h)      Fuel(MW)   Oxidant(MW)  Fraction(%)'
	sum1 = zero
	sum2 = zero
	sum3 = zero
	sum4 = zero 
	do i = 1, nbzones
		if(mcomb(i).gt.zero.or.moxy(i).gt.zero) then
!ccri			OM=1./(YOXYN2(I)/MN2+YOXYO2(i)/MO2)
                        OM=1./(YOXYN2(I)/MN2+YOXYO2(i)/MO2+yoxyCO2(i)/MCO2+YOXYAR(i)/MAr+YOXYH2O(i)/MH2O)
			qvcomb(i) = mcomb(i) / (101325/(273.15*R/WM))*3600
			qvoxy(i) = moxy(i) / (101325/(273.15*R/OM))*3600
			pwrcomb(i) = mcomb(i) * PCI
			pwroxy(i) = moxy(i) * hentalox(i)
			pwrtot(i) =  pwrcomb(i) + pwroxy(i)
			chemrap(i) = (yoxyO2(i)*moxy(i)/MO2) / (mcomb(i)/WM)
			sum1 = sum1 + qvcomb(i)
			sum2 = sum2 + qvoxy(i)
			sum3 = sum3 + pwrcomb(i)
			sum4 = sum4 + pwroxy(i) 
		endif
	enddo
	do i = nbzones, 1, -1
		if(mcomb(i).gt.zero.or.moxy(i).gt.zero) then
			if(chemrap(i).gt.R_O2_FUEL) then
				write(44,311) qvcomb(i), qvoxy(i), pwrcomb(i)/ud6, pwroxy(i)/ud6&
	&			,100*pwrtot(i)/(sum3+sum4)
			elseif(chemrap(i).eq.R_O2_FUEL) then
				write(44,312) qvcomb(i), qvoxy(i), pwrcomb(i)/ud6, pwroxy(i)/ud6&
	&			,100*pwrtot(i)/(sum3+sum4)
			elseif(chemrap(i).lt.R_O2_FUEL) then
				write(44,313) qvcomb(i), qvoxy(i), pwrcomb(i)/ud6, pwroxy(i)/ud6&
	&			,100*pwrtot(i)/(sum3+sum4)
			endif
	endif
	enddo
	write(44,*) 'Total'
	write(44,302) sum1, sum2, sum3/ud6, sum4/ud6, 100.0
	write(44,322) sum1+sum2, (sum3+sum4)/ud6
	degasOK = 0
	infilOK = 0
qvh2o = zero
qvco2 = zero
	qvn2 = zero
	qvo2 = zero
	pwrdegas = zero
	pwrinfil = zero
	do i = 1, nbzones
		if(yparaH2O(i).ne.zero.OR.yparaCO2(i).ne.zero) then
			qvh2o = qvh2o + (yparaH2O(i)*mpara(i))/(101325/(273.15*R/MH2O))*3600
			qvco2 = qvco2 + (yparaCO2(i)*mpara(i))/(101325/(273.15*R/MCO2))*3600
			pwrdegas = pwrdegas + mpara(i) * hentalpara(i) 
			tdegas = tpara(i)
			degasOK = 1 
		endif
		if(yparaN2(i).ne.zero.OR.yparaO2(i).ne.zero) then
			qvn2 = qvn2 + (yparaN2(i)*mpara(i))/(101325/(273.15*R/MN2))*3600
			qvo2 = qvo2 + (yparaO2(i)*mpara(i))/(101325/(273.15*R/MO2))*3600
			pwrinfil = pwrinfil + mpara(i) * hentalpara(i)
			tinfil = tpara(i)
			infilOK = 1
		endif
	enddo
	if (degasOK.eq.1) then
		write(44,*) ' '
		write(44,*) '----------------------------------------------------------------------------'
		write(44,*) ' DEGASING'
		write(44,*) '----------------------------------------------------------------------------'
		write(44,*) ' CO2 (Nm3/h)  H2O (Nm3/h)  TOTAL (Nm3/h)  POWER (MW)  T (C)'
		write(44,303) qvco2, qvh2o, qvco2+qvh2o, pwrdegas/ud6, tdegas-273.15 
	else
		write(44,*) ' '
		write(44,*) '----------------------------------------------------------------------------'
		write(44,*) ' NO DEGASING'
		write(44,*) '----------------------------------------------------------------------------'
	endif
	if (infilOK.eq.1) then
		write(44,*) ' '
		write(44,*) '----------------------------------------------------------------------------'
		write(44,*) ' AIR LEAKS'
		write(44,*) '----------------------------------------------------------------------------'
		write(44,*) ' AIR (Nm3/h) POWER (MW)   T(C)'
		write(44,304) qvn2 + qvo2, pwrinfil/ud6, tinfil-273.15
	else
		write(44,*) ' '
		write(44,*) '----------------------------------------------------------------------------'
		write(44,*) ' NO AIR LEAKS'
		write(44,*) '----------------------------------------------------------------------------'
	endif
	close(44)
!
! Fin de la routine
!------------------
	END
