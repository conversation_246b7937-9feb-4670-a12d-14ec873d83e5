      subroutine read_int_real(nunit,nvaleur,ival1,rval2,qerr)

      implicit none

      integer :: nunit
      integer :: nvaleur
      integer :: ival1(nvaleur)
      real :: rval2(nvaleur)
      logical :: qerr

      integer :: i

      qerr=.false.

      do i=1,nvaleur
        read(nunit,*,ERR=9998,END=9999) ival1(i),rval2(i)
      enddo

      return

9998  continue
      print *,' READ_INT_REAL : ERREUR LECTURE '
      print *,' NVALEUR : ',nvaleur
      qerr=.true.
      return

9999  continue
      print *,' READ_INT_REAL : END OF FILE '
      print *,' NVALEUR : ',nvaleur
      qerr=.true.
      return

      end
