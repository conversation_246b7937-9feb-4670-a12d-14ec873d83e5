      subroutine read_char_real(nunit,ncarsize,nvaleur,chaine,lchaine,rval,qerr)

      implicit none

      integer :: nunit
      integer :: ncarsize
      integer :: nvaleur
      character*(ncarsize) chaine(nvaleur)
      integer :: lchaine(nvaleur)
      real :: rval(nvaleur)
      logical :: qerr

      character*130 cline
      character*130 ctamp
      integer :: lline,ltamp
      integer, parameter :: ntabsize=30
      integer, parameter :: maxtab=30
      integer :: nchamp
      character*(ntabsize) champ(maxtab)
      integer :: lchamp(maxtab)
      integer :: longcar
      integer :: i

      qerr=.false.

      do i=1,nvaleur
        read(nunit,150,ERR=9998,END=9999) cline
150     format(a)

        lline=longcar(cline)

        call cleanline(cline,ctamp,ltamp)

        call decode_champs(ctamp,ntabsize,maxtab,nchamp,champ,lchamp)

        if (nchamp.lt.2) then
          go to 9995
        endif

        chaine(i)=' '
        chaine(i)(1:lchamp(1))=champ(1)(1:lchamp(1))
        lchaine(i)=lchamp(1)

        read(champ(2),*,ERR=9996,END=9997) rval(i)

100     format(a,a)
200     format(a,a,1x,e13.6)
      enddo

      return

9995  continue
      print *,' READ_CHAR_REAL : NOMBRE DE CHAMPS < 2'
      qerr=.true.
      return

9996  continue
      print *,' READ_CHAR_REAL : ERREUR LECTURE CHAMP 2'
      qerr=.true.
      return

9997  continue
      print *,' READ_CHAR_REAL : END OF FILE LECTURE CHAMP 2'
      qerr=.true.
      return

9998  continue
      print *,' READ_CHAR_REAL : ERREUR LECTURE LIGNE '
      qerr=.true.
      return

9999  continue
      print *,' READ_CHAR_REAL : END OF FILE '
      qerr=.true.
      return

      end
