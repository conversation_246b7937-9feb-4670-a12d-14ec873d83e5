	  SUBROUTINE solve_bc_heat_balance (nxp,       vitconv,   tgaz,      yO,       &
                                 &      yN,        yCo2,      yh2o,      yAr,      &
                                 &      yF,        typecond,  tparoi,    frad,     &
                                 &      eps,       hinf,      tinf,      flux,     &
                                 &      modtp,     fcc,       hcc,       lparoi,   &
                                 &      tpadmimin, tpadmimax, errorent,  relaxent, &
                                 &      cornusselt, modcv, Tbr,  vitflame, longzone_grid,hcvcst)
!
! ******************************************************************************
!
!       Cette subroutine fait le bilan d une paroi dans le four
!       Et donc calcule la temperature de paroi
!
! ******************************************************************************
	IMPLICIT NONE

! Declaration des entrees 
!------------------------
	INTEGER, INTENT (IN) :: nxp
	INTEGER, INTENT (IN) :: modtp
	REAL, INTENT (IN), DIMENSION(nxp) :: vitconv
	REAL, INTENT (IN), DIMENSION(nxp) :: vitflame, tgaz, Tbr !ccri Callidus corr
	REAL, INTENT (IN), DIMENSION(nxp) :: yO,yN,yCo2,yh2o,yAr,yF
	INTEGER, INTENT (IN),DIMENSION (nxp) :: typecond
	REAL, INTENT (IN),DIMENSION (nxp) :: frad,hinf,Tinf
	REAL, INTENT (IN),DIMENSION (nxp) :: flux
	REAL, INTENT (IN), DIMENSION(nxp) :: eps
	REAL, INTENT (IN) :: lparoi
	REAL, INTENT (IN) :: errorent
	REAL, INTENT (IN) :: cornusselt
	REAL, INTENT (IN) :: relaxent,tpadmimin,tpadmimax
	REAL, INTENT (IN), DIMENSION(nxp) :: longzone_grid !ccri
	REAL, INTENT (IN) :: hcvcst

! Sorties
!---------

	REAL, INTENT (INOUT),DIMENSION (nxp) :: tparoi
	REAL, INTENT (OUT),DIMENSION (nxp) :: fcc,hcc
	INTEGER, INTENT (INOUT) :: modcv !ccri for Callidus correlation
! Variables internes
!--------------------

	REAL :: dimconv
	REAL :: DIFF,dDIFF
	REAL :: fluxrad,fluxcc,fluxperts
	REAL :: Tpf,Tpc,Tp
	REAL :: sigma
	REAL :: fradinc
	REAL :: SMALL
	LOGICAL :: sous
!cth	REAL :: hc0

	INTEGER :: i, itertp
	INTEGER, PARAMETER :: maxitertp=100

        REAL :: criteretp
        REAL,DIMENSION (nxp) :: tconv !ccri
        INTEGER :: nbconvnat
        DATA nbconvnat/0/

!****   COMMON
        INTEGER :: ires(3)
        COMMON/impr/ires

!
! Initialisations
!----------------
	sigma=5.67e-8
	SMALL=1.E-3
	criteretp=max(errorent,5.0e-7)

!
! Boucle sur les tranches
!------------------------
	
	DO i=1,nxp

          IF (modcv.eq.2.and.vitflame(i).ne.0.0) THEN !ccri
            tconv(i)=Tbr(i)
          ELSE
            tconv(i)=tgaz(i)
          ENDIF

!cth INTEGRE compute_convec_comb
!cth 	  hc0=5.
          dimconv=lparoi
	  sous=.false.
	  IF (vitconv(i).LT.SMALL) sous=.true. ! Convection naturelle
          IF (ires(2).ge.1) then
          if (sous.and.nbconvnat.le.30) then
            print *,' BILANENTHALPIQUE : Conv. Nat. : ',i,vitconv(i)
            if (nbconvnat.eq.30) print *,' BILANENTHALPIQUE : quiet mode ! '
            nbconvnat=nbconvnat+1
          endif
          ENDIF

	  fradinc=frad(i)/eps(i)+sigma*(tparoi(i))**4 !charge
	  Tpc=0.
	  Tp=tparoi(i)

	  IF (typecond(i).EQ.2) THEN          ! Cas Temperature de paroi imposee

	    CALL compute_convec_comb (dimconv, vitconv(i), yO(i), yN(i), &
                               &  yCO2(i), yH2O(i), yAr(i), yF(i), &
     	                       &  tconv(i), Tp, cornusselt, sous, &
                               &  modcv, vitflame(i), longzone_grid(i), hcvcst, &
                               &  hcc(i))

!cth NE SERT A RIEN
!cth	    IF (.NOT.sous) hc0=hcc(i)         ! Convection forcee
!cth INTEGRE compute_convec_comb
!cth	    IF (sous) hcc(i)=0.5*(hcc(i)+hc0) ! Convection naturelle

	  ELSEIF (typecond(i).EQ.1) THEN      ! Cas hinfini et Tinfini imposes

	    itertp=1

	    DO WHILE (ABS((Tp-Tpc)/Tp) > criteretp) ! Calcul de la temperature
                                                    ! de paroi qui equilibre
                                                    ! les flux
	      Tpc=Tp
	      Tpf=Tp
	      if (tpc<=tpadmimin) tpc=0.5*(tpadmimin+tpf)
	      if (tpc>=tpadmimax) tpc=0.5*(tpadmimax+tpf)

              CALL compute_convec_comb (dimconv, vitconv(i), yO(i), yN(i), &
                               &  yCO2(i), yH2O(i), yAr(i), yF(i), &
     	                       &  tconv(i), Tpc, cornusselt, sous, &
                               &  modcv, vitflame(i), longzone_grid(i), hcvcst, &
                               &  hcc(i))
!cth NE SERT A RIEN
!cth	      IF (.NOT.sous) hc0=hcc(i)             ! Convection forcee
!cth INTEGRE compute_convec_comb
!cth	      IF (sous) hcc(i)=0.5*(hcc(i)+hc0)     ! Convection naturelle
	      fluxrad=eps(i)*(fradinc-sigma*Tpf**4) ! charge
	      fluxcc=hcc(i)*(tconv(i)-Tpf)           ! Flux conducto convectif gaz-paroi:
	      fluxperts=hinf(i)*(Tpf-Tinf(i))       ! Flux evacue par conducto convection:

	      IF (modtp.eq.1) THEN
		Tp=(fluxrad+hcc(i)*tconv(i)+hinf(i)*Tinf(i))/(hcc(i)+hinf(i))
		GOTO 200
	      ENDIF

	      DIFF=fluxrad+fluxcc-fluxperts ! Bilan des flux sur la paroi:
	      dDIFF=-4.*eps(i)*sigma*Tpc*Tpc*Tpc-hcc(i)-hinf(i)
	      Tp=Tpf-DIFF/dDIFF*relaxent
	      itertp=itertp+1
              if (itertp.ge.maxitertp) then
                exit
              endif
	    ENDDO
200         CONTINUE

	  ELSEIF (typecond(I).EQ.3) THEN

	    itertp=1
	    DO WHILE (ABS((Tp-Tpc)/Tp) > criteretp) ! Calcul de la temperature de paroi qui equilibre les flux
	      Tpc=Tp
	      Tpf=Tp
	      if (tpc<=tpadmimin) tpc=0.5*(tpadmimin+tpf)
	      if (tpc>=tpadmimax) tpc=0.5*(tpadmimax+tpf)

              CALL compute_convec_comb (dimconv, vitconv(i), yO(i), yN(i), &
                               &  yCO2(i), yH2O(i), yAr(i), yF(i), &
     	                       &  tconv(i), Tpc, cornusselt, sous, &
                               &  modcv, vitflame(i), longzone_grid(i), hcvcst, &
                               &  hcc(i))

!cth NE SERT A RIEN
!cth	      IF (.NOT.sous) hc0=hcc(i)             ! Convection forcee
!cth INTEGRE compute_convec_comb
!cth	      IF (sous) hcc(i)=0.5*(hcc(i)+hc0)     ! convection naturelle
	      fluxrad=eps(i)*(fradinc-sigma*Tpf**4) ! charge

	      fluxcc=hcc(i)*(tconv(i)-Tpf)           ! Flux conducto convectif gaz-paroi:
	      fluxperts=flux(i)                     ! Flux evacue par conductor- convection:
	      DIFF=fluxrad+fluxcc-fluxperts         ! Bilan des flux sur la paroi:
	      dDIFF=-4.*eps(i)*sigma*Tpc*Tpc*Tpc-hcc(i)   ! charge
	      Tp=Tpf-DIFF/dDIFF*relaxent
	      itertp=itertp+1
              if (itertp.ge.maxitertp) exit
	    ENDDO

	  ENDIF
	  fcc(i)=hcc(i)*(tconv(i)-Tp) ! Flux conducto-convectif definitif:
	  tparoi(i)=Tp ! Temperature de la surface apres iterations

	ENDDO	! Fin boucle sur les tranches

9997    format(4(4x,a,1x,f9.2))
9998    format(1x,10f12.2) 
9999    format(1x,10f10.4) 
!
! Fin du programme
!-----------------
	END SUBROUTINE	

