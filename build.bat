@echo off
REM Build script for CALCIFER on Windows 11
REM Requires: Intel OneAPI, CMake

echo ========================================
echo CALCIFER Build Script for Windows 11
echo ========================================

REM Check if Intel OneAPI is available
where ifort >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Intel Fortran Compiler (ifort) not found!
    echo Please install Intel OneAPI HPC Toolkit
    echo Download from: https://www.intel.com/content/www/us/en/developer/tools/oneapi/hpc-toolkit-download.html
    pause
    exit /b 1
)

REM Check if CMake is available
where cmake >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: CMake not found!
    echo Please install CMake
    echo Download from: https://cmake.org/download/
    pause
    exit /b 1
)

echo Found Intel Fortran Compiler: 
ifort --version
echo.
echo Found CMake:
cmake --version
echo.

REM Create build directory
if not exist build mkdir build
cd build

REM Configure with CMake
echo Configuring with CMake...
cmake .. -G "NMake Makefiles" -DCMAKE_BUILD_TYPE=Release
if %errorlevel% neq 0 (
    echo ERROR: CMake configuration failed!
    pause
    exit /b 1
)

REM Build the project
echo Building CALCIFER...
cmake --build . --config Release
if %errorlevel% neq 0 (
    echo ERROR: Build failed!
    pause
    exit /b 1
)

echo.
echo ========================================
echo Build completed successfully!
echo ========================================
echo.
echo Executables created in: build\EXEC\WINDOWS\
echo Libraries created in: build\LIBRARIES\WINDOWS\
echo Modules created in: build\MODULES\WINDOWS\
echo.

REM List created executables
echo Available executables:
dir /b EXEC\WINDOWS\*.exe 2>nul
if %errorlevel% neq 0 (
    echo No executables found in EXEC\WINDOWS\
)

echo.
pause 