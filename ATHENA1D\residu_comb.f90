        subroutine residu_comb(err,ierrmax)
!        
!*****************************************************************
!
!	Modifications : (05/03) Y. <PERSON>
!		Remplacement de residus.out par atmo.cvg
!	Modifications : (12/03) T. <PERSON>ux
!		Remplacement de atmo.cvg par four_somme.rcvg et four_max.rcvg
!
!*****************************************************************
!

        USE modTwoDRad
        USE modshared

	IMPLICIT NONE
!
! Declaration des variables
!--------------------------
        INTEGER :: ierrmax
        REAL :: err

        CHARACTER*130 filename
        INTEGER :: i, longcar
        REAL diff
        INTEGER :: itg(1),itn(1),its(1)
        INTEGER :: ipr(1),ifn(1),ifs(1)

!cth Impression
      INTEGER ires(3)
      COMMON/impr/ires

!
! Calcul de l erreur sur les temperatures, flux radiatif et puissance
!-------------------------------------------------------

	 errtg = sum(abs(Tgi1-Tgi2))
	 errtg = errtg/sum(abs(Tgi2))
	 errtn = sum(abs(tpn1-tpn2))
	 errtn = errtn/sum(abs(tpn2))
	 errts = sum(abs(tps1anc-tps1))
	 errts = errts/sum(abs(tps1))
	 errtw = abs((tpw1-tpw2)/tpw2)
	 errte = abs((tpe1-tpe2)/tpe2)
	 errfn = sum(abs(fradpnanc-fradpn))
	 errfn = errfn/sum(abs(fradpn))
	 errfs = sum(abs(fradpsanc-fradps))
	 errfs = errfs/sum(abs(fradps))
	 errfw = abs((fradpwanc-fradpw)/fradpw)
	 errfe = abs((fradpeanc-fradpe)/fradpe)
	 errpr = sum(abs(puissradanc-puissrad))
	 errpr = errpr/sum(abs(puissrad))

        if (ires(1).ge.1) then
!
! Ecriture erreur sur somme ds le fichier _comb.cvg_sum   
!-----------------------------------
	filename=casename(1:lcasename)//'_'//moduname(code_max)(1:longcar(moduname(code_max)))//'.cvg_sum'
     	OPEN(UNIT=19,FILE=filename,POSITION = 'APPEND')
100     format(i4,12(1x,e10.3))
        write(19,100) indglobt,errtg,errtn,errts,errtw,errte,errpr,errfn,errfs,errfw,errfe,&
     &                reshg
        CLOSE(19)
        endif
	
!
! Calcul de l erreur max sur les temperatures, flux radiatif et puissance
!-------------------------------------------------------

         errpr=0.0
	 do i=1,NX
!cth           if (Tgi1(i).lt.300.0) then
           if (Tgi1(i).lt.tgadmimin) then
             print *,' RESIDU_COMB : TG1 : ',Tgi1(i),tgadmimin
             errpr=errpr+1.0
           endif
           if (Tgi2(i).lt.tgadmimin) then
             print *,' RESIDU_COMB : TG2 : ',Tgi2(i),tgadmimin
             errpr=errpr+1.0
           endif
           if (tpn1(i).lt.tpadmimin) then
             print *,' RESIDU_COMB : TPN1 : ',tpn1(i),tpadmimin
             errpr=errpr+1.0
           endif
           if (tpn2(i).lt.tpadmimin) then
             print *,' RESIDU_COMB : TPN2 : ',tpn2(i),tpadmimin
             errpr=errpr+1.0
           endif
           if (tps1(i).lt.tpadmimin) then
             print *,' RESIDU_COMB : TPS1 : ',tps1(i),tpadmimin
             errpr=errpr+1.0
           endif
           if (tps2(i).lt.tpadmimin) then
             print *,' RESIDU_COMB : TPS2 : ',tps2(i),tpadmimin
             errpr=errpr+1.0
           endif
         enddo
           if (tpw1.lt.tpadmimin) then
             print *,' RESIDU_COMB : TPW1 : ',tpw1,tpadmimin
             errpr=errpr+1.0
           endif
           if (tpw2.lt.tpadmimin) then
             print *,' RESIDU_COMB : TPW2 : ',tpw2,tpadmimin
             errpr=errpr+1.0
           endif
           if (tpe1.lt.tpadmimin) then
             print *,' RESIDU_COMB : TPE1 : ',tpe1,tpadmimin
             errpr=errpr+1.0
           endif
           if (tpe2.lt.tpadmimin) then
             print *,' RESIDU_COMB : TPE2 : ',tpe2,tpadmimin
             errpr=errpr+1.0
           endif
         if (errpr.gt.1.0) then
           STOP ' RESIDU_COMB : TEMPERATURE < MIN '
         endif

        if (ires(1).ge.1) then
         errtg=-1.0
         errtn=-1.0
         errts=-1.0
         errtw=-1.0
         errte=-1.0
         errfn=-1.0
         errfs=-1.0
         errfw=-1.0
         errfe=-1.0
         errpr=-1.0
         errpt=-1.0

	 errtg = maxval(abs((Tgi1-Tgi2)/Tgi2))
	 errtn = maxval(abs((tpn1-tpn2)/tpn2))
	 errts = maxval(abs((tps1anc-tps1)/tps1))
	 errtw = abs((tpw1-tpw2)/tpw2)
	 errte = abs((tpe1-tpe2)/tpe2)
	 errfn = maxval(abs((fradpnanc-fradpn)/fradpn))
	 errfs = maxval(abs((fradpsanc-fradps)/fradps))
	 errfw = abs((fradpwanc-fradpw)/fradpw)
	 errfe = abs((fradpeanc-fradpe)/fradpe)
!cth	 errpr = maxval(abs((puissradanc-puissrad)/puissrad))
        errpr=-1.0
	do i=1,NX
          diff=abs(puissradanc(i)-puissrad(i))
          if (diff.gt.1.0e-10) then
            diff=diff/abs(puissrad(i))
            if (diff.gt.errpr) then
              errpr=diff
            endif
          else
            errpr=0.0
          endif
	enddo

!
! Ecriture erreur max ds le fichier _comb.cvg_max
!-----------------------------------
	  filename=casename(1:lcasename)//'_'//moduname(code_max)(1:longcar(moduname(code_max)))//'.cvg_max'
	  OPEN(UNIT=19,FILE=filename,POSITION = 'APPEND')
          write(19,100) indglobt,errtg,errtn,errts,errtw,errte,errpr,errfn,errfs,errfw,errfe,&
     &                  resmaxhg
          CLOSE(19)

!
! Champs des erreurs dans le fichier four_residu.res
!-----------------------------------
	  filename=casename(1:lcasename)//'_'//moduname(code_max)(1:longcar(moduname(code_max)))//'.cvg_res'
	  OPEN(UNIT=19,FILE=filename)
          write(19,200) ' URF TG:0.1 TN:0.1 TS:0.1 TO:0.1 TE:0.1 FN:0.1 FS:0.1 FO:0.1 FE:0.1 PR:0.1'
200       format(1x,a)
          WRITE(19,200) ' Iter   Tgaz   Tnord   TSUD   TOUEST   TEST   PRAD    FNORD   FSUD   FOUEST    FEST'
          do i=1,nx
	    errtg = (Tgi1(i)-Tgi2(i))/Tgi2(i)
	    errtn = (tpn1(i)-tpn2(i))/tpn2(i)
	    errts = (tps1anc(i)-tps1(i))/tps1(i)
	    errtw = (tpw1-tpw2)/tpw2
	    errte = (tpe1-tpe2)/tpe2
	    errfn = (fradpnanc(i)-fradpn(i))/fradpn(i)
	    errfs = (fradpsanc(i)-fradps(i))/fradps(i)
	    errfw = (fradpwanc-fradpw)/fradpw
	    errfe = (fradpeanc-fradpe)/fradpe
	    errpr = (puissradanc(i)-puissrad(i))/puissrad(i)
            write(19,100) i,errtg,errtn,errts,errtw,errte,errpr,errfn,errfs,errfw,errfe
          enddo
          CLOSE(19)

!cth Calcul de l indice des erreurs max 
	  itg = maxloc(abs((Tgi1-Tgi2)/Tgi2))
	  itn = maxloc(abs((tpn1-tpn2)/tpn2))
	  its = maxloc(abs((tps1anc-tps1)/tps1))
	  ifn = maxloc(abs((fradpnanc-fradpn)/fradpn))
	  ifs = maxloc(abs((fradpsanc-fradps)/fradps))
	  ipr = maxloc(abs((puissradanc-puissrad)/puissrad))
	      filename=casename(1:lcasename)//'_'//moduname(code_max)(1:longcar(moduname(code_max)))//'.cvg_ind'
	  OPEN(UNIT=19,FILE=filename,POSITION = 'APPEND')
          write(19,300) indglobt,float(itg(1))/100.,float(itn(1))/100.,float(its(1))/100.,&
     &                  float(ipr(1))/100.,float(ifn(1))/100.,float(ifs(1))/100.
          CLOSE(19)
300       format(i4,12(1x,f7.3))

        endif

!cth Calcul de l erreur pour test convergence
        err=-1.0
	do i=1,NX				
          diff=abs(Tgi1(i)-Tgi2(i))
          if (diff.gt.err) then
            ierrmax=i
            err=diff
          endif
	enddo

!
! Fin du programme 
!-----------------
        end
