      subroutine read_bc_comb(basefour,lbasefour,&
     &  maxzone,nx,nposbrul,nzone,&
     &  gridx,cellx,&
     &  poszone,brulzone,&
     &  maxoxydant,nboxydant,maxcaroxy,&
     &  nomoxydant)

!****************************************************************************
!
!       Lecture du fichier .lim four
!
!****************************************************************************

!cyou on a laisse les declarations de iconv* mais rien n'est lu
!cyou on ne modifie pas le module modlim de fait.

      USE modshared
      USE modespece
      USE modlim

      implicit none

!
! Declaration des variables
!--------------------------

      CHARACTER*(*) basefour
      INTEGER :: lbasefour
      integer :: maxzone
      integer :: nx,nposbrul,nzone
      real, dimension(nx+1) :: gridx
      real, dimension(nx) :: cellx
      real, dimension(maxzone) :: poszone
      integer, dimension(maxzone) :: brulzone
      integer :: maxoxydant,nboxydant,maxcaroxy
      character*(maxcaroxy) :: nomoxydant(maxoxydant)

      character*8 cvers
      character*130 filename
      integer :: lname
      integer :: nunit
      character*130 cline
      integer :: lline
      integer, parameter :: ncodesize=10
      character*(ncodesize) code
      character*(ncodesize) codecomp
      integer :: lcode
      integer :: lcodecomp
      integer :: nvaleur
      real :: xvaleur
      logical :: qcgot,qvgot
      logical :: qerr
      integer :: i
      logical :: toujours

      character*(cespsize) tabesp(ncodesp)
      integer :: ltabesp(ncodesp)
      real :: valesp(ncodesp)
      integer :: iesp,indice
      character*(maxcaroxy) nomgaz
      integer :: lnomgaz


      character*1 :: typefr,typeent,descr,orient
      integer :: nentre
      integer :: ncombglo,noxyglo
      integer :: nline
      integer :: iindex

      integer :: ibrul,izone,iz
      real :: position
      logical :: qposition

      integer :: iii

      integer :: longcar,index

!cth Niveau Impression
      INTEGER :: ires(3)
      COMMON/impr/ires

!cth Init
      cvers=' '

      nentre=0
      ncombglo=0
      qcombglo=.false.
      qmcombglo=.false.
      dcombglo=0.0
      tcombglo=273.15
      necomb=0
      qbrulcomb=.false.
      qfracomb=.false.
      qmcomb=.false.
      qposcomb=.false.
      qdefcomb=.false.
      descomb=' '
      idcomb=0
      ifcomb=0
      irepcomb=0
      fracomb=0.0
      debcomb=0.0
      tempcomb=273.15
      crepcomb=0.0
      poscomb=0.0
      noxyglo=0
      qoxyglo=.false.
      qeoxyglo=.false.
      qsoxyglo=.false.
      qmoxyglo=.false.
      doxyglo=0.0
      eoxyglo=0.0
      toxyglo=273.15
      neoxy=0
      qbruloxy=.false.
      qfraoxy=.false.
      qeoxy=.false.
      qsoxy=.false.
      qmoxy=.false.
      qposoxy=.false.
      qdefoxy=.false.
      desoxy=' '
      idoxy=0
      ifoxy=0
      irepoxy=0
      indoxy=1
      fraoxy=0.0
      deboxy=0.0
      exeoxy=0.0
      tempoxy=273.15
      crepoxy=0.0
      posoxy=0.0
      nepara=0
      qmpara=.false.
      qmcomppara=.false.
      qcomppara=.false.
      qpospara=.false.
      qdefpara=.false.
      despara=' '
      idpara=0
      ifpara=0
      ireppara=0
      debpara=0.0
      temppara=273.15
      creppara=0.0
      pospara=0.0
      nparoiall=0
      qparoiall=.false.
      epsparoiall=0.0
      hparoiall=0.0
      tparoiall=0.0
      iconvparoiall=0
      nparoi=0
      desparoi=' '
      oriparoi=' '
      typparoi=' '
      idparoi=0
      ifparoi=0
      xdparoi=0.0
      xfparoi=0.0
      qposparoi=.false.
      epsparoi=0.0
      hparoi=0.0
      tparoi=0.0
      degparoi=0.0
      iconvparoi=0
      ficparoi=' '
      ficdegparoi=' '
      ncoupall=0
      ncouplage=0
      qcoupall=.false.
      epscoupall=0.0
      iconvcoupall=0
      nsortie=0
      qdefsortie=.false.
      dessortie=' '
      idsortie=0
      xdsortie=0.0
      dsortie=0.0


!
! Debut du programme
!-------------------

      nunit=99

      filename=' '
      filename=basefour(1:lbasefour)//'.ibc'
      lname=longcar(filename)

      open(unit=nunit,file=filename,STATUS='OLD',ERR=9997)

!cth 1ere ligne = commentaire
      nline=0
      nline=nline+1
 170  FORMAT(2x,a8)
      READ(nunit,170,ERR=9998,END=9999) cvers
      if (cvers.ne.cversion) then
        print *,' READ_BC_COMB : FILE : VERSION ERROR'
        print *,' FILE NAME : ',filename(1:lname)
        print *,' VERSION : EXPECTED : ',cversion
        print *,' VERSION : GOT      : ',cvers
        stop ' CORRECT THIS, AND TRY AGAIN '
      endif

100   format(a)

!cth Boucle sur les lignes du fichier
      toujours=.true.
      do while(toujours)
        code=' '
        code(1:1)='*'
        lcode=longcar(code)
        do while(lcode.lt.2)
          nline=nline+1
          read(nunit,100,ERR=9998,END=1000) cline
          lline=longcar(cline)
          call cleanline(cline,code,lcode)
        enddo
        do i=lcode+1,ncodesize
          code(i:i)='*'
        enddo
        typefr=code(1:1)
        descr=code(3:3)
        if (typefr.eq.'E') then
          typeent=code(2:2)
          nentre=nentre+1
          if (typeent.eq.'C') then
            if (descr.eq.'G') then
              qcombglo=.true.
              ncombglo=ncombglo+1
              nline=nline+1
              read(nunit,*,ERR=9998,END=9999) dcombglo
              nline=nline+1
              read(nunit,*,ERR=9998,END=9999) tcombglo
              tcombglo=tcombglo+273.15
              if (code(4:4).eq.'V'.or.code(4:4).eq.'*') then
                qmcombglo=.false.
              else if (code(4:4).eq.'M') then
                qmcombglo=.true.
              endif
            else if (descr.eq.'B'.or.descr.eq.'Z'.or.descr.eq.'P') then
              necomb=necomb+1
              descomb(necomb)=descr
              qposition=.false.
              if (descr.eq.'B') then
                nline=nline+1
!cth                read(nunit,*,ERR=9998,END=9999) idcomb(necomb)
                read(nunit,*,ERR=9998,END=9999) ibrul
                qbrulcomb(necomb)=.true.
                idcomb(necomb)=brulzone(ibrul)
                izone=idcomb(necomb)
              else if (descr.eq.'Z') then
                nline=nline+1
                read(nunit,*,ERR=9998,END=9999) idcomb(necomb)
                qbrulcomb(necomb)=.false.
                izone=idcomb(necomb)
              else if (descr.eq.'P') then
                qposition=.true.
                nline=nline+1
                read(nunit,*,ERR=9998,END=9999) position
                call get_pos_index(nzone,poszone,position,idcomb(necomb))
                izone=idcomb(necomb)
                qbrulcomb(necomb)=.false.
                qposcomb(necomb)=qposition
                poscomb(necomb)=position
                stop ' POSITION PAS ENCORE IMPLANTE '
              endif
              ifcomb(necomb)=idcomb(necomb)
              if (qdefcomb(izone)) then
                print *,' READ_BC_COMB : ENTRE COMBUS DEJA DEFINI ?? '
                print *,' LINE : ',cline(1:lline)
                print *,' CODE : ',code,lcode
                print *,' DESCR : ',descr
                print *,' No ENTREE : ',necomb
                print *,' No ZONE   : ',izone
                stop ' MIEUX VAUT S ARRETER '
              else
                qdefcomb(izone)=.true.
              endif
              if (code(4:4).eq.'F'.or.code(4:4).eq.'*') then
                qfracomb(izone)=.true.
                nline=nline+1
                read(nunit,*,ERR=9998,END=9999) fracomb(izone)
                tempcomb(izone)=tcombglo
              else if (code(4:4).eq.'D') then
                qfracomb(izone)=.false.
                nline=nline+1
                read(nunit,*,ERR=9998,END=9999) debcomb(izone)
                nline=nline+1
                read(nunit,*,ERR=9998,END=9999) tempcomb(izone)
                tempcomb(izone)=tempcomb(izone)+273.15
                if (code(5:5).eq.'V'.or.code(4:4).eq.'*') then
                  qmcomb(izone)=.false.
                else if (code(5:5).eq.'M') then
                  qmcomb(izone)=.true.
                endif
              endif
              nline=nline+1
              read(nunit,*,ERR=9998,END=9999) irepcomb(izone)
              if (irepcomb(izone).ne.0) then
                nline=nline+1
                read(nunit,*,ERR=9998,END=9999) crepcomb(izone)
              endif
            else
              print *,' READ_BC_COMB : TYPE DESCRIPTION INCONNU'
              print *,' LINE : ',cline(1:lline)
              print *,' CODE : ',code,lcode
              print *,' DESCR : ',descr
              stop ' MIEUX VAUT S ARRETER '
            endif
          else if (typeent.eq.'O') then
            if (descr.eq.'G') then
              qoxyglo=.true.
              noxyglo=noxyglo+1
              nvaleur=1
              indice=1
              nline=nline+1
              call read_real_char(nunit,maxcaroxy,nvaleur,xvaleur,nomgaz,lnomgaz,qerr)
              if (qerr) go to 9030
              if (lnomgaz.gt.0) then
                call get_index_code(nboxydant,maxcaroxy,nomoxydant,nomgaz,indice,qcgot,qerr)
                if (qerr) then
                  print *,' READ_BC_COMB : QERR : OXYDANT '
                  print *,' LNOMGAZ = ',lnomgaz
                  print *,'  NOMGAZ = ','-',nomgaz(1:lnomgaz),'-'
                  print *,'  NBOXY  = ',nboxydant,maxcaroxy
                  do iii=1,nboxydant
                    print *,'  NOMOXY = ',iii,'-',nomoxydant(iii),'-'
                  enddo
                  print *,' NEWLONGAZ = ',longcar(nomgaz)
                  go to 9040
                endif
                if (.not.qcgot) then
                  print *,' READ_BC_COMB : QCGOT : OXYDANT '
                  print *,' LNOMGAZ = ',lnomgaz
                  print *,'  NOMGAZ = ','-',nomgaz(1:lnomgaz),'-'
                  do iii=1,lnomgaz
                    print *,'  ==> ',iii,'-',nomgaz(iii:iii),'-'
                  enddo
                  print *,'  NBOXY  = ',nboxydant,maxcaroxy
                  do iii=1,nboxydant
                    print *,'  NOMOXY = ',iii,'-',nomoxydant,'-'
                  enddo
                  go to 9005
                endif
              endif
              indoxyglo=indice
              if (code(4:4).eq.'E'.or.code(4:4).eq.'*') then
                qeoxyglo=.true.
                qmoxyglo=.false.
!cth                nline=nline+1
!cth                read(nunit,*,ERR=9998,END=9999) eoxyglo
                eoxyglo=xvaleur
              else if (code(4:4).eq.'D') then
                qeoxyglo=.false.
!cth                nline=nline+1
!cth                read(nunit,*,ERR=9998,END=9999) doxyglo
                doxyglo=xvaleur
                if (code(6:6).eq.'V'.or.code(6:6).eq.'*') then
                  qmoxyglo=.false.
                else if (code(6:6).eq.'M') then
                  qmoxyglo=.true.
                endif
              endif
!ccri  'true' means that the mass flow rate is modified regarding to humidity
!ccri  'false' means that the mass flow rate is the one declared.
	      if (code(5:5).eq.'H'.or.code(5:5).eq.'*') then 
                qsoxyglo=.false.
              else
                qsoxyglo=.true.
              endif
              nline=nline+1
              read(nunit,*,ERR=9998,END=9999) toxyglo
              toxyglo=toxyglo+273.15
            else if (descr.eq.'B'.or.descr.eq.'Z'.or.descr.eq.'P') then
              neoxy=neoxy+1
              desoxy(neoxy)=descr
              if (descr.eq.'B') then
                nline=nline+1
                read(nunit,*,ERR=9998,END=9999) ibrul
                qbruloxy(necomb)=.true.
                idoxy(neoxy)=brulzone(ibrul)
                izone=idoxy(neoxy)
              else if (descr.eq.'Z') then
                nline=nline+1
                read(nunit,*,ERR=9998,END=9999) idoxy(neoxy)
                qbruloxy(necomb)=.false.
                izone=idoxy(neoxy)
              else if (descr.eq.'P') then
                qposition=.true.
                nline=nline+1
                read(nunit,*,ERR=9998,END=9999) position
                call get_pos_index(nzone,poszone,position,idoxy(neoxy))
                izone=idoxy(neoxy)
                qbruloxy(necomb)=.false.
                qposoxy(neoxy)=qposition
                posoxy(neoxy)=position
                stop ' POSITION PAS ENCORE IMPLANTE '
              endif
              ifoxy(neoxy)=idoxy(neoxy)
              if (qdefoxy(izone)) then
                print *,' READ_BC_COMB : ENTRE OXYDANT DEJA DEFINI ?? '
                print *,' LINE : ',cline(1:lline)
                print *,' CODE : ',code,lcode
                print *,' DESCR : ',descr
                print *,' No ENTREE : ',neoxy
                print *,' No ZONE   : ',izone
                stop ' MIEUX VAUT S ARRETER '
              else
                qdefoxy(izone)=.true.
              endif
              nvaleur=1
              indice=1
              nline=nline+1
              call read_real_char(nunit,maxcaroxy,nvaleur,xvaleur,nomgaz,lnomgaz,qerr)
              if (qerr) go to 9030
              if (lnomgaz.gt.0) then
                call get_index_code(nboxydant,maxcaroxy,nomoxydant,nomgaz,indice,qcgot,qerr)
                if (qerr) go to 9040
                if (.not.qcgot) go to 9005
              endif
              indoxy(izone)=indice
              if (code(4:4).eq.'F'.or.code(4:4).eq.'*') then
                qfraoxy(izone)=.true.
                qeoxy(izone)=.false.
                qsoxy(izone)=.false.
                qmoxy(izone)=.false.
!cth                nline=nline+1
!cth                read(nunit,*,ERR=9998,END=9999) fraoxy(izone)
                fraoxy(izone)=xvaleur
              else if (code(4:4).eq.'E') then
                qfraoxy(izone)=.false.
                qeoxy(izone)=.true.
                qmoxy(izone)=.false.
!cth                nline=nline+1
!cth                read(nunit,*,ERR=9998,END=9999) exeoxy(izone)
                exeoxy(izone)=xvaleur
                nline=nline+1
                read(nunit,*,ERR=9998,END=9999) tempoxy(izone)
                tempoxy(izone)=tempoxy(izone)+273.15
!ccri  'true' means that the mass flow rate is modified regarding to humidity
!ccri  'false' means that the mass flow rate is the one declared.
		if (code(5:5).eq.'H'.or.code(5:5).eq.'*') then
                  qsoxy(izone)=.false.
                else if (code(5:5).eq.'S') then
                  qsoxy(izone)=.true.
                endif
              else if (code(4:4).eq.'D') then
                qfraoxy(izone)=.false.
                qeoxy(izone)=.false.
!cth                nline=nline+1
!cth                read(nunit,*,ERR=9998,END=9999) deboxy(izone)
                deboxy(izone)=xvaleur
                nline=nline+1
                read(nunit,*,ERR=9998,END=9999) tempoxy(izone)
                tempoxy(izone)=tempoxy(izone)+273.15
!ccri  'true' means that the mass flow rate is modified regarding to humidity
!ccri  'false' means that the mass flow rate is the one declared.
                if (code(5:5).eq.'H'.or.code(5:5).eq.'*') then
                  qsoxy(izone)=.false.
                else if (code(5:5).eq.'S') then
                  qsoxy(izone)=.true.
                endif
                if (code(6:6).eq.'V'.or.code(6:6).eq.'*') then
                  qmoxy(izone)=.false.
                else if (code(6:6).eq.'M') then
                  qmoxy(izone)=.true.
                endif
              endif
            else
              print *,' READ_BC_COMB : TYPE DESCRIPTION INCONNU'
              print *,' LINE : ',cline(1:lline)
              print *,' CODE : ',code,lcode
              print *,' DESCR : ',descr
              stop ' MIEUX VAUT S ARRETER '
            endif
          else if (typeent.eq.'P') then
            if (descr.eq.'B'.or.descr.eq.'Z'.or.descr.eq.'P') then
              nepara=nepara+1
              despara(nepara)=descr
              if (descr.eq.'B') then
                nline=nline+1
                read(nunit,*,ERR=9998,END=9999) ibrul
                idpara(nepara)=brulzone(ibrul)
                izone=idpara(nepara)
              else if (descr.eq.'Z') then
                nline=nline+1
                read(nunit,*,ERR=9998,END=9999) idpara(nepara)
                izone=idpara(nepara)
              else if (descr.eq.'P') then
                qposition=.true.
                nline=nline+1
                read(nunit,*,ERR=9998,END=9999) position
                call get_pos_index(nzone,poszone,position,idpara(nepara))
                izone=idpara(nepara)
                qpospara(nepara)=qposition
                pospara(nepara)=position
                stop ' POSITION PAS ENCORE IMPLANTE '
              endif
              ifpara(nepara)=idpara(nepara)
              if (qdefpara(izone)) then
                print *,' READ_BC_COMB : ENTREE PARASITE DEJA DEFINI ?? '
                print *,' LINE : ',cline(1:lline)
                print *,' CODE : ',code,lcode
                print *,' DESCR : ',descr
                print *,' No ENTREE : ',nepara
                print *,' No ZONE   : ',izone
                stop ' MIEUX VAUT S ARRETER '
              else
                qdefpara(izone)=.true.
              endif
              if (code(4:4).eq.'D'.or.code(4:4).eq.'*') then
                nline=nline+1
                read(nunit,*,ERR=9998,END=9999) debpara(izone)
                nline=nline+1
                read(nunit,*,ERR=9998,END=9999) temppara(izone)
                temppara(izone)=temppara(izone)+273.15
                if (code(5:5).eq.'V'.or.code(5:5).eq.'*') then
                  qmpara(izone)=.false.
                else if (code(5:5).eq.'M') then
                  qmpara(izone)=.true.
                endif
                if (code(6:6).eq.'F'.or.code(6:6).eq.'*') then
                  qcomppara(izone)=.false.
                else if (code(6:6).eq.'C') then
                  qcomppara(izone)=.true.
                endif
              endif
              if (qcomppara(izone)) then
!cth   Composition massique
                nline=nline+1
                read(nunit,100,ERR=9998,END=9999) cline
                lline=longcar(cline)
                codecomp=' '
                codecomp='COMPOMAS'
                lcodecomp=longcar(codecomp)
                call get_code_inline(cline,codecomp,nvaleur,qcgot,qvgot)
                if (qcgot) then
                  if (.not.qvgot) go to 9020
                  nesppara(izone)=nvaleur
                  qmcomppara(izone)=.true.
                  if (nesppara(izone).gt.ncodesp) then
                    print *,' READ_BC_COMB : TROP D ESPECES '
                    print *,' CODE     : ',code(1:lcode)
                    print *,' CODECOMP : ',codecomp(1:lcodecomp)
                    print *,' No PARASITE : ',nepara
                    print *,' No ZONE : ',izone
                    print *,' NB VALEUR : ',nesppara(izone)
                    print *,' NB MAX. ESPECES : ',ncodesp
                    stop ' MIEUX VAUT S ARRETER '
                  endif
!cth   Lecture des differentes especes
                  nline=nline+nvaleur
                  call read_char_real(nunit,cespsize,nesppara(izone),tabesp,ltabesp,valesp,qerr)
                  if (qerr) go to 9030
!cth   Decodage nom espece et affectation
                  do iesp=1,nesppara(izone)
                    codecomp=' '
                    codecomp=tabesp(iesp)(1:ltabesp(iesp))
                    lcodecomp=longcar(codecomp)
                    call get_index_code(ncodesp,cespsize,codesp,codecomp,indice,qcgot,qerr)
                    if (qerr) go to 9040
                    if (.not.qcgot) go to 9000
                    xmaspara(indice,izone)=valesp(iesp)
                  enddo
                else
                  go to 9000
                endif
              endif
            else
              print *,' READ_BC_COMB : TYPE DESCRIPTION INCONNU'
              print *,' LINE : ',cline(1:lline)
              print *,' CODE : ',code,lcode
              print *,' DESCR : ',descr
              stop ' MIEUX VAUT S ARRETER '
            endif
          else
            print *,' READ_BC_COMB : TYPE ENTREE INCONNU'
            print *,' LINE : ',cline(1:lline)
            print *,' CODE : ',code,lcode
            print *,' TYPE : ',typeent
            stop ' MIEUX VAUT S ARRETER '
          endif
        else if (typefr.eq.'P') then
          orient=code(2:2)
          descr=code(3:3)
          if (orient.eq.'A') then
            nparoiall=nparoiall+1
            qparoiall=.true.
            if (descr.eq.'G') then
              nline=nline+1
              read(nunit,*,ERR=9998,END=9999) epsparoiall
              nline=nline+1
              read(nunit,*,ERR=9998,END=9999) hparoiall
              nline=nline+1
              read(nunit,*,ERR=9998,END=9999) tparoiall
              tparoiall=tparoiall+273.15
!cyou              nline=nline+1
!cyou              read(nunit,*,ERR=9998,END=9999) iconvparoiall
            else
              print *,' READ_BC_COMB : TYPE DESCRIPTION INCONNU'
              print *,' LINE : ',cline(1:lline)
              print *,' CODE : ',code,lcode
              print *,' DESCR : ',descr
              stop ' MIEUX VAUT S ARRETER '
            endif
          else
            nparoi=nparoi+1
            desparoi(nparoi)=descr
!cth            iindex=index('GPF',descr)
            iindex=index('GZPF',descr)
            if (iindex.eq.0) then
              print *,' READ_BC_COMB : DESCRIPTION INCONNUE'
              print *,' LINE : ',cline(1:lline)
              print *,' CODE : ',code,lcode
              print *,' DESCR : ',descr
              stop ' MIEUX VAUT S ARRETER '
            endif
            oriparoi(nparoi)=orient
            iindex=index('NSWE',orient)
            if (iindex.eq.0) then
              print *,' READ_BC_COMB : ORIENTATION INCONNUE'
              print *,' LINE : ',cline(1:lline)
              print *,' CODE : ',code,lcode
              print *,' DESCR : ',descr
              print *,' ORIENT : ',orient
              stop ' MIEUX VAUT S ARRETER '
            endif
            typparoi(nparoi)=code(4:4)
            iindex=index('IT*',typparoi(nparoi))
            if (iindex.eq.0) then
              print *,' READ_BC_COMB : TYPE PAROI INCONNU'
              print *,' LINE : ',cline(1:lline)
              print *,' CODE : ',code,lcode
              print *,' DESCR : ',descr
              print *,' ORIENT : ',orient
              print *,' TYPE   : ',typparoi(nparoi)
              stop ' MIEUX VAUT S ARRETER '
            endif
            if (descr.eq.'G') then
              qposparoi(nparoi)=.false.
            else if (descr.eq.'Z') then
              if (orient.eq.'E'.or.orient.eq.'W') then
                print *,' PAROI EST/OUEST PAR ZONE ? : ',orient,descr
                stop ' MIEUX VAUT S ARRETER '
              endif
              qposparoi(nparoi)=.false.
              nline=nline+1
              read(nunit,*,ERR=9998,END=9999) izdparoi(nparoi),izfparoi(nparoi)
              if (izdparoi(nparoi).gt.nzone.or.izfparoi(nparoi).gt.nzone) then
                print *,' PAROI IZONE>NZONE : ',orient,descr,izdparoi(nparoi),izfparoi(nparoi),nzone
                stop ' MIEUX VAUT S ARRETER '
              endif
              xdparoi(nparoi)=poszone(izdparoi(nparoi))+1.0e-4
              xfparoi(nparoi)=poszone(izfparoi(nparoi)+1)-1.0e-4
            else if (descr.eq.'P') then
              qposparoi(nparoi)=.true.
              nline=nline+1
              read(nunit,*,ERR=9998,END=9999) xdparoi(nparoi),xfparoi(nparoi)
            else if (descr.eq.'F') then
              qposparoi(nparoi)=.false.
              nline=nline+1
              read(nunit,100,ERR=9998,END=9999) cline
              lline=longcar(cline)
              call cleanline(cline,ficparoi(nparoi),lficparoi(nparoi))
            endif
            if (descr.eq.'G'.or.descr.eq.'F') then
              xdparoi(nparoi)=0.0+1.0e-4
              xfparoi(nparoi)=gridx(nx+1)-1.0e-4
            endif
            nline=nline+1
            read(nunit,*,ERR=9998,END=9999) epsparoi(nparoi)
            if (typparoi(nparoi).eq.'I'.or.typparoi(nparoi).eq.'*') then
              if (descr.ne.'F') then
                nline=nline+1
                read(nunit,*,ERR=9998,END=9999) hparoi(nparoi)
              endif
              nline=nline+1
              read(nunit,*,ERR=9998,END=9999) tparoi(nparoi)
              tparoi(nparoi)=tparoi(nparoi)+273.15
            else if (typparoi(nparoi).eq.'T'.and.descr.ne.'F') then
              nline=nline+1
              read(nunit,*,ERR=9998,END=9999) tparoi(nparoi)
              tparoi(nparoi)=tparoi(nparoi)+273.15
            endif
!cyou            nline=nline+1
!cyou            read(nunit,*,ERR=9998,END=9999) iconvparoi(nparoi)
          endif
        else if (typefr.eq.'C') then
          orient=code(2:2)
          descr=code(3:3)
          if (orient.eq.'A') then
            ncoupall=ncoupall+1
            qcoupall=.true.
            if (descr.eq.'G') then
              nline=nline+1
              read(nunit,*,ERR=9998,END=9999) epscoupall
!cyou              nline=nline+1
!cyou              read(nunit,*,ERR=9998,END=9999) iconvcoupall
            else
              print *,' READ_BC_COMB : TYPE DESCRIPTION INCONNU'
              print *,' LINE : ',cline(1:lline)
              print *,' CODE : ',code,lcode
              print *,' DESCR : ',descr
              stop ' MIEUX VAUT S ARRETER '
            endif
          endif
        else if (typefr.eq.'S') then
          descr=code(3:3)
          nsortie=nsortie+1
          dessortie(nsortie)=descr
          if (descr.eq.'Z') then
            nline=nline+1
            read(nunit,*,ERR=9998,END=9999) idsortie(nsortie)
            izone=idsortie(nsortie)
          else if (descr.eq.'P') then
            nline=nline+1
            read(nunit,*,ERR=9998,END=9999) position
            call get_pos_index(nzone,poszone,position,idsortie(nsortie))
            izone=idsortie(nsortie)
            xdsortie(nsortie)=position
          else
            print *,' READ_BC_COMB : TYPE DESCRIPTION INCONNU'
            print *,' LINE : ',cline(1:lline)
            print *,' CODE : ',code,lcode
            print *,' DESCR : ',descr
            stop ' MIEUX VAUT S ARRETER '
          endif
          if (qdefsortie(izone)) then
            print *,' READ_BC_COMB : SORTIE DEJA DEFINI ?? '
            print *,' LINE : ',cline(1:lline)
            print *,' CODE : ',code,lcode
            print *,' DESCR : ',descr
            print *,' No SORTIE : ',nsortie
            print *,' No ZONE   : ',izone
            stop ' MIEUX VAUT S ARRETER '
          else
            qdefsortie(izone)=.true.
          endif
          nline=nline+1
          read(nunit,*,ERR=9998,END=9999) dsortie(nsortie)
        else
          print *,' READ_BC_COMB : TYPE FRONTIERE INCONNU'
          print *,' LINE : ',cline(1:lline)
          print *,' CODE : ',code,lcode
          stop ' MIEUX VAUT S ARRETER '
        endif
      enddo

1000  continue

      close(unit=nunit)

!cth Verif coherence
      if (ncombglo.gt.1) then
          print *,' READ_BC_COMB : POUR COMBUSTIBLE '
          print *,' MULTIPLE DEFINITION DU DEBIT GLOBAL '
          stop ' MIEUX VAUT S ARRETER '
      endif
      if (noxyglo.gt.1) then
          print *,' READ_BC_COMB : POUR OXYDANT '
          print *,' MULTIPLE DEFINITION DU DEBIT GLOBAL '
          stop ' MIEUX VAUT S ARRETER '
      endif
      if (nparoiall.gt.1) then
          print *,' READ_BC_COMB : POUR PAROI '
          print *,' MULTIPLE DEFINITION ALL '
          stop ' MIEUX VAUT S ARRETER '
      endif
      if (ncoupall.gt.1) then
          print *,' READ_BC_COMB : POUR COUPLAGE '
          print *,' MULTIPLE DEFINITION ALL '
          stop ' MIEUX VAUT S ARRETER '
      endif
      if (ncoupall.eq.1) then
        ncouplage=1
      endif
      if (ncouplage.gt.1) then
        print *,' MULTIPLE COUPLING BOUNDARIES ?? '
        stop ' INCOHERENT BOUNDARY CONDITION '
      else if(ncouplage.ne.1.and.code_calcul.ne.CODE_FOUR_ONLY) then
        print *,' NO COUPLING ??  '
        stop ' INCOHERENT BOUNDARY CONDITION '
      else if(ncouplage.eq.1.and.code_calcul.ne.CODE_COUPLAGE) then
        print *,' COUPLING AND FURNACE ONLY ??  '
        stop ' INCOHERENT BOUNDARY CONDITION '
      endif

      if (necomb.eq.0) then
        print *,' PAS DE COMBUS ?????  '
        stop ' MIEUX VAUT S ARRETER '
      endif
      if (noxyglo.eq.0.and.neoxy.eq.0) then
        print *,' PAS D OXYDANT ?????  '
        stop ' MIEUX VAUT S ARRETER '
      endif

      if (nparoiall.eq.1) then
        if(code_calcul.ne.CODE_FOUR_ONLY) then
          nparoi=4-ncouplage
        else 
          nparoi=4
        endif
        do i=1,nparoi
          desparoi(i)='G'
          typparoi(i)='I'
          qposparoi(i)=.false.
          epsparoi(i)=epsparoiall
          hparoi(i)=hparoiall
          tparoi(i)=tparoiall
        enddo
        oriparoi(1)='W'
        oriparoi(2)='N'
        oriparoi(3)='E'
        if(nparoi.eq.4) oriparoi(4)='S'
        xdparoi(1)=0.0
        xfparoi(1)=0.0
        xdparoi(2)=0.0+1.0e-4
        xfparoi(2)=gridx(nx+1)-1.0e-4
        xdparoi(3)=gridx(nx+1)
        xfparoi(3)=gridx(nx+1)
        if(nparoi.eq.4) then
          xdparoi(4)=0.0+1.0e-4
          xfparoi(4)=gridx(nx+1)-1.0e-4
        endif
      endif
      if (nparoi.eq.0) then
        print *,' PAS PAROI ??  '
        stop ' MIEUX VAUT S ARRETER '
      endif
      do i=1,nparoi
        call get_pos_index(nx,gridx,xdparoi(i),idparoi(i))
        call get_pos_index(nx,gridx,xfparoi(i),ifparoi(i))
      enddo



      if (ires(1).ge.1) then

      if (ncombglo.ne.0) then
        print *,' COMBUS GLOBAL : ',dcombglo,tcombglo
      endif
      if (necomb.ne.0) then
        do i=1,necomb
          print *,' COMBUS ENTREE : ',i,descomb(i),idcomb(i),ifcomb(i)
          iz=idcomb(i)
          print *,' --> ZONE : ',iz
          print *,' --> : ',fracomb(iz),debcomb(iz),tempcomb(iz),irepcomb(iz),crepcomb(iz)
        enddo
      endif 
      if (noxyglo.ne.0) then
        print *,' OXYDANT GLOBAL : ',qeoxyglo,doxyglo,eoxyglo,toxyglo
      endif
      if (neoxy.ne.0) then
        do i=1,neoxy
          print *,' OXYDANT ENTREE : ',i,desoxy(i),idoxy(i),ifoxy(i)
          iz=idoxy(i)
          print *,' --> ZONE : ',iz
          print *,' --> : ',fraoxy(iz),deboxy(iz),tempoxy(iz),irepoxy(iz),crepoxy(iz)
        enddo
      endif
      if (nsortie.ne.0) then
        print *,' SORTIE : ',nsortie
        do i=1,nsortie
          print *,i,idsortie(i),xdsortie(i),dsortie(i)
        enddo
      else 
        print *,' SORTIE PAR DEFAUT en NX : ',nx,gridx(nx+1),cellx(nx)
      endif
      if (ncoupall.ne.0) then
        print *,' COUPLAGE ALL : '
!cyou        print *,' --> : ',epscoupall,iconvcoupall
      endif
      if (ncouplage.eq.1) then
        print *,' COUPLAGE : ',ncouplage
      endif

      if (nparoiall.eq.1) then
        print *,' PAROI ALL : '
!cyou        print *,' --> : ',epsparoiall,hparoiall,tparoiall,iconvparoiall
      endif
      if (nparoi.ne.0) then
        print *,' PAROI : ',nparoi
        do i=1,nparoi
          print *,i,oriparoi(i),desparoi(i),typparoi(i),idparoi(i),ifparoi(i),epsparoi(i),hparoi(i),tparoi(i)
          if (desparoi(i).eq.'F') then
            print *,' --> FILE : ',ficparoi(i)
          endif
        enddo
      endif 

      endif  !ires(1)

!cth Verif coherence sur bruleurs et zone

      if (necomb.ne.0) then
        descr=descomb(1)
        do i=1,necomb
          if (descr.ne.descomb(i)) then
            print *,' COMBU DESCRIPTION MULTIPLE ZONE ET BRULEUR  '
            stop ' MIEUX VAUT S ARRETER '
          endif
        enddo
!cth On peut avoir envie d un calcul avec un bruleur en panne
!cth        if (descr.eq.'B'.and.necomb.ne.nposbrul) then
!cth            print *,' COMBU NECOMB#NPOSBRUL : ',descr,necomb,nposbrul
!cth            stop ' MIEUX VAUT S ARRETER '
!cth        endif
        if (descr.eq.'B'.and.necomb.gt.nposbrul) then
            print *,' COMBU NECOMB>NPOSBRUL : ',descr,necomb,nposbrul
            stop ' MIEUX VAUT S ARRETER '
        endif
        if (descr.eq.'Z'.and.necomb.gt.nzone) then
            print *,' COMBU NECOMB>NZONE : ',descr,necomb,nzone
            stop ' MIEUX VAUT S ARRETER '
        endif
      endif
      if (neoxy.ne.0) then
        descr=desoxy(1)
        do i=1,neoxy
          if (descr.ne.desoxy(i)) then
            print *,' OXY DESCRIPTION MULTIPLE ZONE ET BRULEUR  '
            stop ' MIEUX VAUT S ARRETER '
          endif
        enddo
!cth On peut avoir envie d un calcul avec un bruleur en panne
!cth        if (descr.eq.'B'.and.neoxy.ne.nposbrul) then
!cth            print *,' OXY NEOXY#NPOSBRUL : ',descr,neoxy,nposbrul
!cth            stop ' MIEUX VAUT S ARRETER '
!cth        endif
        if (descr.eq.'B'.and.neoxy.gt.nposbrul) then
            print *,' OXY NEOXY>NPOSBRUL : ',descr,neoxy,nposbrul
            stop ' MIEUX VAUT S ARRETER '
        endif
        if (descr.eq.'Z'.and.neoxy.gt.nzone) then
            print *,' OXY NEOXY>NZONE : ',descr,neoxy,nzone
            stop ' MIEUX VAUT S ARRETER '
        endif
      endif

!cth TOUT CE QUI SUIT
!cth A FAIRE DANS ENTREE_COMBUSTIBLE ET ENTREE_OXYDANT
!cth Calcul debit locaux si debit global
      if (qcombglo) then
        do i=1,necomb
          iz=idcomb(i)
          debcomb(iz)=fracomb(iz)*dcombglo
        enddo
      endif
      if (qoxyglo.and..not.qeoxyglo) then
        do i=1,neoxy
          iz=idoxy(i)
          deboxy(iz)=fraoxy(iz)*doxyglo
        enddo
      endif

!cth Calcul debit global si debit entree par entree
!cth et Calcul fraction de repartition
      if (.not.qcombglo) then
        dcombglo=0.0
        do i=1,necomb
          iz=idcomb(i)
          dcombglo=dcombglo+debcomb(iz)
        enddo
        do i=1,necomb
          iz=idcomb(i)
          fracomb(iz)=debcomb(iz)/dcombglo
        enddo
      endif
      if (.not.qoxyglo) then
        doxyglo=0.0
        do i=1,neoxy
          iz=idoxy(i)
          doxyglo=doxyglo+deboxy(iz)
        enddo
        do i=1,neoxy
          iz=idoxy(i)
          fraoxy(iz)=deboxy(iz)/doxyglo
        enddo
      endif
!cth Verif debit global si debit global et repartition
!cth et Calcul fraction de repartition
      if (qcombglo) then
        verdebcomb=0.0
        do i=1,necomb
          iz=idcomb(i)
          verdebcomb=verdebcomb+debcomb(iz)
        enddo
        verfracomb=0.0
        do i=1,necomb
          iz=idcomb(i)
          verfracomb=verfracomb+fracomb(iz)
        enddo
        if (abs(1.0-verfracomb).gt.5.0e-3) then
          print *,' READ_BC_COMB : COMBUSTIBLE '
          print *,' PROBLEME REPARTITION DEBIT GLOBAL '
          print *,' SOMME REPART : ',verfracomb
          print *,' DEBIT GLOBAL : ',dcombglo
          print *,' DEBIT REEL   : ',verdebcomb
          print *,' DEBIT ERREUR   : ',dcombglo-verdebcomb,abs(dcombglo-verdebcomb)/dcombglo
          print *,' CORRECTION AUTOMATIQUE ?? '
        endif
      endif
      if (qoxyglo.and..not.qeoxyglo) then
        verdeboxy=0.0
        do i=1,neoxy
          iz=idoxy(i)
          verdeboxy=verdeboxy+deboxy(iz)
        enddo
        verfraoxy=0.0
        do i=1,neoxy
          iz=idoxy(i)
          verfraoxy=verfraoxy+fraoxy(iz)
        enddo
        if (abs(1.0-verfraoxy).gt.5.0e-3.and.neoxy.ne.0) then
          print *,' READ_BC_COMB : OXYDANT '
          print *,' PROBLEME REPARTITION DEBIT GLOBAL '
          print *,' SOMME REPART : ',verfraoxy
          print *,' DEBIT GLOBAL : ',doxyglo
          print *,' DEBIT REEL   : ',verdeboxy
          print *,' DEBIT ERREUR   : ',doxyglo-verdeboxy,abs(doxyglo-verdeboxy)/doxyglo
          print *,' CORRECTION AUTOMATIQUE ?? '
        endif
      endif

      return

9000  CONTINUE
!cth Erreur code
      PRINT 200,' Code non trouve : ',code(1:lcode),filename(1:lname)
200   format(a,10(1x,a))
      PRINT *,' Line : ',nline
      STOP ' MIEUX VAUT S ARRETER '

9005  CONTINUE
!cth Erreur nom oxydant non trouve
      PRINT 200,' Nom oxydant non trouve : ',code(1:lcode),filename(1:lname)
      PRINT *,' Line : ',nline
      STOP ' MIEUX VAUT S ARRETER '
9010  CONTINUE
!cth Erreur nvaleur
      PRINT 300,' Nombre de valeur ?? : ',nvaleur,code(1:lcode),filename(1:lname)
300   format(a,1x,i3,10(1x,a))
      PRINT *,' Line : ',nline
      STOP ' MIEUX VAUT S ARRETER '

9020  CONTINUE
!cth Erreur nvaleur non trouve
      PRINT 300,' Nombre de valeur non trouve : ',nvaleur,code(1:lcode),filename(1:lname)
      PRINT *,' Line : ',nline
      STOP ' MIEUX VAUT S ARRETER '

9030  CONTINUE
!cth Erreur lecture valeurs
      PRINT 200,' Erreur lecture valeur : ',code(1:lcode),filename(1:lname)
      PRINT *,' Line : ',nline
      STOP ' MIEUX VAUT S ARRETER '

9040  CONTINUE
!cth Erreur longueur de chaine trop petite
      PRINT 300,' Longueur de chaine trop petite : ',nvaleur,code(1:lcode),filename(1:lname)
      STOP ' MIEUX VAUT S ARRETER '

9997  CONTINUE
!cth Erreur ouverture fichier
      PRINT *,' Erreur ouverture fichier : ',filename(1:lname)
      STOP ' MIEUX VAUT S ARRETER '

9998  CONTINUE
!cth Erreur lecture
      PRINT *,' Erreur lecture sur : ',filename(1:lname)
      PRINT 200,' Code : ',code(1:lcode)
      PRINT *,' Line : ',nline
      STOP ' MIEUX VAUT S ARRETER '

9999  CONTINUE
!cth Fin de fichier
      PRINT *,' Fin de fichier sur : ',filename(1:lname)
      PRINT 200,' Code : ',code(1:lcode)
      PRINT *,' Line : ',nline
      STOP ' MIEUX VAUT S ARRETER '

      end
