      SUBROUTINE solve_init_comb(itercoup)

!cth Variables globales
      USE modTwoDRad
      USE modBilan
      USE modfacteur
      USE modcodefour
      USE modshared
      USE modespece
      USE modlim
	
      IMPLICIT NONE

      INTEGER :: ITELU,NTOTLU
!cth Arguments
      INTEGER :: itercoup

!cth Parameter
      REAL,PARAMETER :: SMALL=1.0e-6
      REAL,PARAMETER :: criterhaut=1.0e-3

!cth Variables locales
      INTEGER :: longcar
      INTEGER :: I,J
      REAL :: CALC1

      integer :: lname
      character*130 filename

!cth Impression
      INTEGER ires(3)
      COMMON/impr/ires

      ALLOCATE(dimen(NX))
      ALLOCATE (LARGFOUR(NX))
      ALLOCATE (HAUTFOUR(NX))
      ALLOCATE (HAUT2(Nx+1))
      ALLOCATE (LARGSOLE(NX))
      haut2=0.
      hautfour=0.

!
!cth DEVRAIT Y AVOIR ROUTINE ALLOCATION SEPAREE
! Definition des tableaux
!------------------------


!cth
!cth Allocation des tableaux anciennement commun aux 2 fours pour REHEAT1D
!cth ==> anciennement declares a 2*NX
!cth

      ALLOCATE(YN(NX))
      ALLOCATE(YH2O(NX))
      ALLOCATE(YO(NX))
      ALLOCATE(YCO2(NX))
      ALLOCATE(YF(NX))
      ALLOCATE(YAr(NX))
      ALLOCATE(YCO(NX))
      ALLOCATE(Tgi1(NX))
      ALLOCATE(Tgi2(NX))
      ALLOCATE(Tgi21(NX))
      ALLOCATE(puisstr(NX))
      ALLOCATE(puissrad(NX))
      ALLOCATE(puissradanc(NX))

!-----------------------------------------------------------------------
! Allocation des variables relative a la geometrie
!-----------------------------------------------------------------------
      ALLOCATE(INTERDIT(NX,NY))
      ALLOCATE(INTERIOR(NX,NY))

      DO i=1,nx
        dimen(i)= gridx(i+1)-gridx(i)
        dx(i)= gridx(i+1)-gridx(i)
        dxequiv(i)=dx(i)
        x(i)=0.5*(gridx(i)+gridx(i+1))
      ENDDO

      if(nlargload.ne.0.and.nlargload.ne.nlargfour.and.ires(1).eq.1) then
        print*,'le Nombre de sections de largeurs differentes',&
     &    ' n est pas egale sur la charge et la voute'
      endif

!cth Calcul au maille des largeurs et hauteurs
      CALL set_geom_cell(nhautfour,xhautfour,vhautfour,&
     &    lxmax,lymax,nx,gridx,hautfour)
      CALL set_geom_cell(nlargfour,xlargfour,vlargfour,&
     &    lxmax,lzmax,nx,gridx,largfour)
      CALL set_geom_cell(nlargload,xlargload,vlargload,&
     &    lxmax,lzmax,nx,gridx,largsole)


!cth Calcul hauteur en chaque noeud
      rectangulaire=.true.
      do i=1,nx
        haut2(i)=hautfour(i)
        if (abs((haut2(i+1)-haut2(i))/haut2(i)).gt.criterhaut) then
          rectangulaire=.false.
        endif
      enddo
      haut2(nx+1)=haut2(nx)

      ALLOCATE (surfnord(NX))
      surfnord=0.0

      DO I=1,NX
        CALC1=cos(atan(ABS((HAUT2(I+1)-HAUT2(I))/(GRIDX(I+1)-GRIDX(I)+small))))
        SURFNORD(I)=(GRIDX(I+1)-GRIDX(I))/CALC1
      ENDDO

!cth Echange facteur de forme pour le four
      if (code_calcul.ne.CODE_FOUR_ONLY.and.code_charge.ne.CODE_GTM) then
        call get_interf_field(MODU_CHARGE,mycode_module,CHAMP_FRINC,frayinci,1)
        call get_interf_field(MODU_CHARGE,mycode_module,CHAMP_FRSPE,frayspec,1)
        call get_interf_field(MODU_CHARGE,mycode_module,CHAMP_FCONV,fconvect,1)
      endif

      ALLOCATE (NCOEFSUD(NX))
      ALLOCATE (NCOEFNORD(NX))
      ALLOCATE (COEFSUD(ncoefmax,NX))
      ALLOCATE (COEFNORD(ncoefmax,NX))
      ALLOCATE (typecondsud(NX))
      ALLOCATE (typecondnord(NX))
      ALLOCATE (tinfsud(NX))
      ALLOCATE (tinfnord(NX))
      ALLOCATE (hinfsud(NX))
      ALLOCATE (hinfnord(NX))
      ALLOCATE (fimpsud(NX))
      ALLOCATE (fimpnord(NX))
      ALLOCATE (Ivarsud(NX))
      ALLOCATE (Ivarnord(NX))
      ALLOCATE (epsv(NX))
      ALLOCATE (epsp(NX))

      COEFSUD=0.
      COEFNORD=0.

      ALLOCATE (Zonegaz(NX,NY))
      ALLOCATE (pos(NX+1))
      ALLOCATE (YBN(NX))
      ALLOCATE (YBH2O(NX))
      ALLOCATE (YBO(NX))
      ALLOCATE (YBCO2(NX))
      ALLOCATE (YBCO(NX))
      ALLOCATE (YBAr(NX))
      ALLOCATE (YBF(NX))
      ALLOCATE (yNDEG(NX))
      ALLOCATE (yH2ODEG(NX))
      ALLOCATE (yODEG(NX))
      ALLOCATE (yCO2DEG(NX))
      ALLOCATE (yCODEG(NX))
      ALLOCATE (yArDEG(NX))
      ALLOCATE (yFDEG(NX))
      ALLOCATE (DEG(NX))
      ALLOCATE (HDEG(NX))
      ALLOCATE (SOR(NX))
      ALLOCATE (DP(0:NX+1))
      ALLOCATE (WMfum(NX))
      ALLOCATE (DR(0:NX+1))
      ALLOCATE (HGAZ(0:NX+1))
      ALLOCATE (HGAZ1(0:NX+1))
      ALLOCATE (HGAZ2(0:NX+1))
      ALLOCATE (RHOGAZ(NX))
      ALLOCATE (VISGAZ(NX))
      ALLOCATE (CPGAZ(NX))
      ALLOCATE (DEBITGLOB(NX))
      ALLOCATE (DEBITCONV(NX))
      ALLOCATE (VITCONV(NX))
      ALLOCATE (vitflame(NX))  !ccri
      ALLOCATE (fracomb_grid(NX))  !ccri
      ALLOCATE (longzone_grid(NX))  !ccri
      dp=0.0
      dr=0.0
      hgaz=0.0
      hgaz1=0.0
      hgaz2=0.0
      rhogaz=0.0
      visgaz=0.0
      cpgaz=0.0
      ALLOCATE (dPrsdT(NX))
      ALLOCATE (dPrsdTanc(NX))
      puisstr=0.0
      puissrad=0.0
      puissradanc=0.0
      ALLOCATE(mb(NX))
      ALLOCATE(WMbrul(NX))
      ALLOCATE (TBR(NX))
      ALLOCATE (HBR(NX))
      ALLOCATE(puissbruz(nlongzone))
      ALLOCATE(puisspci(nlongzone))
      ALLOCATE(puissrech(nlongzone))
      ALLOCATE(puissparoiz(nlongzone))
      ALLOCATE(puissloadz(nlongzone))

      ALLOCATE (fradpsanc(NX))
      ALLOCATE (fradpnanc(NX))
      ALLOCATE (tps1(NX))
      ALLOCATE (tps2(NX))
      ALLOCATE (TpN1(NX))
      ALLOCATE (TpN2(NX))
      ALLOCATE (tps21(NX))
      ALLOCATE (TpN21(NX))
      ALLOCATE (tps1anc(NX))
      tps1=0.0
      tps2=0.0
      tps1anc=0.0
      ALLOCATE (fradpn(NX))
      ALLOCATE (ftotps(NX))
      ALLOCATE (fccpn(NX))
      ALLOCATE (ftotpn(NX))
      ALLOCATE (hccpn(NX))
      ALLOCATE(fradps(NX))
      ALLOCATE(fccps(NX))
      ALLOCATE (hccps(NX))
      ALLOCATE (fcctr(NX))
      ALLOCATE (surfconvpn(NX))
      ALLOCATE (surfconvps(NX))

      ftotps=0.0
      POS=0.

!cth Maillage rayonnement

      CALL set_dim_meshes(NX,ny,hautfour,&
     &     nang,nxp1,nxp2,nyp1,nyp2,np,nbrayon,interdit,&
     &     interior,RECTANGULAIRE,HAUT2,GRIDY,&
     &     CONTROLMAILLRAYON,nbrayon4,ang1,raison)

      ALLOCATE (theta(nang+1))
      ALLOCATE (dtheta(nang+1))
      ALLOCATE (alpha(nbrayon+1))
      ALLOCATE (dalpha(nbrayon+1))
      ALLOCATE (tsi(nbtsi))
      ALLOCATE (dtsi(nbtsi))
      ALLOCATE (xyp(np,2))
      ALLOCATE (gamma(np))
      ALLOCATE (coin(np,2))
      ALLOCATE (surf(np))
      ALLOCATE (vol(NX,ny))
      ALLOCATE (direc(np))
      ALLOCATE (emis(np))
      ALLOCATE (xycoin(np,2))

      CALL set_angular_meshes (NX,ny,nxp1,nxp2,nyp1,&
     &     nyp2,np,nang,nbrayon,nbtsi,tsi,dtsi,&
     &     interdit,&
     &     theta,dtheta,alpha,dALPHA,&
     &     gridx,gridy,x,y,dx,dy,&
     &     xyp,xycoin,gamma,coin,direc,&
     &     surf,RECTANGULAIRE,&
     &     CONTROLMAILLRAYON,nbrayon4,ang1,raison,&
     &     dxequiv,hautfour,largfour)

      surfest=hautfour(NX)
      surfouest=hautfour(1)
      surfconvpn=dxequiv*(largfour+2.0*hautfour)
      surfconvps=dx*(largfour*fconvect)
      surfconvpw=surfouest*Largfour(1)
      surfconvpe=surfest*Largfour(nx)

!cth Reprise eventuelle si couplage
      if (code_calcul.ne.CODE_FOUR_ONLY) then
        redem=restart
      endif

!cth
!cth Initialisation des temperatures OU de tout les champs si reprise
!cth

      IF (redem.eq.0) THEN
        call init_temp_comb(namefour(myindfour),lnamefour(myindfour),nx,x,&
     &    ctempini,tginideb,tginifin,tpninideb,tpninifin,tpwini,tpeini,&
     &    Tgi1,Tpn1,Tpw1,Tpe1)
      ELSE IF (redem.eq.1) THEN
	      filename=casename(1:lcasename)//'_'//moduname(code_max)(1:longcar(moduname(code_max)))//'.rest'
        OPEN(unit=14,file=filename)
		READ(14,*)
        READ(14,*)itelu,ntotlu,indglob0
        READ(14,*)
        IF (ITELU.NE.itercoup) THEN
          PRINT *,' REPRISE FOUR ITERATIONS INCOHERENTES ; ',ITELU,itercoup
          STOP ' MIEUX VAUT S ARRETER '
        ENDIF
        IF (NTOTLU.NE.NX) THEN
          PRINT *,' REPRISE FOUR NB VALEURS INCOHERENTES ; ',NTOTLU,NX
          STOP ' MIEUX VAUT S ARRETER '
        ENDIF
        DO i = 1, nx
          READ(14,*) j,tgi1(i),tpn1(i),tps1(i),tpw1,tpe1,fradpn(i),fradps(i),fradpw,fradpe,puissrad(i),ftotps(i)
        END DO
        CLOSE(14)
      ELSE
        STOP ' SOLVE_INIT_COMB : REDEM NI 0 NI 1 '
      ENDIF

!cth Combustible et oxydant
      CALL compute_fuel(xcombmol,&
     &     pcisto,r_o2_flu,r_co2_flu,r_h2o_flu,&
     &     pcivol,volair,volfuel,&
     &     qcompo,qstoechio,qvolume)

!cth Conditions aux limites
      call read_bc_comb(namefour(myindfour),lnamefour(myindfour),&
     &  nbzonesmax,nx,nposbrul,nbzones,&
     &  gridx,x,&
     &  poszone,brulzone,&
     &  maxoxydant,nboxydant,maxcaroxy,&
     &  nomoxydant)

      CALL set_burners_inlet (nbzonesmax,NX,mb,Tbr,ybN,&
     &  ybH2O,ybO,ybCO2,ybAr,ybF,GRIDX,WMbrul,dimen,&
     &  qzone,nlongzone,longzone,&
     &  indmaille,nbtranchzone,mbruleurz,mcomb,&
     &  hbr,puissbrul,puissbruz,puisspci,&
     &  nposbrul,brulzone,&
     &  qdefent,qdefsor,&
     &  ncombesp,noxyesp,&
     &  maxoxydant,nboxydant,&
     &  xcombmol,xcombmas,xoxymol,xsoxymol,xoxymas,xsoxymas,&
     &  humid,thumid,oxymas,oxysmas,&
     &  dvcombreal,dvcombglo,&
     &  dvoxyreal,dvoxyglo,dvsoxyreal,dvsoxyglo,exoxyglo,&
     &  dvfumeereal,dvfumeeglo,&
     &  qoxydant,qhumid,qoxymas,fracomb_grid,longzone_grid)

      CALL set_boundary_condition(NX,NY,tinfsud,tinfnord,epsv,epsp,typecondsud,&
     &  typecondnord,typecondouest,typecondest,&
     &  COEFSUD,COEFNORD,Ivarsud,IvarNord,&
     &  hinfouest,hinfest,tinfouest,tinfest,&
     &  nxp1,nyp1,nxp2,nyp2,np,emis,&
     &  interdit,RECTANGULAIRE,ncoefsud,ncoefnord,ncoefmax,&
     &  x,tpn1,tps1,tpw1,tpe1)

      yN= 0.
      yCO2= 0.
      yO= 0.
      yAr= 0.
      yF= 0.
      yH2O= 0.
      yCO= 0.
      yNDEG= 0.
      yCO2DEG= 0.
      yODEG= 0.
      yH2ODEG= 0.
      yArDEG= 0.
      yFDEG=0.

      SOR=0.
      CALL set_side_outlets(NX,SOR,nlongzone,INDMAILLE,DIMEN,LONGZONE,&
     &     NBTRANCHZONE)

      DR=0.
      DEG=0.
      CALL set_reversed_gas(namefour(myindfour),lnamefour(myindfour),nx,x,dr,crecirc,valrecirc)

!cth Bilan massique
      CALL solve_mass_balance(NX,mb,DR,SOR,DEG,ybN,ybO,ybCO2,ybH2O,ybAr,&
     &     ybF,DP,yN,yO,yCO2,yH2O,yAr,yF,yNDEG,yODEG,yCO2DEG,yH2ODEG,&
     &     yArDEG,yFDEG,debitglob,debitconv,xfumeemol,WMfum)

!cth Ecriture exemple de fichier .ini .rec
!cth      call write_example_comb(nx,x,tgi1,tpn1,tps1,dr,iecrini,iecrprof)
      call write_example_comb()

      tps1anc=tps1

      indglobt=indglob0+indglob

!cth Entete fichier de suivi de convergence
      if (ires(1).ge.1) then

	      filename=casename(1:lcasename)//'_'//moduname(code_max)(1:longcar(moduname(code_max)))//'.cvg_max'
        if (restart.eq.0) then
          OPEN(UNIT=19,FILE=filename)
        else
          OPEN(UNIT=19,FILE=filename,POSITION='APPEND')
        endif
        write(19,100) ' URF TG:0.1 TN:0.1 TS:0.1 TO:0.1 TE:0.1 FN:0.1 FS:0.1 FO:0.1 FE:0.1 PR:0.1 HG:0.1'
100     format(1x,a)
        WRITE(19,100) ' Iter   Tgaz   Tnord   TSUD   TOUEST   TEST   PRAD    FNORD   FSUD   FOUEST   FEST   RESH'
        CLOSE(19)

	      filename=casename(1:lcasename)//'_'//moduname(code_max)(1:longcar(moduname(code_max)))//'.cvg_sum'
        if (restart.eq.0) then
          OPEN(UNIT=19,FILE=filename)
        else
          OPEN(UNIT=19,FILE=filename,POSITION='APPEND')
        endif
        write(19,100) ' URF TG:0.1 TN:0.1 TS:0.1 TO:0.1 TE:0.1 FN:0.1 FS:0.1 FO:0.1 FE:0.1 PR:0.1 HG:0.1'
        WRITE(19,100) ' Iter   Tgaz   Tnord   TSUD   TOUEST   TEST   PRAD    FNORD   FSUD   FOUEST   FEST   RESH'
        CLOSE(19)

	      filename=casename(1:lcasename)//'_'//moduname(code_max)(1:longcar(moduname(code_max)))//'.cvg_ind'
        if (restart.eq.0) then
          OPEN(UNIT=19,FILE=filename)
        else
          OPEN(UNIT=19,FILE=filename,POSITION='APPEND')
        endif
        write(19,100) ' URF TG:0.1 TN:0.1 TS:0.1 PR:0.1 FN:0.1 FS:0.1'
        WRITE(19,100) ' Iter   Igaz   Inord   ISUD   Irad    INORD   ISUD'
        CLOSE(19)

      endif

      return

9997  CONTINUE
!cth Erreur ouverture fichier
      PRINT *,' Erreur ouverture fichier : ',filename(1:lname)
      STOP ' MIEUX VAUT S ARRETER '

9998  CONTINUE
!cth Erreur lecture
      PRINT *,' Erreur lecture sur : ',filename(1:lname)
      STOP ' MIEUX VAUT S ARRETER '

      END
