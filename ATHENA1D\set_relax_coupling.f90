      SUBROUTINE set_relax_coupling(ITER)

      USE modcouplage
      USE modshared

      IMPLICIT NONE

      INTEGER :: ITER

      REAL :: DRELAX
      INTEGER :: ITERLOC

      ITERLOC=ITER+1

      IF (code_calcul.eq.CODE_COUPLAGE) THEN

      IF (ITERLOC.GE.ITMINRELAX.AND.ITERLOC.LT.NBITERELAX) THEN
        DRELAX=(RELAXCFDON-RELAXCFMIN)*FLOAT(ITERLOC-ITMINRELAX)/FLOAT(NBITERELAX-ITMINRELAX)
        RELAXCFLUX=RELAXCFMIN+DRELAX
        DRELAX=(RELAXCTDON-RELAXCTMIN)*FLOAT(ITERLOC-ITMINRELAX)/FLOAT(NBITERELAX-ITMINRELAX)
        RELAXCTEMP=RELAXCTMIN+DRELAX
        if (tracecouplage.ge.1) then
          PRINT 100,' Coupling evolutive relax. : iter,relaxt,relaxf = ',iter,relaxctemp,relaxcflux
        endif
      ELSE IF (ITERLOC.GE.NBITERELAX) THEN
        RELAXCFLUX=RELAXCFDON
        RELAXCTEMP=RELAXCTDON
        if (tracecouplage.ge.1) then
          IF (ITERLOC.EQ.NBITERELAX) THEN
            PRINT 100,' Coupling max relax. : iter,relaxt,relaxf = ',iter,relaxctemp,relaxcflux
          ENDIF
        endif
      ELSE IF (ITERLOC.LE.ITMINRELAX) THEN
!cth        RELAXCFLUX=RELAXCFDON
!cth        RELAXCTEMP=RELAXCTDON
        RELAXCFLUX=RELAXCFMIN
        RELAXCTEMP=RELAXCTMIN
      ENDIF

      ELSE
        RELAXCFLUX=1.0
        RELAXCTEMP=1.0
      ENDIF

      if (tracecouplage.ge.1) then
        PRINT 100,' Coupling Relax. : iter,relaxt,relaxf = ',iter,relaxctemp,relaxcflux
      endif
      PRINT *,' '

100   FORMAT (A,i5,2f7.3)
      END
