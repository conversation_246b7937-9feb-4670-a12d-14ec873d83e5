
#ifdef Windows_NT

      SUBROUTINE SHOW_CONV_GRAPH(ite,dif,emax,imx,tbridge)


      USE DFPORT
      USE MSFLIB

      INTEGER ITE
      REAL DIF,EMAX
      INTEGER IMX
      REAL TBRIDGE

      INTEGER IL
      INTEGER*2 DUMMY2
      RECORD / RCCOORD / CURSOR
      CHARACTER*30 label

!C>        ** Show convergence on graphic screen **
        label=' '
        il = 2
        DUMMY2 = SETTEXTCOLOR (11)
        CALL SETTEXTPOSITION (il,1,CURSOR)
        CALL OUTTEXT ('Iteration   = ')
        CALL SETTEXTPOSITION (il,15,CURSOR)
        WRITE (label,'(I3)') ite
        CALL OUTTEXT (label)
        il = il + 1
        CALL SETTEXTPOSITION (il,1,CURSOR)
        CALL OUTTEXT ('Mean Error  = ')
        CALL SETTEXTPOSITION (il,15,CURSOR)
        WRITE (label,'(E8.2E2)') dif
        CALL OUTTEXT (label)
        il = il + 1
        CALL SETTEXTPOSITION (il,1,CURSOR)
        CALL OUTTEXT ('Maxi Error  = ')
        CALL SETTEXTPOSITION (il,15,CURSOR)
        WRITE (label,'(E8.2E2)') emax
        CALL OUTTEXT (label)
        CALL SETTEXTPOSITION (il,24,CURSOR)
        WRITE (label,'(A5,I4)') 'at i=',imx
        CALL OUTTEXT (label)
        il = il + 1
        CALL SETTEXTPOSITION (il,1,CURSOR)
        CALL OUTTEXT ('Tbridgewall = ')
        CALL SETTEXTPOSITION (il,15,CURSOR)
        WRITE (label,'(F7.2,A4)') tbridge,' oC'
        CALL OUTTEXT (label)

        END

#else
      SUBROUTINE SHOW_CONV_GRAPH(ite,dif,emax,imx,tbridge)

      CONTINUE

      END
#endif
