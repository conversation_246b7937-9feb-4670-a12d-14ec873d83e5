    	 subroutine write_geo_comb(basefour,lbasefour,&
     &     lxmax,lymax,lzmax,nxnoeud,nynoeud,nzone,nposbrul,&
     &     maxhaut,nlargfour,nhautfour,&
     &     xlargfour,xhautfour,&
     &     vlargfour,vhautfour,&
     &     maxpointx,gridx,maxpointy,gridy,&
     &     maxzone,qzone,longzone,qbrul,posbrul,&
     &     qnxzone,qdxzone,nxzone,dxzone,qnxbrul,qdxbrul,nxbrul,dxbrul)
!****************************************************************************
!
!	Ecriture du fichier .geo d un four
!		
!****************************************************************************

      USE modshared
 
      IMPLICIT NONE
!
! Declaration des variables
!--------------------------
      CHARACTER*(*) basefour
      INTEGER :: lbasefour
      real :: lxmax,lymax,lzmax
      integer :: nxnoeud,nynoeud,nzone,nposbrul
      integer :: maxhaut,nlargfour,nhautfour
      real :: xlargfour(maxhaut),xhautfour(maxhaut)
      real :: vlargfour(maxhaut),vhautfour(maxhaut)
      integer :: maxpointx,maxpointy
      real :: gridx(maxpointx),gridy(maxpointy)
      integer :: maxzone
      logical :: qzone,qbrul
      real :: longzone(maxzone),posbrul(maxzone)
      integer :: nxzone(maxzone),nxbrul(maxzone)
      real :: dxzone(maxzone),dxbrul(maxzone)
      logical :: qnxzone,qdxzone
      logical :: qnxbrul,qdxbrul
      logical :: inside

      INTEGER :: longcar
      CHARACTER*130 filename
      INTEGER :: lname
      INTEGER :: nunit, nunitin, nunitout
      CHARACTER*130 cline
      INTEGER :: lline
      CHARACTER*20 code
      INTEGER :: lcode
      INTEGER :: i

      INTEGER :: ires(3)
      COMMON/impr/ires

!
! Debut du programme
!-------------------
      if(ires(1).ge.1)then
  10  format(a130)
      nunitout=98
      filename=' '
      filename=basefour(1:lbasefour)//'.geo_ori'
      lname=longcar(filename)
      open(unit=nunitout,file=filename,ERR=9997)
      nunitin=99
      filename=' '
      filename=basefour(1:lbasefour)//'.igeo'
      lname=longcar(filename)
      open(unit=nunitin,file=filename,ERR=9997)
	  inside = .true.
      do while(inside) 
        read(nunitin,10,END=50) cline
        write(nunitout,10) cline
      enddo
  50  close(nunitin)
	  close(nunitout)
      endif

! Writing the .geo file
!----------------------
      nunit=99

      filename=' '
      filename=basefour(1:lbasefour)//'.igeo'
!cth      filename=basefour(1:lbasefour)//'.geonew'
      lname=longcar(filename)

      open(unit=nunit,file=filename,ERR=9997)

!cth 1ere ligne = commentaire
      cline=' '
      cline='# '//cversion//' # COMBUSTION CHAMBER GEOMETRY INPUT FILE'
      lline=longcar(cline)
      write(nunit,100,ERR=9998) cline(1:lline)
100   format(a)

!cth Longueur max du four
      code=' '
      code='LXMAX'
      lcode=longcar(code)
      write(nunit,100,ERR=9998) code(1:lcode)
      write(nunit,200,ERR=9998) lxmax
200   format(1x,2(1x,f10.5))

!cth Hauteur max du four
      code=' '
      code='LYMAX'
      lcode=longcar(code)
      write(nunit,100,ERR=9998) code(1:lcode)
      write(nunit,200,ERR=9998) lymax

!cth largeur max du four
      code=' '
      code='LZMAX'
      lcode=longcar(code)
      write(nunit,100,ERR=9998) code(1:lcode)
      write(nunit,200,ERR=9998) lzmax

      if (qbrul.and.nposbrul.ne.0) then
!cth Position bruleurs pour definition zones
        code=' '
        code='XBRULEUR'
        lcode=longcar(code)
        write(nunit,300,ERR=9998) code(1:lcode),nposbrul
        do i=1,nposbrul
          write(nunit,400,ERR=9998) i,posbrul(i)
        enddo
      endif

      if (qnxbrul) then
!cth Remaillage nx par bruleur
        code=' '
        code='NXBRUL'
        lcode=longcar(code)
        write(nunit,300,ERR=9998) code(1:lcode),nposbrul
        do i=1,nposbrul
          write(nunit,500,ERR=9998) i,nxbrul(i)
        enddo
      endif

      if (qdxbrul) then
!cth Remaillage dx par bruleur
        code=' '
        code='DXBRUL'
        lcode=longcar(code)
        write(nunit,300,ERR=9998) code(1:lcode),nposbrul
        do i=1,nposbrul
          write(nunit,400,ERR=9998) i,dxbrul(i)
        enddo
      endif

      if (qzone.and.nzone.ne.0) then
!cth Definition directe des zones par leur longueur
        code=' '
        code='LZONE'
        lcode=longcar(code)
        write(nunit,300,ERR=9998) code(1:lcode),nzone
        do i=1,nzone
          write(nunit,400,ERR=9998) i,longzone(i)
        enddo
      endif

      if (qnxzone) then
!cth Remaillage nx par zone
        code=' '
        code='NXZONE'
        lcode=longcar(code)
        write(nunit,300,ERR=9998) code(1:lcode),nzone
        do i=1,nzone
          write(nunit,500,ERR=9998) i,nxzone(i)
        enddo
      endif
500   format(1x,i4,1x,2(1x,i4))

      if (qdxzone) then
!cth Remaillage dx par zone
        code=' '
        code='DXZONE'
        lcode=longcar(code)
        write(nunit,300,ERR=9998) code(1:lcode),nzone
        do i=1,nzone
          write(nunit,400,ERR=9998) i,dxzone(i)
        enddo
      endif

      if (nlargfour.ne.0) then
!cth Largeur four en fonction de la position
        code=' '
        code='XLARG'
        lcode=longcar(code)
        write(nunit,300,ERR=9998) code(1:lcode),nlargfour
        do i=1,nlargfour
          write(nunit,600,ERR=9998) xlargfour(i),vlargfour(i)
        enddo
      endif
600   format(1x,2(1x,f10.5))

      if (nhautfour.ne.0) then
!cth Hauteur four en fonction de la position
        code=' '
        code='XHAUT'
        lcode=longcar(code)
        write(nunit,300,ERR=9998) code(1:lcode),nhautfour
        do i=1,nhautfour
          write(nunit,600,ERR=9998) xhautfour(i),vhautfour(i)
        enddo
      endif

!cth Maillage en X
      code=' '
      code='XMAIL'
      lcode=longcar(code)
      write(nunit,300,ERR=9998) code(1:lcode),nxnoeud
300   format(a,1x,2(1x,i4))
      do i=1,nxnoeud
        write(nunit,400,ERR=9998) i,gridx(i)
400   format(1x,i4,1x,2(1x,f10.5))
      enddo

!cth Maillage en Y
      code=' '
      code='YMAIL'
      lcode=longcar(code)
      write(nunit,300,ERR=9998) code(1:lcode),nynoeud
      do i=1,nynoeud
        write(nunit,400,ERR=9998) i,gridy(i)
      enddo

      close(unit=nunit)

      return

9997  CONTINUE
!cth Erreur ouverture fichier
      print *,' WRITE_GEO_COMB : '
      PRINT *,' Erreur ouverture fichier : ',filename(1:lname)
      STOP ' MIEUX VAUT S ARRETER '

9998  CONTINUE
!cth Erreur ecriture
      print *,' WRITE_GEO_COMB : '
      PRINT *,' Erreur ecriture sur : ',filename(1:lname)
      STOP ' MIEUX VAUT S ARRETER '

      END
