# USBKEY CMakeLists.txt
# USB key module library

# Find all source files
file(GLOB USBKEY_SOURCES "*.f90")
file(GLOB USBKEY_F77_SOURCES "*.f")

# Create static library
add_library(usbkey STATIC ${USBKEY_SOURCES} ${USBKEY_F77_SOURCES})

# Set module directory
set_target_properties(usbkey PROPERTIES
    Fortran_MODULE_DIRECTORY ${CMAKE_Fortran_MODULE_DIRECTORY}
)

# Include directories
target_include_directories(usbkey PUBLIC
    ${CMAKE_Fortran_MODULE_DIRECTORY}
)

# Compiler definitions
target_compile_definitions(usbkey PRIVATE
    $<$<BOOL:${WIN32}>:WIN32>
    $<$<BOOL:${WIN32}>:_WINDOWS>
)

# Link dependencies
target_link_libraries(usbkey modules)

# Fortran compiler flags for fixed format files
if(WIN32)
    set_source_files_properties(${USBKEY_F77_SOURCES} PROPERTIES
        Fortran_FORMAT FIXED
        COMPILE_FLAGS "/extend_source:132"
    )
else()
    set_source_files_properties(${USBKEY_F77_SOURCES} PROPERTIES
        Fortran_FORMAT FIXED
        COMPILE_FLAGS "-FI -extend-source"
    )
endif()

message(STATUS "USBKEY: Sources: ${USBKEY_SOURCES}")
message(STATUS "USBKEY: F77 sources: ${USBKEY_F77_SOURCES}") 