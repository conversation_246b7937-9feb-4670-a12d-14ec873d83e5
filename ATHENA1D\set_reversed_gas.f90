	SUBROUTINE set_reversed_gas(basename,lbasename,nx,x,dr,crecirc,valrecirc)
!
!---------------------------------------------------------------------
! Affectation recirculation a partir fichier .par ou .rec
!---------------------------------------------------------------------
!
	IMPLICIT NONE


        CHARACTER*(*) basename
        INTEGER, INTENT (IN) :: lbasename
	INTEGER, INTENT(IN) :: NX
	REAL, INTENT(IN), DIMENSION(NX) :: x
	REAL, INTENT(OUT), DIMENSION(0:NX+1) :: DR
        character*1 crecirc
        real :: valrecirc

        CHARACTER*130 filename
        INTEGER :: lname
	INTEGER :: I

        INTEGER :: longcar

        DR = 0.0

        if (crecirc.eq.'N') then
          valrecirc=0.0
        endif
        if (crecirc.ne.'F') then
	  DO I=1,NX
	    DR(I)=valrecirc
	  ENDDO
	  DR(1)=0.0
        else

          filename=' '
          filename=basename(1:lbasename)//'.irec'
          lname=longcar(filename)
          call read_rec_file(filename,nx,x,dr(1))
        endif

        return

!
!cth Erreur ouverture
9997    continue
        print *,' Erreur ouverture fichier recirc-gaz.don '
        stop ' MIEUX VAUT S ARRETER '

	END
