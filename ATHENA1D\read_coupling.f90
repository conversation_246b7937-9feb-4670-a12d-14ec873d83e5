	SUBROUTINE read_coupling

!--------------------------------------------
! Lecture des entres du fichier couplage.don
!--------------------------------------------
!

        USE modcouplage
        USE modshared

	IMPLICIT NONE

        INTEGER lcaseline
        CHARACTER*80 caseline
        INTEGER, PARAMETER :: CODEEXPERT=1234567890
        INTEGER :: expertcode
        INTEGER lname
        CHARACTER*80 filename
        CHARACTER*130 cline
        INTEGER :: lline
        INTEGER :: numline
        INTEGER :: nreserve
        INTEGER :: longcar
        INTEGER :: ilin
        INTEGER :: nunit
        INTEGER :: lmodu

!
! Debut du programme
!-------------------

        numline=0

        nunit=36

        OPEN(UNIT=nunit,FILE='casename',STATUS='OLD',ERR=9994)
        READ(nunit,100,ERR=9995,END=9996) caseline
        CLOSE(unit=nunit)

        lcaseline=longcar(caseline)
        CALL CLEANLINE(caseline,casename,lcasename)
        if (lcasename.le.0) then
          print *,' LEC_CASENAME : CASENAME NOT DEFINED ?? '
          print *,' line : ',caseline(1:lcaseline)
          print *,' file : ',casename(1:lmaxcase)
          STOP ' CORRECT THIS, AND TRY AGAIN '
        endif
        if (lcasename.gt.lmaxcase) then
          print *,' LEC_CASENAME : CASENAME TOO LONG '
          print *,' line : ',caseline(1:lcaseline)
          print *,' file : ',casename(1:lmaxcase)
          print *,' longueur  : ',lcasename
          STOP ' CORRECT THIS, AND TRY AGAIN '
        endif

        nunit=37

        caseline=' '
        filename=casename(1:lcasename)//'_coupling.ipar'
        lname=longcar(filename)

        OPEN(UNIT=nunit,FILE=filename,STATUS='OLD',ERR=9997)

!cth 1ere ligne commentaire
        numline=numline+1
!cth        READ(nunit,170,ERR=9998,END=9999) cversion
        READ(nunit,170,ERR=9998,END=9999) cline
170     FORMAT(2x,a8)
100     FORMAT(a)
        lline=longcar(cline)
        lline=max(lline,8)
        cversion=cline(1:8)

        numline=numline+1
        READ(nunit,100,ERR=9990,END=9999) cline
        numline=numline+1
        READ(nunit,100,ERR=9990,END=9999) cline

!gm CALCULATION OPTIONS
        numline=numline+1
	READ(nunit,*,ERR=9998,END=9999) restart
        numline=numline+1
	READ(nunit,*,ERR=9998,END=9999) typecharge

        nreserve=2
        do ilin=1,nreserve
          numline=numline+1
	  READ(nunit,100,ERR=9998,END=9999) cline
        enddo

        numline=numline+1
        READ(nunit,100,ERR=9990,END=9999) cline
        numline=numline+1
        READ(nunit,100,ERR=9990,END=9999) cline

!gm MESH PARAMETERS
        numline=numline+1
	READ(nunit,*,ERR=9998,END=9999) mconfcoup

        nreserve=2
        do ilin=1,nreserve
          numline=numline+1
	  READ(nunit,100,ERR=9998,END=9999) cline
        enddo

        numline=numline+1
        READ(nunit,100,ERR=9990,END=9999) cline
        numline=numline+1
        READ(nunit,100,ERR=9990,END=9999) cline

!gm PHYSICAL MODELS AND NUMERICAL SOLUTION
        numline=numline+1
	READ(nunit,*,ERR=9998,END=9999) relaxcdon
        numline=numline+1
	READ(nunit,*,ERR=9998,END=9999) critercoup
        numline=numline+1
	READ(nunit,*,ERR=9998,END=9999) fctflux

        nreserve=1
        do ilin=1,nreserve
          numline=numline+1
	  READ(nunit,100,ERR=9998,END=9999) cline
        enddo

!cth Decodage de typecharge
        IF (TYPECHARGE.LT.0) THEN                    ! load alone
          CODE_CHARGE=ABS(TYPECHARGE)
          CODE_CALCUL=TYPECHARGE
        ELSE IF (TYPECHARGE.EQ.CODE_FOUR_ONLY) THEN  ! combustion chamber alone
!cth          CODE_CHARGE=CODE_GLASS
          CODE_CHARGE=CODE_SOFT
          CODE_CALCUL=CODE_FOUR_ONLY
        ELSE						                 ! coupling
          CODE_CHARGE=TYPECHARGE
          CODE_CALCUL=CODE_COUPLAGE
        ENDIF

!cth Valeur par defaut modifiable par CODE EXPERT
!cth CHANGEMENT DE VALEUR PAR DEFAUT POUR SMR
!cth POUR LE MOMENT ON DISSOCIE SUR LA CHARGE
!cth DANS L AVENIR TROUVER DES VALEURS COMMUNES GARANTIES

        itmin=1
        itmax=1000000
        tracecouplage=1
        nbfour=1
        IF (CODE_CHARGE.EQ.CODE_SMR) THEN
          itmax=50
          nitertrelax=15
          relaxctmin=0.3
          relaxctdon=0.8
          niterfrelax=15
          relaxcfmin=0.7
          relaxcfdon=0.9
          relaxctemp=relaxctdon
          relaxcflux=relaxcfdon
        ELSE
          relaxctmin=relaxctdon
          relaxcfmin=relaxcfdon
          relaxctdon=relaxcdon
          relaxcfdon=relaxcdon
          relaxctemp=relaxctdon
          relaxcflux=relaxcfdon
        ENDIF

!cth Ligne blanche de separation
        numline=numline+1
        READ(nunit,100,ERR=1000,END=1000) cline

!cth Options CODE EXPERT
        numline=numline+1
	READ(nunit,*,ERR=9998,END=1000) expertcode

        IF (expertcode.eq.CODEEXPERT) then
          numline=numline+1
	  READ(nunit,*,ERR=9998,END=1000) itmin,itmax
          numline=numline+1
	  READ(nunit,*,ERR=9998,END=1000) nitertrelax,relaxctmin,relaxctdon
          numline=numline+1
	  READ(nunit,*,ERR=9998,END=1000) niterfrelax,relaxcfmin,relaxcfdon
          numline=numline+1
	  READ(nunit,*,ERR=9998,END=1000) tracecouplage
          numline=numline+1
	  READ(nunit,*,ERR=9998,END=1000) nbfour
        ENDIF

!cth Pas code expert
1000    CONTINUE

        CLOSE(UNIT=nunit) 

        nbiterelax=nitertrelax

!cth Verif du nombre de module athena1d
        IF (TYPECHARGE.EQ.NO_CODE) THEN
          PRINT *,' READ_COUPLING : UNKNOWN LOAD TYPE ?? '
          STOP ' CORRECT THIS, AND TRY AGAIN '
        ENDIF
        IF (TYPECHARGE.LT.0) nbfour=0
        IF (TYPECHARGE.EQ.CODE_FOUR_ONLY) nbfour=1
        IF (TYPECHARGE.EQ.CODE_REHEAT) THEN
          IF (NBFOUR.LT.1.AND.NBFOUR.GT.MAXFOUR) THEN
            PRINT *,' READ_COUPLING : NBFOUR ? = ',NBFOUR
            PRINT *,' REHEAT CASE : FURNACE NUMBER MUST BE DEFINED '
            STOP ' CORRECT THIS, AND TRY AGAIN '
          ENDIF
        ELSE
          nbfour=1
        ENDIF

!cth Base de nom des fichiers
        lmodu=longcar(moduname(code_charge))
        nameload=casename(1:lcasename)//'_'//moduname(code_charge)(1:lmodu)

        lmodu=longcar(moduname(code_four_only))
        if (nbfour.eq.1) then
          namefour(1)=casename(1:lcasename)//'_'//moduname(code_four_only)(1:lmodu)
        else
          namefour(1)=casename(1:lcasename)//'_'//moduname(code_four_only)(1:lmodu)//'_1'
          namefour(2)=casename(1:lcasename)//'_'//moduname(code_four_only)(1:lmodu)//'_2'
        endif

        lnameload=longcar(nameload)
        lnamefour(1)=longcar(namefour(1))
        lnamefour(2)=longcar(namefour(2))

        print *,' ---------------------------------------------------------'
	print *,' '
!cth	print *,'              ',progname(typecharge),' SIMULATION '
	print *,'              ',progname(code_charge),' SIMULATION '
	print *,' '
	print *,'                    ',cversout,'                          '
	print *,' '
        print *,' ---------------------------------------------------------'
        IF (TYPECHARGE.EQ.CODE_FOUR_ONLY)  THEN
          print *,' FURNACE ONLY : NO LOAD NAME '
        ELSE
          print *,' LOAD NAME : ',nameload(1:lnameload)
        ENDIF
        IF (nbfour.eq.1) then
          print *,' COMBUSTION CHAMBER NAME : ',namefour(1)(1:lnamefour(1))
        ELSE
          print *,' COMBUSTION CHAMBER NAME 1 : ',namefour(1)(1:lnamefour(1))
          print *,' COMBUSTION CHAMBER NAME 2 : ',namefour(2)(1:lnamefour(2))
        ENDIF
	print *,' ---------------------------------------------------------'
	print *,' '
	print *,' '

        IF (expertcode.eq.CODEEXPERT.AND.tracecouplage.GE.1) then
	  print *,' '
	  print *,' ---------------------------- '
          print *,' COUPLING : USING EXPERT MODE '
	  print *,' ---------------------------- '
	  print *,' '
        ENDIF

        RETURN


9994    CONTINUE
!gm  Erreur ouverture fichier
        PRINT *,' '
        PRINT *,' Software version : ',cversout
        PRINT *,' OPEN ERROR : casename'
        PRINT *,' '
        STOP ' CORRECT THIS, AND TRY AGAIN '

9995    CONTINUE
!gm  Erreur lecture
        PRINT *,' '
        PRINT *,' Software version : ',cversout
        PRINT *,' READ ERROR : casename'
        PRINT *,' '
        STOP ' CORRECT THIS, AND TRY AGAIN '

9996    CONTINUE
!cth Fin de fichier
        PRINT *,' '
        PRINT *,' Software version : ',cversout
        PRINT *,' END-OF-FILE : casename'
        PRINT *,' '
        STOP ' CORRECT THIS, AND TRY AGAIN '

9997    CONTINUE
!cth Erreur ouverture fichier
        PRINT *,' '
        PRINT *,' Software version : ',cversout
        PRINT *,' OPEN ERROR : ',filename(1:lname)
        PRINT *,' '
        STOP ' CORRECT THIS, AND TRY AGAIN '

9998    CONTINUE
!cth Erreur lecture
        PRINT *,' '
        PRINT *,' Software version : ',cversout
        PRINT *,' READ ERROR : ',filename(1:lname)
        PRINT *,' LINE NUMBER : ',numline
        PRINT *,' '
        STOP ' CORRECT THIS, AND TRY AGAIN '

9999    CONTINUE
!cth Fin de fichier
        PRINT *,' '
        PRINT *,' Software version : ',cversout
        PRINT *,' END-OF-FILE : ',filename(1:lname)
        PRINT *,' LINE NUMBER : ',numline
        PRINT *,' '
        STOP ' CORRECT THIS, AND TRY AGAIN '

9990    CONTINUE
!cth Erreur lecture commentaire et ligne de separation
        PRINT *,' '
        PRINT *,' Software version : ',cversout
        PRINT *,' COMMENT READ ERROR : ',filename(1:lname)
        PRINT *,' LINE NUMBER : ',numline
        PRINT *,' '
        STOP ' CORRECT THIS, AND TRY AGAIN '

	END
