	SUBROUTINE solve_comb (	itercoup,  errtsom,  errtmax,    indtmax, &
	&			tfourmax,  tsortie,  qconvfour,  qarret   )
!****m* athena1d.library/solve_comb
!NAME
!	solve_comb.f90 - main programm resolve combustion chamber
!***
!	AUTHOR		:	
!
!	FUNCTION	:	Thermal transfer modelling in combustion chamber
!
!	INPUTS		:	itercoup	iteration courant for the coupling
!
!	OUTPUTS		:	errtsom 
!				errtmax 
!				indtmax 
!				tfourmax 
!				tsortie
!				qconvfour 
!				qarret
!
!	NOTES		:	The load can represented by different boundary conditions
!				It exists one pseudo 2D model and 1D model
!				The program uses a WSGG method (somme ponderee de gaz gris)
!				and a angular discretization for radiation transfers
!
!**********************************************************************

	USE modTwoDRad
	USE modBilan
	USE modfacteur
	USE modcodefour
	USE modcouplage
	USE modshared

	IMPLICIT NONE

!****	INCLUDE
	INCLUDE 'prophy.cmn'

!****	COMMON
	INTEGER :: ires(3)
	COMMON/impr/ires

	REAL :: PCI,WM,R_O2_FUEL,R_CO2_FUEL
	REAL :: R_H2O_FUEL,R_N2_FUEL,INVWM,R_H2O_CO2,R_CO2_O2,R_N2_O2
	COMMON/combustible/PCI,WM,R_O2_FUEL,R_CO2_FUEL,&
	&			R_H2O_FUEL,R_N2_FUEL,INVWM,R_H2O_CO2,R_CO2_O2,R_N2_O2

!****	PARAMETER
	REAL, PARAMETER :: SMALL=1.0e-6
	REAL, PARAMETER :: pg=101325 !ccri pg=1.e5 !cth pression pour fct_density

!****	EXTERNAL FUNCTION
	REAL :: hmel, chamel, fct_density, fct_viscosity ! fonctions externes

!****	ARGUMENTS VARIABLES
	INTEGER :: itercoup, indtmax
	REAL :: errtsom,errtmax,tsortie,tfourmax
	LOGICAL :: qconvfour,qarret

!****	LOCAL VARIABLES
	INTEGER :: longcar,i,j,l,m,itergaz,jitermax, kcoef, itermax, indglobinit, ierrmax

	CHARACTER*130 filename
	LOGICAL :: QERR

	REAL :: s, pi, reacnul, err,errrel,varpol,respol
	REAL :: puissbrultot,puissparoi2
	REAL :: puissbilan,puissbilan2,puissbilan3,puissbilan4
	REAL :: fluxradv,fluxradpn,fluxccv,fluxccpn
	REAL :: fluxradpe,fluxccpe,fluxradpw,fluxccpw
	REAL :: nm3h, errorconv,erriter1
	REAL :: thlargefact
	REAL :: puissbilan5,puissparoir,puissparoit
	REAL :: puisstrraj

	REAL, DIMENSION(nx) :: sdifplus,sdifmoins
        REAL :: hmelange

!
! Debut du programme
!-------------------

	indglobinit=-1
	qerr=.false.
	TestTwoDRad=.false.
	AskForStop=.false.
	qconvfour=TestTwoDRad
	qarret=AskForStop
	pi=4.*atan(1.)
	reacnul=0.
	err=100.
	errrel=1.

	iterlocal=iterlocal+1

	IF (redem.eq.0) THEN
	  IF (code_charge.eq.CODE_SMR) THEN
	    redem=1
	  ELSE IF ((indglob.eq.1)) THEN
	    redem=0
	  ELSE
	    redem=1
	  ENDIF
	ENDIF

!cth Reception eventuelle des champs de la charge et sous relaxation
	IF (code_calcul.ne.CODE_FOUR_ONLY) THEN
	  IF(code_charge.ne.CODE_GTM) CALL get_interf_field(MODU_CHARGE,mycode_module,CHAMP_TEMP,tps1,nx)
	  IF (indglobt.eq.1) THEN
	    tps1anc=tps1
	  ENDIF
	  tps1=tps1anc+relaxctemp*(tps1-tps1anc)
	ENDIF

!cth          print *,' SOLVE_COMB : ',iterlocal,indglobt,redem
!cth          print *,' ==> RELAXCTEMP : ',relaxctemp
!cth          print *,' ==> TPS : '
!cth          DO i=1,NX
!cth            print *,i,tps1(i),tps1anc(i)
!cth          enddo

!
!cth Sous relaxation evolutive
	CALL set_relax_comb(itercoup)
!
	itergaz=1
!
! ----------------------------------------------------------------------
!
!    DEBUT DE LA BOUCLE ITERATIVE INTERNE A SOLVE_COMB(ATMO)
!
! ----------------------------------------------------------------------

	PRINT *,' '
	PRINT 1015

1001	CONTINUE
	Zonegaz=0
	ierrmax=0
!
! Calcul radiatif.
! -----------------------------------------------------------------------------
! Quelques remarques sur le calcul radiatif: on a introduit deux manieres
! de ccalculer les flux radiatifs. L une s apparente a la methode de la sphere
! d Hottel (4), l autre est une methode de transferts discrets (3), plus precise
! On essaye de determiner une solution approximative a l aide de 4, puis
! de converger vers la bonne soution avec 3
!------------------------------------------------------------------------------
! Programme rayonWSGG, sorties:	fradps : phiR paroi sud
!				fradpn : phiR paroi nord
!				fradpe : paroi est (scalaire)
!				fradpw : paroi ouest (scalaire)
!------------------------------------------------------------------------------
!
	fradpsanc=fradps
	fradpnanc=fradpn
	fradpeanc=fradpe
	fradpwanc=fradpw
	puissradanc=puissrad
	fradps=0.
	fradpn=0.
	fradpe=0.
	fradpw=0.
	puisstr=0.
	puissrad=0.

!cth TEST POUR STABILISER TEMPERATURES PAROIS OUEST, EST ET NORD
!
	IF (code_calcul.ne.CODE_FOUR_ONLY.and.indglobt.eq.1) THEN
	  puisstr=1.0e-15
	  puissrad=1.0e-15
	  fradps=1.0e-15
	  CALL solve_init_temp(itercoup)
	  indglobinit=indglob
        ELSE  IF (indglobt.eq.1) THEN
	  puisstr=1.0e-15
	  puissrad=1.0e-15
	  fradps=1.0e-15
	  CALL solve_init_temp(itercoup)
	  indglobinit=indglob
	ENDIF
!
	IF (modrad.eq.2) THEN
	  CALL solve_radiation(NX      ,ny      ,nang    ,nbrayon      ,nbtsi,nxp1,nxp2 ,&
	&		       nyp1    ,nyp2    ,np      ,tps1         ,Tpn1  ,Tpe1,Tpw1,&
	&		       Tgi1    ,yCO2    ,yH2O    ,yAr          ,yN    ,yO  ,yF  ,&
	&		       hautfour,largfour                                        ,&
	&		       theta   ,dtheta  ,alpha   ,dalpha       ,tsi   ,dtsi     ,&
	&		       interdit,interior                                        ,&
	&		       x       ,y       ,dx      ,dy           ,gridx ,gridy    ,&
	&		       xyp     ,xycoin  ,emis    ,gamma        ,coin            ,&
	&		       direc   ,surf    ,fradps  ,fradpn       ,fradpe,fradpw   ,&
	&		       puissrad,dprsdt  ,icor_ray,RECTANGULAIRE                 ,&
	&		       errorrad,nggcod                                          )
	ELSE
		PRINT*,'*********************************************************'
		PRINT*,'                  ARRET dans SOLVE_COMB'
		PRINT*,' La variable modrad different de 0 ou 2 dans cn_f.par '
		PRINT*,'*********************************************************'
		STOP
	ENDIF
!
!cth Init pour sous relaxation
	IF (indglobt.eq.1) THEN
	  DO i=1,NX
	    fradpsanc(i)=0.5*fradps(i)
	    fradpnanc(i)=0.5*fradpn(i)
	    puissradanc(i)=0.5*puissrad(i)
	    dprsdtanc(i)=0.5*dprsdt(i)
	    fradpeanc=0.5*fradpe
	    fradpwanc=0.5*fradpw
!cth	    fradpsanc(i)=0.9*fradps(i)
!cth	    fradpnanc(i)=0.9*fradpn(i)
!cth	    puissradanc(i)=0.9*puissrad(i)
!cth	    dprsdtanc(i)=0.9*dprsdt(i)
!cth	    fradpeanc=0.9*fradpe
!cth	    fradpwanc=0.9*fradpw
	  ENDDO
	ENDIF

!cth
	IF (indglobt.eq.indglobinit) THEN
           print *,' SOLVE_COMB : INDGLOBT ',indglobt,indglob,indglobinit
	  DO i=1,NX
	    puissradanc(i)=0.9*puissrad(i)
	  ENDDO
        ENDIF
!cth

!cth Sous relaxation flux et puissance radiative
	DO i=1,NX
	  fradps(i)=fradpsanc(i)+relaxrflux*(fradps(i)-fradpsanc(i))
	  fradpn(i)=fradpnanc(i)+relaxrflux*(fradpn(i)-fradpnanc(i))
	  puissrad(i)=puissradanc(i)+relaxrpuis*(puissrad(i)-puissradanc(i))
	ENDDO

	fradpe=fradpeanc+relaxrflux*(fradpe-fradpeanc)
	fradpw=fradpwanc+relaxrflux*(fradpw-fradpwanc)

	DO i=1,nx
	  puisstr(i)=puissrad(i)*largfour(i)  ! largsole a ete remplace par largfour (cyou)
	  dprsdt(i)=dprsdt(i)*largfour(i)  ! largsole a ete remplace par largfour (cyou)
	ENDDO

        PUISSTRRAJ=0.0
	DO I=1,NX
!cth	  puisstr(I)=puisstr(I)+fradpn(I)*DX(I)*2*hautfour(I)
          PUISSTRRAJ=PUISSTRRAJ+fradpn(I)*DX(I)*2*hautfour(I)
	ENDDO

!cth calcul proprietes du gaz
	DO i=1, NX
	  rhogaz(i)=fct_density(YN(i),YO(i),YCO2(i),YH2O(i),YF(i),Tgi1(i),Pg)
	  visgaz(i)=fct_viscosity(reacfour,YN(i),YO(i),YCO2(i),YH2O(i),YAr(i),YF(i),Tgi1(i))
	  cpgaz(i)=chamel(reacfour,YN(i),YO(i),YCO2(i),YH2O(i),YAr(i),YF(i),Tgi1(i))
	ENDDO

!cth calcul vitesse pour convection
	DO i=1, NX
          vitconv(i)=debitconv(i)/(rhogaz(i)*largfour(i)*hautfour(i))
	ENDDO

!cth Init du tableau enthalpie gaz
	DO i=1, NX
		hgaz(i)=hmel(reacfour,yN(i),yO(i),yCO2(i),yH2O(i),&
	&		yAr(i),yF(i),Tgi1(i))
	ENDDO

!cth Init du tableau enthalpie degazage
	DO i=1, NX
		hdeg(i)=hmel(reacnul,yNdeg(i),yOdeg(i),yCO2deg(i),yH2Odeg(i),&
	&	yArdeg(i),yFdeg(i),tps1(i))
	ENDDO

	Tgi2=Tgi1
	TpN2=TpN1
	TpW2=TpW1
	TpE2=Tpe1
	tps2=tps1
	hgaz2=hgaz

!cth Calcul en sud hinfini ou flux impose si polynome
	DO i=1, NX
	  IF (typecondsud(i).eq.1.or.typecondsud(i).eq.3) THEN
	    respol=0.
	    IF (IVARsud(i).eq.0) varpol=tps1(I)
	    IF (IVARsud(i).eq.1) varpol=X(I)
	    DO kcoef=ncoefsud(i),2,-1
	      respol=(respol+COEFSUD(kcoef,I))*varpol
	    ENDDO
	    respol=respol+COEFSUD(1,i)
	    IF (typecondsud(i).eq.1) hinfsud(i)=respol
	    IF (typecondsud(i).eq.3) fimpsud(i)=respol
	  ENDIF
	ENDDO

!cth Calcul en nord hinfini ou flux impose si polynome
	DO i=1, NX
	  IF (typecondnord(i).eq.1.or.typecondnord(i).eq.3) THEN
	    respol=0.
	    IF (IVARnord(i).eq.0) varpol=TpN1(I)
	    IF (IVARnord(i).eq.1) varpol=X(I)
	    DO kcoef=ncoefnord(i),2,-1
	      respol=(respol+COEFNORD(kcoef,I))*varpol
	    ENDDO
	    respol=respol+COEFNORD(1,i)
	    IF (typecondnord(i).eq.1) hinfnord(i)=respol
	    IF (typecondnord(i).eq.3) fimpnord(i)=respol
	  ENDIF
	ENDDO
	IF (typecondouest.eq.3) fimpouest=fimpnord(1)
	IF (typecondest.eq.3) fimpest=fimpnord(nx)

!ccri calcul vitesse flame pour convection dans la correlation Callidus
        DO i=1,NX !ccri-compoute vitflame
          vitflame(i)=fracomb_grid(i)*(sum(mb))/9./(fct_density(YN(i),YO(i),YCO2(i),YH2O(i),&
     &      YF(i),Tbr(i),Pg)*2*3.1415*0.07*0.07)
        ENDDO

!cth Bilan et temperatures sur parois

	CALL solve_bc_heat_balance (nx, vitconv,       Tgi2,      yO,  &      !paroi nord 
				& yN,         yCo2,          yh2o,      yAr,       &
				& yF,         typecondnord,  Tpn2,      fradpn,    & 
				& epsp,       hinfnord,      tinfnord,  fimpnord,  &
				& modtp,      fccpn,         hccpn,     lxmax,     &
				& tpadmimin,  tpadmimax,     errorent,  relaxent,  &
				& corcvbrul,  modcvbrul,     Tbr,   vitflame, longzone_grid,hcvbrul)

	CALL solve_bc_heat_balance (nx, vitconv,      Tgi2,      yO,  &       !paroi sud
				& yN,         yCo2,         yh2o,      yAr,       &
				& yF,         typecondsud,  tps2,      fradps,    &
				& epsv,       hinfsud,      tinfsud,   fimpsud,   &
				& modtp,      fccps,        hccps,     lxmax,     &
				& tpadmimin,  tpadmimax,    errorent,  relaxent,  &
				& corcvload,  modcvload,    Tbr,    vitflame, longzone_grid,hcvload)

	CALL solve_bc_heat_balance (1, vitconv(1),     Tgi2(1),    yO(1), &  !paroi ouest
				& yN(1),      yCo2(1),        yh2o(1),    yAr(1),      &
				& yF(1),      typecondouest,  Tpw2,       fradpw,      &
				& epsp(1),    hinfouest,      tinfouest,  fimpouest,   &
				& modtp,      fccpw,          hccpw,      hautfour(1), &
				& tpadmimin,  tpadmimax,      errorent,   relaxent,  &
				& corcvref,  modcvref,       Tbr(1),    vitflame(1),longzone_grid(1),hcvref)

	CALL solve_bc_heat_balance (1, vitconv(nx),  Tgi2(nx),  yO(nx), &    !paroi est
				& yN(nx),     yCo2(nx),     yh2o(nx),  yAr(nx),      &
				& yF(nx),     typecondest,  Tpe2,      fradpe,       &
				& epsp(nx),   hinfest,      tinfest,   fimpest,      &
				& modtp,      fccpe,        hccpe,     hautfour(nx), &
				& tpadmimin,  tpadmimax,    errorent,  relaxent,  &
				& corcvref,  modcvref,     Tbr(nx),    vitflame(nx), longzone_grid(nx),hcvref)

!cth Flux convectif

	DO i=1, NX
	  fcctr(i)=fccpn(i)*surfconvpn(i)+fccps(i)*surfconvps(i)
	ENDDO
	fcctr(1)=fcctr(1)+fccpw*surfconvpw
	fcctr(nx)=fcctr(nx)+fccpe*surfconvpe

	CALL solve_heat_balance ( nx,       mb,       hbr,      deg,   &
				& hdeg,     dp,       dr,       debitglob, &
				& hgaz2,    Tgi2,     reacfour, yO,        &
				& yN,       yCo2,     yh2o,     yAr,       &
				& yF,       puisstr,  dprsdt,   fcctr,     &
				& modresol, nbresol,  linptr,   reshg,     &
				& resmaxhg)

	jitermax=30
	CALL compute_gas_temp ( nx,     tgi2,     hgaz2,     reacfour, &
				& yN,       yO,       yCO2,      yH2O,     &
				& yAr,      yF,       tgadmimin, tgadmimax,&
				& errorent, relaxent, jitermax)

	DO i=1,NX
	  IF (abs(Tgi2(i))>tgadmimax.or.Tgi2(i)<tgadmimin) THEN
	    qerr=.true.
	    PRINT *,'Error in ',i,Tgi2(i),tgi1(i),puisstr(i)
	    PRINT*,' '
	  ENDIF
	ENDDO

	ftotps=fradps+fccps
	ftotpN=fradpN+fccpN
	ftotpE=fradpE+fccpE
	ftotpW=fradpW+fccpW

	IF (qerr) THEN
	  CALL write_data_comb()
	  filename=casename(1:lcasename)//'_'//moduname(code_max)(1:longcar(moduname(code_max)))//'.err'
	  OPEN (unit=27,file=filename)
	  DO i=1,NX
	    WRITE (27,271) i,Tgi2(i),tgi1(i)&
	&	,TpN2(i),tpn1(i),tpw2,tpw1,tpe2,tpe1,tps1(i)
	    WRITE (27,271) i,puissrad(i)&
	&	,fradpN(i),fradps(i),fradpw,fradpe
	    WRITE (27,271) i,puissradanc(i)&
	&	,fradpNanc(i),fradpsanc(i),fradpwanc,fradpeanc
	  ENDDO
	  CLOSE (27)
	  PRINT*,' ***************'
	  STOP ' ERROR IN SOLVE_COMB'
	ENDIF
!
! Calcul de l ecart sur les temperatures paroi et gaz:
!-----------------------------------------------------

	CALL residu_comb(err,ierrmax)
	errrel=err/Tgi1(ierrmax)

	hgaz=hgaz2

!cth Sous relaxation temperatures
	Tgi1=Tgi1*(1.0-relaxgaz)+Tgi2*relaxgaz
	TpN1=TpN1*(1.0-relaxparoi)+TpN2*relaxparoi
	TpW1=TpW1*(1.0-relaxparoi)+TpW2*relaxparoi
	TpE1=TpE1*(1.0-relaxparoi)+TpE2*relaxparoi

	CALL write_data_comb()
!cth	CALL write_example_comb(nx,x,tgi1,tpn1,tps1,dr,iecrini,iecrprof)
	CALL write_example_comb()

	IF (itergaz.eq.1) THEN
	  erriter1=errrel
	  errorconv=erriter1*ratioconv
	ENDIF
!
	itermax = itergazmax
!
	IF (itergaz.ge.itermax) THEN
		IF (errrel<errorgaz) TestTwoDRad=.true.
		qconvfour=TestTwoDRad
!cth		WRITE(*,1017) itergaz,errrel,errorgaz
		GO TO 2000 
	ELSE IF (errrel<errorgaz.and.itergaz.ge.itergazmin) THEN
		TestTwoDRad=.true.
		qconvfour=TestTwoDRad
!cth		WRITE(*,1017) itergaz,errrel,errorgaz
		GO TO 2000
!cth Arreter iteration si l erreur a diminuee d un facteur ratioconv
!cth Lorsque le calcul est couple.
!cth Car inutile d essayer de converger tres finement
!cth puisque le couplage va de toute facon bouleverser
!cth les C.L.
!cth Si le couplage se stabilise alors de toute facon on doit converger
!cth en dessous de errorgaz
!cth	ELSE IF (code_calcul.ne.CODE_FOUR_ONLY.and.errrel < errorconv.and.itercoup.gt.5.and.itergaz.ge.itergazmin) THEN
!cth	ELSE IF (code_calcul.ne.CODE_FOUR_ONLY.and.errrel < errorconv.and.itercoup.gt.4.and.itergaz.ge.itergazmin) THEN
	ELSE IF (code_calcul.ne.CODE_FOUR_ONLY.and.errrel < errorconv.and.itercoup.ge.3.and.itergaz.ge.itergazmin) THEN
!cth		WRITE(*,1017) itergaz,errrel,errorgaz
		GO TO 2000 
	ENDIF
!
!cth	WRITE(*,1017) itergaz,errrel,errorgaz
!
! Test pour l'arret du calcul
!cth CHANGER NOM DU FICHIER
!cth FICHIER N A PAS BESOIN D EXISTER EN PERMANENCE
!cth ==> DESTRUCTION FICHIER APRES LECTURE EVENTUELLE
!cth EVENTUELLEMENT TRAITER D AUTRE INFO : CHANGEMENT DE PARAMETRE ???
!cth PLUTOT ROUTINE SEPAREE
!cth DE PLUS IL FAUDRAIT ECRIRE LE FICHIER RESTART EN CAS D ARRET
!cth ==> ARRET CONTROLER DANS MAIN ?
	lignemsg=' '
        filename=' '
	filename=namefour(myindfour)(1:lnamefour(myindfour))//'.ctrl'
	OPEN(63,file=filename,status='OLD',err=1000)
	READ(63,'(A)',ERR=1000,END=1000) lignemsg
	CLOSE(63)
1000	CONTINUE

	IF (lignemsg(1:4).eq.'stop'.or.lignemsg(1:4).eq.'STOP') THEN
		AskForStop=.true.       !gm
		qarret=AskForStop
                print *,' '
                print *,' ---------------------------- '
		print *,' STOP REQUESTED BY FILE .ctrl '
                print *,' ---------------------------- '
                print *,' '
		GO TO 2000
	ENDIF
!

	IF (errrel<errorgaz) THEN
	  WRITE(*,1018) itergaz,errrel,errorgaz
        ELSE
	  WRITE(*,1017) itergaz,errrel,errorgaz
	ENDIF

	itergaz=itergaz+1
	indglob = indglob + 1
	indglobt = indglob0 + indglob
!
	GOTO 1001
!---------------------------------------------------------------------------
!
!	FIN ITERATIONS SUR LE GAZ
!
!---------------------------------------------------------------------------

2000	CONTINUE
!

        IF (qconvfour) THEN
	  WRITE(*,1018) itergaz,errrel,errorgaz
        ELSE
	  WRITE(*,1017) itergaz,errrel,errorgaz
        ENDIF
	PRINT 1015
        PRINT *,' '

	itergaz=itergaz+1
	indglob = indglob + 1
	indglobt = indglob0 + indglob

	puisssortiefum=0.
	puissparoiz = 0.
	fluxradv=0.
	fluxradpn=0.
	fluxccv=0.
	fluxccpn=0.
	puissload=0.0
	puissloadcv=0.0
	puissloadrad=0.0
	puissgaz=0.
	DO I=1,NX
	  fluxradv=fluxradv+fradps(I)*DX(I)*largsole(i)*fconvect
	  fluxradpn=fluxradpn+fradpn(I)*DXEQUIV(I)*(largfour(i)+2*hautfour(I))
	  fluxradpn=fluxradpn+fradps(I)*DX(I)*(largfour(i) - largsole(i))
	  fluxccv=fluxccv+fccps(I)*DX(I)*largsole(i)*fconvect
	  fluxccpn=fluxccpn+fccpn(I)*DXEQUIV(I)*(largfour(i)+2*hautfour(I))
	  fluxccpn=fluxccpn+fccps(I)*DX(I)*(largfour(i) - largsole(i))
	  hmelange=hmel(reacbrul,yN(I),yO(I),yCO2(I),yH2O(I),yAr(I),yF(I),Tgi1(I))
	  puisssortiefum=puisssortiefum+Sor(I)*hmelange
	  puissgaz=puissgaz+puisstr(i)
	ENDDO

	fluxradpe = fradpe*surfest*largfour(nx)
	fluxccpe  = fccpe*surfest*largfour(nx)
	fluxradpw = fradpw*surfouest*largfour(1)
	fluxccpw  = fccpw*surfouest*largfour(1)

	puissbrultot = 0.
	DO j=1,nbzones
	  puissrech(j)=puissbruz(j)
	  puissbrultot = puissbrultot + puissbruz(j)+ PCI*mcomb(j)           
	ENDDO

	puissload =fluxradv+fluxccv
	puissloadcv = fluxccv
	puissloadrad = fluxradv
	puissparoiouest     = fluxradpw+fluxccpw
	puissparoiest       = fluxradpe+fluxccpe
	puissparoi=fluxradpn+fluxradpw+fluxradpe
	puissparoi=puissparoi+fluxccpn+fluxccpw+fluxccpe
	puissparoit=puissparoi+puissload
	puissparoir=fluxradpn+fluxradpw+fluxradpe+puissloadrad

	puissparoi2 = fluxccpn+fluxccv+fluxccpw+fluxccpe
	puissparoi2 = puissparoi2 + puissgaz

	puissbilan=puissbrultot-puissparoi-puisssortiefum-puissload
	puissbilan2=puissbrultot-puissparoi2-puisssortiefum
	puissbilan3=puissbrul-puissparoi-puisssortiefum-puissload
	puissbilan4=puissbrul-puissparoi2-puisssortiefum
	puissbilan5=puissbrul-puissparoit-puisssortiefum

	IF (ires(1).ge.1) THEN
		PRINT*,' BRULEUR = ',puissbrultot*1e-6,' (+degazage)'
		PRINT*,' PAROI   = ',puissparoi*1e-6
		PRINT*,' FUMEES  = ',puisssortiefum*1e-6
		PRINT*,' SOLE    = ',puissload*1e-6
		PRINT*,' PAROITOT= ',puissparoit*1e-6
		PRINT*,' PAROIRAD= ',puissparoir*1e-6
		PRINT*,' PUISSRAD= ',puissgaz*1e-6
		PRINT*,' PUISSRAJ= ',puisstrraj*1e-6
		PRINT*,' BILAN   = ',puissbilan/puissbrultot
		PRINT*,' PAROI2  = ',(puissparoi2)*1e-6
		PRINT*,' BILAN2  = ',puissbilan2/puissbrultot
		PRINT*,' BRULEUR = ',puissbrul*1e-6,' (+degazage)'
		PRINT*,' BILAN3  = ',puissbilan3/puissbrul
		PRINT*,' BILANPR = ',puissbilan4/puissbrul
		PRINT*,' BILANFR = ',puissbilan5/puissbrul
	ENDIF

	T_fum = Tgi1(Nx)
	nm3h  = R*273.15*3600./(WMfum(Nx)*101325)
	F_fum = Sor(Nx)*nm3h
	xCO2_fum = yCO2(Nx) * WMfum(Nx) / WMCO2
	xH2O_fum = yH2O(Nx) * WMfum(Nx) / WMH2O
	xO2_fum  = yO(Nx)  * WMfum(Nx) / WMO2

	CALL write_bal_comb()

	DO j=1,nbzones
	  l=indmaille(j)
	  m=indmaille(j)+nbtranchzone(j)+1
	  DO i=l+1,m-1		
	    puissparoiz(j)= puissparoiz(j)+ftotpn(I)*(largfour(i)+2*hautfour(I))*DXEQUIV(I) &
	&                 + ftotps(I)*DX(I)*(largfour(i) - largsole(i))
	  ENDDO
	ENDDO

	puissparoiz(nbzones)= puissparoiz(nbzones)+puissparoiouest
	puissparoiz(1)      = puissparoiz(1)+puissparoiest

	puissloadz = 0.
	DO j=1,nbzones
	  puissPCI(j) = PCI*mcomb(j)
	  l=indmaille(j)
	  m=indmaille(j)+nbtranchzone(j)+1
	  DO i=l+1,m-1
	    puissloadz(j)= puissloadz(j)+DX(i)*ftotps(i)*largsole(i)*fconvect
	  ENDDO
	ENDDO 

	puissparoiperdu=0.
	DO j=1,nbzones !Calcul de la puissance de prechauffage par zone+pertes
	  puissparoiperdu=puissparoiperdu + puissparoiz(j)
	ENDDO
!
!Ecriture optionnelle
!------------------------------------------
	IF(ires(1).eq.1) THEN
	  filename=casename(1:lcasename)//'_'//moduname(code_max)(1:longcar(moduname(code_max)))//'.flu_ld'
	  OPEN(unit=11,file=filename) ! Puissance transmise a la sole par zone 
	  puissbilan = 0.0d0
	  DO i=1,NX   
	    puissbilan = puissbilan + ftotps(i)*largsole(i)*DX(i) !cyou
	    WRITE(11,222) i,ftotps(i),dx(i),puissbilan !cyou
	  ENDDO    
	  PRINT*,' TOTAL HEAT FLUXES = ',puissbilan   
	  CLOSE(11)
	ENDIF

!
!calcul et verification des surfaces relles
!------------------------------------------
	IF(ires(2).eq.1) THEN
		sdifplus=0.
		sdifmoins=0.  
		IF (largfour(1).gt.largfour(2)) sdifplus(1) = sdifplus(1) + hautfour(1)*(largfour(1)-largfour(2))
		IF (largfour(nx).gt.largfour(nx-1)) sdifmoins(nx)=sdifmoins(nx)+ hautfour(nx)*(largfour(nx)-largfour(nx-1))
		IF (hautfour(1).gt.hautfour(2)) sdifplus(1) = sdifplus(1) + largfour(2)*(hautfour(1)-hautfour(2))
		IF (hautfour(nx).gt.hautfour(nx-1)) sdifplus(nx) = sdifmoins(nx) + largfour(nx-1)*(hautfour(nx)-hautfour(nx-1))
		DO i = 2,nx-1
			IF(largfour(i).gt.largfour(i+1)) THEN
				sdifplus(i) = sdifplus(i)+ hautfour(i)*(largfour(i)-largfour(i+1))
			ELSE
				sdifplus(i)= sdifplus(i) + 0.
			ENDIF
			IF(largfour(i).gt.largfour(i-1)) THEN
				sdifmoins(i) = sdifmoins(i) + hautfour(i)*(largfour(i)-largfour(i-1))
			ELSE
				sdifmoins(i) = sdifmoins(i) + 0.
			ENDIF
			IF(hautfour(i).gt.hautfour(i+1)) THEN
				sdifplus(i) = sdifplus(i)+ largfour(i+1)*(hautfour(i)-hautfour(i+1))
			ELSE
				sdifplus(i)= sdifplus(i)+ 0.
			ENDIF
			IF(hautfour(i).gt.hautfour(i-1)) THEN
				sdifmoins(i) = sdifmoins(i) + largfour(i-1)*(hautfour(i)-hautfour(i-1))
			ELSE
				sdifmoins(i) = sdifmoins(i)+ 0.
			ENDIF
		ENDDO
		filename=casename(1:lcasename)//'_'//moduname(code_max)(1:longcar(moduname(code_max)))//'.geo_area'
		OPEN(unit=11,file=filename)
		WRITE(11,*) 'i  s  surfnord  hautfour  largfour  sdifplus   sdifmoins'
		WRITE(11,*) '********************************************************'
		DO j=1,nbzones
		  l=indmaille(j)
		  m=indmaille(j)+nbtranchzone(j)+1
		  DO i=l+1,m-1
		    s = surfnord(i)*( largfour(i) + 2*hautfour(i) ) + sdifplus(i) + sdifmoins(i)&
	&	      + surfnord(i)*( largfour(i) - largsole(i)) 
		    WRITE(11,803) i,s,surfnord(i),hautfour(i),largfour(i),sdifplus(i),sdifmoins(i)
		  ENDDO
		ENDDO
		WRITE(11,*) '********************************************************'
		CLOSE(11)
	ENDIF
!
	CALL get_err_coupling(NX,tps1,tps1anc,errtsom,errtmax,indtmax)
	tsortie=tgi1(nx)
	t_fum=tgi1(nx)
	tfourmax=tgi1(indtmax)
	tps1anc=tps1
!
!cth Emission des champs du four
	IF (code_calcul.ne.CODE_FOUR_ONLY) THEN
	  CALL set_interf_field(mycode_module,MODU_CHARGE,CHAMP_FLUX,ftotps,nx)
	  CALL set_interf_field(mycode_module,MODU_CHARGE,CHAMP_TEMPW,tpw1,1)
	  CALL set_interf_field(mycode_module,MODU_CHARGE,CHAMP_TEMPE,tpe1,1)
	ENDIF
!
222	FORMAT(i3,x,3(f15.3,1x)) 
271	FORMAT(i4,1x,f8.1,12(1x,f8.1))
803	FORMAT(i4,6(x,f8.4))
1015    FORMAT(1x,'----------------------------------------')
!cth1017	FORMAT('        COMBUSTION CHAMBER Iteration #',i5,' / cvg state =',e9.3,' >',e9.3 ' (required)')
1017	FORMAT(1x,'COMBUSTION CHAMBER Iteration #',i4,' / cvg state =',e9.3,' > ',e9.3,'(required)')
1018	FORMAT(1x,'COMBUSTION CHAMBER Iteration #',i4,' / cvg state =',e9.3,' < ',e9.3,'(required)')
!
! Fin de la subroutine
!---------------------
	END
