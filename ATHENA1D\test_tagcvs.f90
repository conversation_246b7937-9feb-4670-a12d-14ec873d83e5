      subroutine test_tagcvs
!****************************************************************************
!
! Teste la coherence des versions : couplage+four+charge
!
!****************************************************************************

      USE modshared

      implicit none

!
! Declaration des variables
!--------------------------

      INTEGER :: icode
      INTEGER :: longcar
      INTEGER :: longvers,longrev
      INTEGER :: longvload,longvfour
      INTEGER :: longvtest
      LOGICAL :: qerror

!cth
      qerror=.false.

      longvers=longcar(COUPTAG)
      longvload=longcar(ALLTAG(CODE_CHARGE))
      longvfour=longcar(ALLTAG(CODE_FOUR))

!cth Teste pour FOUR et CHARGE demandee
      if (longvers.ne.longvload) then
        qerror=.true.
      else if(COUPTAG(1:longvers).ne.ALLTAG(CODE_CHARGE)(1:longvload)) then
        qerror=.true.
      endif
      if (longvers.ne.longvfour) then
        qerror=.true.
      else if(COUPTAG(1:longvers).ne.ALLTAG(CODE_FOUR)(1:longvfour)) then
        qerror=.true.
      endif

      if (qerror) then
        print *,' '
        print *,' VERSIONS : DIFFER ??? '
        print *,'   -COUPLING : ',COUPTAG(1:longvers)
        print *,'   -FURNACE  : ',ALLTAG(CODE_FOUR)(1:longvfour)
        print *,'   -LOAD     : ',ALLTAG(CODE_CHARGE)(1:longvload)
        print *,' '
        STOP ' INCOMPATIBILITY '
      endif

!cth Dans le cas d un meme soft regroupant different module de charge
!cth par exemple GLASS1D qui concerne glass1d et gtm
      qerror=.false.
      do icode=1,code_max
        if (icode.ne.CODE_CHARGE.and.icode.ne.CODE_FOUR) then
          if (ALLTAG(icode).ne.'NOVALUE') then
            longvtest=longcar(ALLTAG(icode))
            if (longvers.ne.longvtest) then
              qerror=.true.
            else if(COUPTAG(1:longvers).ne.ALLTAG(icode)(1:longvtest)) then
              qerror=.true.
            endif
            if (qerror) then
              print *,' '
              print *,' VERSION : DIFFER FOR OTHER LOAD : ',MODUNAME(icode)
              print *,'   -COUPLING   : ',COUPTAG(1:longvers)
              print *,'   -FURNACE    : ',ALLTAG(CODE_FOUR)(1:longvfour)
              print *,'   -LOAD       : ',ALLTAG(CODE_CHARGE)(1:longvload)
              print *,' '
              print *,'   -OTHER LOAD : ',ALLTAG(icode)(1:longvtest)
              print *,' '
              STOP ' INCOMPATIBILITY '
            endif
          endif
        endif
      enddo

      if (cversout(1:4).eq.'BETA') then
        longvers=longcar(cversout)
        print *,' '
        print *,' ----------------------------------------------- '
        print *,' WARNING WARNING WARNING WARNING WARNING WARNING '
        print *,'     NO OFFICIAL DISTRIBUTION '
        print *,'     VERSION IN DEVELOPMENT : ',cversout(1:longvers)
        print *,' WARNING WARNING WARNING WARNING WARNING WARNING '
        print *,' ----------------------------------------------- '
        print *,' '
      endif

      return

      end
