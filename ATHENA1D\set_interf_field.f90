	SUBROUTINE set_interf_field(	emetteur  ,recepteur,      &
	&				code_champ,champ    ,nchamp)
!**********************************************************************
!
!	NAME		:	set_interf_field.f90
!
!	AUTHOR	:
!
!	FUNCTION	:
!
!	INPUTS	:
!
!	OUTPUTS	:
!
!	NOTES		:
!
!**********************************************************************
!
	USE modshared
	USE modinterf

	IMPLICIT NONE

	INTEGER :: emetteur,recepteur,code_champ
	INTEGER :: nchamp
	REAL, DIMENSION(nchamp) :: champ

!cth Emission four1 vers interface
	if (emetteur.eq.MODU_FOUR1) then
		if (code_champ.eq.CHAMP_FLUX.and.nchamp.eq.nxfour1) then
			ffour1=champ
		else if (code_champ.eq.CHAMP_TEMPW.and.nchamp.eq.1) then
			touest1=champ(1)
		else if (code_champ.eq.CHAMP_TEMPE.and.nchamp.eq.1) then
			test1=champ(1)
		else
			print *,' Emetteur et recepteur : ',emetteur,recepteur
			print *,' MAUVAIS CODE CHAMP OU NB VALEURS : ',code_champ,nchamp
			stop ' SET_INTERF_FIELD '
		endif
	else if (emetteur.eq.MODU_FOUR2) then
		if (code_champ.eq.CHAMP_FLUX.and.nchamp.eq.nxfour2) then
			ffour2=champ
		else if (code_champ.eq.CHAMP_TEMPW.and.nchamp.eq.1) then
			touest2=champ(1)
		else if (code_champ.eq.CHAMP_TEMPE.and.nchamp.eq.1) then
			test2=champ(1)
		else
			print *,' Emetteur et recepteur : ',emetteur,recepteur
			print *,' MAUVAIS CODE CHAMP OU NB VALEURS : ',code_champ,nchamp
			stop ' SET_INTERF_FIELD '
		endif
	else if (emetteur.eq.MODU_CHARGE.and.recepteur.eq.MODU_FOUR1) then
		if (code_champ.eq.CHAMP_TEMP.and.nchamp.eq.nxfour1) then
			tfour1=champ
		else if (code_champ.eq.CHAMP_FRINC.and.nchamp.eq.1) then
			frayinc=champ(1)
		else if (code_champ.eq.CHAMP_FRSPE.and.nchamp.eq.1) then
			frayspec=champ(1)
		else if (code_champ.eq.CHAMP_FCONV.and.nchamp.eq.1) then
			ffluconv=champ(1)
		else
			print *,' Emetteur et recepteur : ',emetteur,recepteur
			print *,' MAUVAIS CODE CHAMP OU NB VALEURS : ',code_champ,nchamp
			stop ' SET_INTERF_FIELD '
		endif
	else if (emetteur.eq.MODU_CHARGE.and.recepteur.eq.MODU_FOUR2) then
		if (code_champ.eq.CHAMP_TEMP.and.nchamp.eq.nxfour2) then
			tfour2=champ
		else if (code_champ.eq.CHAMP_FRINC.and.nchamp.eq.1) then
			frayinc=champ(1)
		else if (code_champ.eq.CHAMP_FRSPE.and.nchamp.eq.1) then
			frayspec=champ(1)
		else if (code_champ.eq.CHAMP_FCONV.and.nchamp.eq.1) then
			ffluconv=champ(1)
		else
			print *,' Emetteur et recepteur : ',emetteur,recepteur
			print *,' MAUVAIS CODE CHAMP OU NB VALEURS : ',code_champ,nchamp
			stop ' SET_INTERF_FIELD '
		endif
	else
		print *,' MAUVAIS CODE EMETTEUR OU RECEPTEUR : ',emetteur,recepteur,code_champ
		stop ' SET_INTERF_FIELD '
	endif

	END
