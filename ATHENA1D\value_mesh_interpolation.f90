      SUBROUTINE value_mesh_interpolation(nxsour,xsour,tempsour,&
                               &nxdest,xdest,tempdest)

! ***********************************************************************
!
! Interpolation d un champ destination a partir du source
! Les 2 maillages etant dans le meme sens
!
! ***********************************************************************

      IMPLICIT NONE

      INTEGER :: nxsour
      REAL, DIMENSION(nxsour) :: xsour
      REAL, DIMENSION(nxsour) :: tempsour
      INTEGER :: nxdest
      REAL, DIMENSION(nxdest) :: xdest
      REAL, DIMENSION(nxdest) :: tempdest

      INTEGER :: I,J
      REAL :: ratio
      
!cth Interpolation des valeurs de la destination a partir des valeurs source
      j=1
      DO i=1,nxdest
        DO WHILE(xsour(j).le.xdest(i))
          IF (j.eq.nxsour) EXIT
          j=j+1
        ENDDO
!cth Pour extrapolation possible en i=1
        if (j.eq.1) j=2

        ratio=(xdest(i)-xsour(j-1))/(xsour(j)-xsour(j-1))
        tempdest(i)=tempsour(j-1)+ratio*(tempsour(j)-tempsour(j-1))
      ENDDO

      END
