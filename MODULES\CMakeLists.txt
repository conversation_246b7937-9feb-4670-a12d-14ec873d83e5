# MODULES CMakeLists.txt
# This directory contains shared Fortran modules

# Create module directory if it doesn't exist
file(MAKE_DIRECTORY ${CMAKE_Fortran_MODULE_DIRECTORY})

# Find all .f90 files in this directory
file(GLOB MODULE_SOURCES "*.f90")

# Create object library for modules
add_library(modules STATIC ${MODULE_SOURCES})

# Set module output directory
set_target_properties(modules PROPERTIES
    Fortran_MODULE_DIRECTORY ${CMAKE_Fortran_MODULE_DIRECTORY}
)

# Include directories
target_include_directories(modules PUBLIC
    ${CMAKE_Fortran_MODULE_DIRECTORY}
)

# Compiler definitions
target_compile_definitions(modules PRIVATE
    $<$<BOOL:${WIN32}>:WIN32>
    $<$<BOOL:${WIN32}>:_WINDOWS>
)

message(STATUS "MODULES: Found ${MODULE_SOURCES}") 