      SUBROUTINE write_restart_comb()

      USE modTwoDRad
      USE modcouplage
      USE modshared

      IMPLICIT NONE

      INTEGER :: i, longcar
      CHARACTER*130 filename

!cth Informations four
 90     format('# ',a8,' # COMBUSTION CHAMBER RESTART FILE')
	      filename=casename(1:lcasename)//'_'//moduname(code_max)(1:longcar(moduname(code_max)))//'.rest'
        OPEN(unit=14,file=filename)
        WRITE(14,90) cversout
        WRITE(14,100) itercoupt,nx,indglobt
100     format(1x,3i6)
        WRITE(14,150) ' Iter   Tgaz   Tnord   TSUD   TOUEST   TEST   FNORD   FSUD   FOUEST   FEST   PRAD    FTSUD'
150     format(1x,a)
        DO i = 1, nx
          WRITE(14,200) i,tgi1(i),tpn1(i),tps1(i),tpw1,tpe1,fradpn(i),fradps(i),fradpw,fradpe,puissrad(i),ftotps(i)
200       format(1x,i5,5(f13.5),20(f13.5))
        END DO
        CLOSE(14)


      RETURN
      END
