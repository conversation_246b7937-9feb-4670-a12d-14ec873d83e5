# GLASS1D CMakeLists.txt
# Glass module executable

# Find all source files
file(GLOB GLASS_SOURCES "*.f90")
file(GLOB GLASS_F77_SOURCES "*.f")

# Main executable sources
set(GLASS_MAIN_SOURCES
    main_glass.f90
    modules_glass.f90
    # Old files
    mesh_tank_setting.f90
    solve_mass_bal_glass.f90
    fct_glass_viscosity.f90
    fct_glass_lambda.f90
    fct_glass_density.f90
    compute_convec_glass.f90
    fct_batch_lambda.f90
    solve_heat_bal_glass.f90
    solve_glass.f90
    write_bal_glass.f90
    write_bal_comb_glass.f90
    set_reversed_glass.f90
    # New files
    init_glass.f90
    init_temp_glass.f90
    write_data_glass.f90
    write_restart_glass.f90
    residu_glass.f90
    read_par_glass.f90
    read_geo_glass.f90
    read_phy_glass.f90
    read_bc_glass.f90
    read_ini_glass.f90
    set_bubblers.f90
    set_electrodes.f90
    set_source.f90
    put_tagcvs_glass.f90
    write_example_glass.f90
    # GTM files
    gtm_getbou.f90
    gtm_wait_for_gtm.f90
    gtm_read_share.f90
    gtm_write_share.f90
    gtm_ggridr.f90
    gtm_planrd.f90
    gtm_planca.f90
    gtm_gvalrd.f90
    gtm_intpol.f90
    gtm_copy_to_walls.f90
    gtm_prt_wait.f90
    gtm_wribou.f90
    gtm_copy_from_walls.f90
    gtm_gvalwr.f90
    gtm_read_glasin.f90
    # Graph files
    draw_graph_glass.f90
    init_graph_glass.f90
)

# Create executable
add_executable(glass1d ${GLASS_MAIN_SOURCES})

# Set module directory
set_target_properties(glass1d PROPERTIES
    Fortran_MODULE_DIRECTORY ${CMAKE_Fortran_MODULE_DIRECTORY}
)

# Include directories
target_include_directories(glass1d PRIVATE
    ${CMAKE_Fortran_MODULE_DIRECTORY}
    ${CMAKE_SOURCE_DIR}/GRAPH
)

# Compiler definitions
target_compile_definitions(glass1d PRIVATE
    GLASSCHARGE
    $<$<BOOL:${WIN32}>:WIN32>
    $<$<BOOL:${WIN32}>:_WINDOWS>
)

# Link dependencies
target_link_libraries(glass1d
    athena1d
    stdprops
    usbkey
    graph
    modules
)

# Fortran compiler flags for fixed format files
if(WIN32)
    set_source_files_properties(${GLASS_F77_SOURCES} PROPERTIES
        Fortran_FORMAT FIXED
        COMPILE_FLAGS "/extend_source:132"
    )
else()
    set_source_files_properties(${GLASS_F77_SOURCES} PROPERTIES
        Fortran_FORMAT FIXED
        COMPILE_FLAGS "-FI -extend-source"
    )
endif()

message(STATUS "GLASS1D: Main sources: ${GLASS_MAIN_SOURCES}")
message(STATUS "GLASS1D: F77 sources: ${GLASS_F77_SOURCES}") 