	  SUBROUTINE solve_heat_balance ( nx,         mb,        hbr,       mdeg,      &
                                  & hdeg,       dp,        dr,        debitglob, & 
                                  & hgaz,       Tgaz,      reacfour,  yO,        &
                                  & yN,         yCo2,      yh2o,      yAr,       &
                                  & yF,         puisstr,   dprsdt,    fcctr,     &
                                  & modresol,   nbresol,   linptr,     reshg,    &
                                  & resmaxhg)
!
! ******************************************************************************
!
!       Cette subroutine fait le bilan enthalpique dans le four
!
! ******************************************************************************

	IMPLICIT NONE

! Declaration des entrees 
!------------------------

	INTEGER, INTENT (IN) :: nx
	REAL, INTENT(IN), DIMENSION(nx) :: mb,hbr,mdeg,hdeg
	REAL, INTENT(IN), DIMENSION(0:nx+1) :: dp,dr
	REAL, INTENT(IN), DIMENSION(nx) :: debitglob,tgaz
	REAL, INTENT(IN) :: reacfour
	REAL, INTENT(IN), DIMENSION(nx) :: yO,yN,yCo2,yh2o,yAr,yF
	REAL, INTENT(IN), DIMENSION(nx) :: puisstr, dPrsdT, fcctr
	INTEGER, INTENT(IN) :: modresol, nbresol, linptr

! Sorties
!---------

	REAL, INTENT(INOUT), DIMENSION(0:nx+1) :: hgaz
	REAL, INTENT(INOUT) :: reshg,resmaxhg

! Variables internes
!--------------------

	REAL :: restbilan

	INTEGER :: i
	REAL :: dPrsdh, SourcePrad, hhh0, somhg
	REAL, DIMENSION(0:nx+1) :: hgaz2

	REAL :: resglob,sombrul,somdeg,somflux

! Foctions exterieures 
!---------------------

	REAL :: chamel
 
	INTEGER :: iresol

!
! Initialisations
!----------------

! Enthalpie emportee par les gaz sortants
!----------------------------------------

        resglob=0.0
        sombrul=0.0
        somdeg=0.0
        somflux=0.0

	somhg=0.0
	reshg=0.0
	resmaxhg=-1.0
	do iresol=1,nbresol

	  if (modresol.eq.1) then

	    do i=1,nx

!cth Terme dprsdt calcule dans rayonnement
              if (linptr.eq.1) then
	        dPrsdh = dPrsdT(i) / chamel(reacfour,yN(i),yO(i),yCO2(i),yH2O(i),yAr(i),yF(i),Tgaz(i)) 
              else
	        dPrsdh = 0.0
              endif
  
              SourcePrad = puisstr(i) - dPrsdh*hgaz(i)
	      restbilan=dp(i-1)*hgaz(i-1)+mb(i)*hbr(i)+dr(i+1)*hgaz(i+1)+mdeg(i)*hDEG(i)-SourcePrad-fcctr(i)
              if (iresol.eq.1) then
                reshg=reshg+(restbilan-dPrsdh*hgaz(i)-debitglob(i)*hgaz(i))
                somhg=somhg+hgaz(i)
                resmaxhg=max(resmaxhg,reshg/hgaz(i))
                resglob=resglob+mb(i)*hbr(i)+mdeg(i)*hDEG(i)-puisstr(i)-fcctr(i)
                sombrul=sombrul+mb(i)*hbr(i)
                somdeg=somdeg+mdeg(i)*hDEG(i)
                somflux=somflux+puisstr(i)+fcctr(i)
              endif

              hhh0=restbilan/(debitglob(i)+dPrsdh)
              hgaz(i)=hhh0

            enddo

          else if (modresol.eq.2) then

            hgaz2=hgaz
            do i=1,nx

!cth Terme dprsdt calcule dans rayonnement
              if (linptr.eq.1) then
	        dPrsdh = dPrsdT(i) / chamel(reacfour,yN(i),yO(i),yCO2(i),yH2O(i),yAr(i),yF(i),Tgaz(i)) 
              else
	        dPrsdh = 0.0
              endif
  
              SourcePrad = puisstr(i) - dPrsdh*hgaz2(i)
	      restbilan=dp(i-1)*hgaz2(i-1)+mb(i)*hbr(i)+dr(i+1)*hgaz2(i+1)+mdeg(i)*hDEG(i)-SourcePrad-fcctr(i)
              if (iresol.eq.1) then
                reshg=reshg+(restbilan-dPrsdh*hgaz(i)-debitglob(i)*hgaz(i))
                somhg=somhg+hgaz(i)
                resmaxhg=max(resmaxhg,reshg/hgaz(i))
                resglob=resglob+mb(i)*hbr(i)+mdeg(i)*hDEG(i)-puisstr(i)-fcctr(i)
                sombrul=sombrul+mb(i)*hbr(i)
                somdeg=somdeg+mdeg(i)*hDEG(i)
                somflux=somflux+puisstr(i)+fcctr(i)
              endif

              hhh0=restbilan/(debitglob(i)+dPrsdh)
              hgaz2(i)=hhh0

            enddo
            do i=nx,1,-1

!cth Terme dprsdt calcule dans rayonnement
	      dPrsdh = dPrsdT(i) / chamel(reacfour,yN(i),yO(i),yCO2(i),yH2O(i),yAr(i),yF(i),Tgaz(i)) 
  
              SourcePrad = puisstr(i) - dPrsdh*hgaz(i)
	      restbilan=dp(i-1)*hgaz(i-1)+mb(i)*hbr(i)+dr(i+1)*hgaz(i+1)+mdeg(i)*hDEG(i)-SourcePrad-fcctr(i)
              hhh0=restbilan/(debitglob(i)+dPrsdh)
              hgaz(i)=hhh0
  
            enddo
            hgaz=0.5*(hgaz2+hgaz)

          endif

        enddo

        reshg=reshg/somhg

!cth        print *,' '
!cth        print *,' SOLVE_HEAT: SORTIE NX : ',nx,(debitglob(nx)-dr(nx)),hgaz(nx)
!cth        print *,' --> ',mb(nx),hbr(nx)
!cth        print *,' --> ',mdeg(nx),hDEG(nx)
!cth        print *,' --> ',puisstr(nx),fcctr(nx)
!cth        print *,' --> ',sombrul,somdeg,somflux
!cth        print *,' --> ',resglob,sombrul+somdeg,somflux
!cth        print *,' --> ',(debitglob(nx)-dr(nx))*hgaz(nx)
        resglob=resglob-(debitglob(nx)-dr(nx))*hgaz(nx)
!cth        print *,' SOLVE_HEAT: RESIDU GLOBAL : ',resglob
!cth        print *,' '

9998    format(1x,10f12.2) 
9999    format(1x,10f10.4) 
!
! Fin du programme
!-----------------
	END SUBROUTINE	
