      subroutine read_int_int(nunit,nvaleur,ival1,ival2,qerr)

      implicit none

      integer :: nunit
      integer :: nvaleur
      integer :: ival1(nvaleur)
      integer :: ival2(nvaleur)
      logical :: qerr

      integer :: i

      qerr=.false.

      do i=1,nvaleur
        read(nunit,*,ERR=9998,END=9999) ival1(i),ival2(i)
      enddo

      return

9998  continue
      print *,' READ_INT_INT : ERREUR LECTURE '
      print *,' NVALEUR : ',nvaleur
      qerr=.true.
      return

9999  continue
      print *,' READ_INT_INT : END OF FILE '
      print *,' NVALEUR : ',nvaleur
      qerr=.true.
      return

      end
