	MODULE modBilan

	SAVE

	REAL :: puissload,puissparoi,puissloadcv,puissloadrad
	REAL :: puisssortiefum,puissbrul,puissgaz
	REAL :: puissparoiouest,puissparoiest
	REAL :: T_fum,F_fum,xCO2_fum,xH2O_fum,xO2_fum

	END MODULE
!-----------------------------------------------------------
	MODULE modTwoDRad

	SAVE

	LOGICAL  TestTwoDRad, AskForStop

	CHARACTER*80 :: lignemsg

!cth Code pour options expert
	INTEGER, PARAMETER :: CODEEXPERT=1234567890

!cth Nombre de zones maxi 
	INTEGER nbzonesmax
	PARAMETER (nbzonesmax=50)

!cth Nombre maxi de changement de hauteur ou largeur
	INTEGER maxhaut
	PARAMETER (maxhaut=50)

!cth Indice du four car 2 four pour REHEAT
	INTEGER, PARAMETER :: myindfour=1

!cth Parametres de remaillage et correction automatique
	LOGICAL :: qremailx,qremaily
	INTEGER :: autoremail,autocorrec,nxremail,nyremail
	REAL :: dxremail,dyremail
	LOGICAL :: qnxzone,qdxzone
	INTEGER, DIMENSION(nbzonesmax) :: nxzone
	REAL, DIMENSION(nbzonesmax) :: dxzone
	LOGICAL :: qnxbrul,qdxbrul
	INTEGER, DIMENSION(nbzonesmax) :: nxbrul
	REAL, DIMENSION(nbzonesmax) :: dxbrul
	REAL :: criterdist
	INTEGER :: nitermail
	INTEGER :: ioptimail

!cth Geometrie

	REAL :: LXMAX,LYMAX,LZMAX
	REAL :: Longfour

	INTEGER :: nbzones,nlongzone
	INTEGER :: NLARGFOUR,NHAUTFOUR
	INTEGER :: NLARGLOAD,NHAUTLOAD
	INTEGER :: NPOSBRUL ! si position dans .geo avec XBRULEUR

	REAL, DIMENSION(maxhaut) :: XLARGFOUR,XHAUTFOUR,XLARGLOAD,XHAUTLOAD
	REAL, DIMENSION(maxhaut) :: VLARGFOUR,VHAUTFOUR,VLARGLOAD,VHAUTLOAD
	REAL, DIMENSION(nbzonesmax) :: POSBRUL
	REAL, DIMENSION(nbzonesmax) :: poszone
	REAL, DIMENSION(nbzonesmax) :: posdebzone,posfinzone
	INTEGER, DIMENSION(nbzonesmax) :: brulzone
	INTEGER, DIMENSION(nbzonesmax) :: zonebrul
	LOGICAL :: qzone,qbrul
	REAL, DIMENSION(nbzonesmax) :: longzone

	INTEGER, DIMENSION(nbzonesmax) :: nbtranchzone,indmaille
	LOGICAL, DIMENSION(nbzonesmax) :: qdefent,qdefsor
	REAL, DIMENSION(nbzonesmax) :: mbruleurz,mcomb
	REAL, DIMENSION(:), ALLOCATABLE :: longzone_grid  !ccri for vitflame

	INTEGER :: NX,NY
	INTEGER :: NXNOEUD,NYNOEUD
	REAL, ALLOCATABLE, DIMENSION(:) :: X,DX,Y,DY
	REAL, ALLOCATABLE, DIMENSION(:) :: DXEQUIV
	REAL, ALLOCATABLE, DIMENSION(:) :: GRIDX,GRIDY
	REAL, ALLOCATABLE, DIMENSION(:) :: haut2
	REAL :: Haut
	REAL, ALLOCATABLE, DIMENSION(:) :: HAUTFOUR,LARGFOUR
	REAL, ALLOCATABLE, DIMENSION(:) :: LARGSOLE
	REAL, ALLOCATABLE, DIMENSION(:) :: dimen
	INTEGER :: Nval,nvalF
	REAL, ALLOCATABLE, DIMENSION(:) :: VML,XML
	REAL, ALLOCATABLE, DIMENSION(:) :: ValF,XFL
	REAL, DIMENSION(:), ALLOCATABLE :: surfnord
	REAL :: surfest,surfouest
	REAL, DIMENSION(:), ALLOCATABLE :: surfconvpn,surfconvps
	REAL :: surfconvpw,surfconvpe

	INTEGER nbtsi,nang,nbrayon,nbrayon4,nggcod
	REAL, DIMENSION(:), ALLOCATABLE :: theta,dtheta,alpha
	REAL, DIMENSION(:), ALLOCATABLE :: tsi,dtsi,dALPHA
	REAL raison, ang1
	REAL pas

	INTEGER :: nxp1,nxp2,nyp1,nyp2,np
	REAL, DIMENSION(:), ALLOCATABLE :: pos
	REAL, DIMENSION(:), ALLOCATABLE :: emis,gamma,surf
	REAL, DIMENSION(:,:), ALLOCATABLE :: vol,xyp,xycoin
	INTEGER, DIMENSION (:), ALLOCATABLE :: direc
	LOGICAL, DIMENSION(:,:), ALLOCATABLE :: interdit,coin,interior
	INTEGER controlmaillrayon
	LOGICAL RECTANGULAIRE

!cth Degre maximal pour forme polynomiale (hinfini, flux impose, ...)
	INTEGER ndegremax
	PARAMETER (ndegremax=5)
	INTEGER ncoefmax
	PARAMETER (ncoefmax=ndegremax+1)

!cth Degre polynome pour perte paroi
	INTEGER, DIMENSION(:), ALLOCATABLE :: ncoefsud,ncoefnord
!cth Type polynome pour perte paroi : 0=temperature, 1=coordonnee x
	INTEGER, DIMENSION(:), ALLOCATABLE :: Ivarsud,Ivarnord
!cth Condition au limite parois
	INTEGER :: modtp
	INTEGER :: modcvref, modcvbrul, modcvload
	REAL :: hcvref, hcvbrul, hcvload
	REAL :: corcvref, corcvbrul, corcvload
	REAL, ALLOCATABLE, DIMENSION(:,:) :: coefsud,coefnord
	INTEGER, DIMENSION(:), ALLOCATABLE :: typecondsud,typecondnord
	REAL, DIMENSION(:), ALLOCATABLE :: tinfnord,hinfnord,fimpnord
	REAL, DIMENSION(:), ALLOCATABLE :: tinfsud,hinfsud,fimpsud
	INTEGER  :: typecondouest,typecondest
	REAL :: tinfouest,hinfouest,fimpouest
	REAL :: tinfest,hinfest,fimpest
	REAL, DIMENSION(:), ALLOCATABLE :: epsv,epsp
!cth Temperatures admissibles
!cth      MIS EN COMMON POUR FACILITER ACCES POUR ROUTINES DE PROPRIETES
!cth      QUI N'UTILISENT PAS LES MODULES
!cth	REAL :: tgadmimin,tgadmimax
!cth	REAL :: tpadmimin,tpadmimax
        include 'temp_admis.cmn'

!cth Temperatures initiales
	CHARACTER*1 ctempini
	REAL :: tginideb,tginifin
	REAL :: tpninideb,tpninifin
	REAL :: tpwini,tpeini

!cth Temperature parois
	REAL, DIMENSION(:), ALLOCATABLE :: TpN1,TpN2,TpN21
	REAL, DIMENSION(:), ALLOCATABLE :: tps1,tps2,tps21
	REAL, DIMENSION(:), ALLOCATABLE :: tps1anc
	REAL :: TpE1,TpE2,TpE21
	REAL :: TpW1,TpW2,TpW21
!cth Composition combustible, oxydant dans fichier .phy
	INTEGER :: ncodmax
	INTEGER :: ncombesp,noxyesp
	logical :: qcompo,qstoechio,qvolume,qcombmas
	logical :: qoxydant,qhumid,qoxymas
	real :: pcisto,r_o2_flu,r_co2_flu,r_h2o_flu
	real :: pcivol,volair,volfuel
	real :: humid,thumid
	real :: oxymas,oxysmas
	integer, parameter :: maxoxydant=3
	integer, parameter :: maxcaroxy=10
	integer :: nboxydant
	CHARACTER*(maxcaroxy), ALLOCATABLE, DIMENSION(:) :: nomoxydant
	INTEGER, ALLOCATABLE, DIMENSION(:) :: lnomoxydant
	REAL, ALLOCATABLE, DIMENSION(:) :: xcombmol,xcombmas
	REAL, ALLOCATABLE, DIMENSION(:,:) :: xoxymol,xoxymas
	REAL, ALLOCATABLE, DIMENSION(:,:) :: xsoxymol,xsoxymas
!cth Fractions gaz par maille
	REAL, ALLOCATABLE, DIMENSION(:) :: yN,yH2O,yO,yCO2,yAr,yF,yCO
!cth Proprietes gaz
	REAL, DIMENSION(:), ALLOCATABLE :: rhogaz,visgaz,cpgaz
!cth Temperature gaz
	REAL, DIMENSION(:), ALLOCATABLE :: hgaz,hgaz1,hgaz2
	REAL, DIMENSION(:), ALLOCATABLE :: Tgi1,Tgi2,Tgi21
	INTEGER, DIMENSION(:,:), ALLOCATABLE :: Zonegaz
!cth Parametres pour recirculation
	CHARACTER*1 crecirc
	REAL :: valrecirc
!cth Debits gaz
	REAL, DIMENSION(:), ALLOCATABLE :: DP,DR,DEBITGLOB,DEBITCONV,vitconv
	REAL, DIMENSION(:), ALLOCATABLE :: vitflame  !ccri for Callidus correlation 
	REAL, DIMENSION(:), ALLOCATABLE :: WMfum
	REAL, DIMENSION(:), ALLOCATABLE :: SOR
	REAL, DIMENSION(:), ALLOCATABLE :: v
!cth Bruleurs
	REAL, DIMENSION(:), ALLOCATABLE :: ybN,ybO,ybCO2,ybH2O,ybAr
	REAL, DIMENSION(:), ALLOCATABLE :: ybF,YBCO
	REAL, DIMENSION(:), ALLOCATABLE :: hbr
	REAL, ALLOCATABLE, DIMENSION(:) :: mb,WMbrul
	REAL, DIMENSION(:), ALLOCATABLE :: TBR
	REAL, DIMENSION(:), ALLOCATABLE :: fracomb_grid  !ccri for vitflame
!cth Degazage
	REAL, DIMENSION(:), ALLOCATABLE :: yNDEG,yODEG,yCO2DEG,yH2ODEG,yArDEG
	REAL, DIMENSION(:), ALLOCATABLE :: yFDEG,YCODEG
	REAL, DIMENSION(:), ALLOCATABLE :: hdeg
	REAL, DIMENSION(:), ALLOCATABLE :: DEG
!cth Debit globaux
	REAL :: dvcombreal,dvcombglo
	REAL :: dvoxyreal,dvoxyglo,dvsoxyreal,dvsoxyglo,exoxyglo
	REAL :: dvfumeereal,dvfumeeglo
	REAL, ALLOCATABLE, DIMENSION(:) :: xfumeemol
!cth Rayonnement
	INTEGER :: linptr
	REAL, DIMENSION(:), ALLOCATABLE :: puisstr
	REAL, DIMENSION(:), ALLOCATABLE :: puissrad
	REAL, DIMENSION(:), ALLOCATABLE :: puissradanc
!cth Pour linearisation terme source rayonnement pour enthalpie
	REAL, DIMENSION(:), ALLOCATABLE :: dPrsdT
	REAL, DIMENSION(:), ALLOCATABLE :: dPrsdTanc
!cth Flux parois
	REAL, DIMENSION(:), ALLOCATABLE :: fradps,fccps,hccps,ftotps
	REAL, DIMENSION(:), ALLOCATABLE :: fradpn,fccpn,hccpn,ftotpn
	REAL, DIMENSION(:), ALLOCATABLE :: fradpsanc,fradpnanc
	REAL :: fradpw,fccpw,hccpw,ftotpw
	REAL :: fradpe,fccpe,hccpe,ftotpe
	REAL :: fradpeanc, fradpwanc
!cth Flux convectif par tranche
	REAL, DIMENSION(:), ALLOCATABLE :: fcctr

	REAL, ALLOCATABLE, DIMENSION(:) :: puissbruz,puisspci
	REAL, ALLOCATABLE, DIMENSION(:) :: puissrech
	REAL, ALLOCATABLE, DIMENSION(:) :: puissparoiz
	REAL, ALLOCATABLE, DIMENSION(:) :: puissloadz
	REAL :: puissparoiperdu

!cth Modele rayonnement
	INTEGER icor_ray, modrad
!cth Modele pour resolution enthalpie
	INTEGER modresol
	INTEGER nbresol
!cth Redemarrage
	INTEGER redem
!cth Sous relaxation et convergence
	REAL :: ratioconv ! cth pour test convergence
	INTEGER :: nitergmax,niteremax ! cth pour test convergence
	INTEGER :: niterpmax,niterrfmax,niterrpmax ! cth pour test convergence
	REAL :: relaxgmin,relaxemin ! cth pour test convergence
	REAL :: relaxgdon,relaxedon ! cth pour test convergence
	REAL :: relaxpdon,relaxparoi,relaxpmin ! cth pour test convergence
	REAL :: relaxrfdon,relaxrflux,relaxrfmin ! cth pour test convergence
	REAL :: relaxrpdon,relaxrpuis,relaxrpmin ! cth pour test convergence
	REAL :: relaxgaz,relaxent,relaxray
	REAL :: errorgaz,errorent,errorrad

	REAL :: errtg,errtn,errts,errtw,errte
	REAL :: errfg,errfn,errfs,errfw,errfe
	REAL :: errpr,errpt
	REAL :: errsomfs,errmaxfs
	REAL :: reshg,resmaxhg
	INTEGER :: indmaxfs

!cth Iterations calcul
	INTEGER :: itergazmin,itergazmax
	INTEGER :: iterlocal
	INTEGER :: indglob,indglob0,indglobt

!cth Option d ecriture des fichiers
	INTEGER :: iecrini,iecrprof,iecrray,iecrflu,iecrcoef

!cth Commons VOIR SI VRAIMENT UTILE
	REAL :: REACBRUL, REACFOUR
	COMMON/combustion/REACBRUL,REACFOUR
	COMMON/message/lignemsg

!cth Definition de l interface pour cle USB
!cth La definition de l interface d appel est necessaire
!cth pour utiliser une DLL (qui sera encapsulee)
        INTERFACE
          SUBROUTINE init_air_usb_key(myindfour,basename,lbasename)
#ifdef Windows_NT
!DEC$ ATTRIBUTES DLLIMPORT :: init_air_usb_key
#endif

            INTEGER :: myindfour
            CHARACTER*(*) basename
            INTEGER :: lbasename

          END
	END INTERFACE

	END MODULE
!----------------------------------------------------------------------
	MODULE modfacteur

	SAVE

	REAL :: frayinci
	REAL :: frayspec
	REAL :: fconvect

	END MODULE
!----------------------------------------------------------------------
	MODULE modcodefour

	SAVE

	INTEGER :: mycode_module
	INTEGER :: mycode_four

!cth  Version de l'exe 
        CHARACTER*80, PARAMETER  :: FOURTAG='$Name:  $'
        CHARACTER*80, PARAMETER  :: FOURREV='$Revision: 1.5 $'
        CHARACTER*8 :: FOURVERS=' '

	END MODULE
!----------------------------------------------------------------------
	MODULE modinterf

	SAVE

        INTEGER :: code_emetteur,code_recepteur

        INTEGER :: NXFOUR1,NXFOUR2,NXCHARGE
        INTEGER :: NPFOUR1,NPFOUR2,NPCHARGE

        REAL, ALLOCATABLE, DIMENSION(:) :: TFOUR1,TFOUR2
        REAL :: TOUEST1,TOUEST2
        REAL :: TEST1,TEST2
        REAL, ALLOCATABLE, DIMENSION(:) :: FFOUR1,FFOUR2
        REAL :: FRAYINC,FRAYSPEC,FFLUCONV

 	END MODULE
!----------------------------------------------------------------------
      MODULE modlim

      SAVE

      integer,parameter :: maxfront=50
      integer,parameter :: maxesp=25
!cth Entrees globales combustible
      logical :: qcombglo,qfcombglo,qmcombglo
      real :: dcombglo,tcombglo
!cth Entree combustibles par bruleur, zone ou position
      integer :: necomb
      character*1 descomb(maxfront)
      integer, dimension(maxfront) :: idcomb,ifcomb,irepcomb
      logical, dimension(maxfront) :: qbrulcomb,qposcomb
      real, dimension(maxfront) :: poscomb
      real, dimension(maxfront) :: fracomb,debcomb,tempcomb,crepcomb
      logical, dimension(maxfront) :: qfracomb,qmcomb,qdefcomb
!cth Entrees globales oxydant
      logical :: qoxyglo,qeoxyglo,qsoxyglo,qmoxyglo
      integer :: indoxyglo
      real :: doxyglo,eoxyglo,toxyglo
!cth Entree oxydant par bruleur, zone ou position
      integer :: neoxy
      character*1 desoxy(maxfront)
      integer, dimension(maxfront) :: idoxy,ifoxy,irepoxy
      logical, dimension(maxfront) :: qbruloxy,qposoxy
      integer, dimension(maxfront) :: indoxy
      real, dimension(maxfront) :: posoxy
      real, dimension(maxfront) :: fraoxy,deboxy,exeoxy,tempoxy,crepoxy
      integer, dimension(maxfront) :: nespoxy
      real, dimension(maxesp,maxfront) :: xmoloxy,xmasoxy
      real, dimension(maxesp,maxfront) :: xsmoloxy,xsmasoxy
      logical, dimension(maxfront) :: qfraoxy,qeoxy,qsoxy,qmoxy,qdefoxy
      logical, dimension(maxfront) :: qcompoxy,qmcompoxy
!cth Entree parasite par bruleur, zone ou position
      integer :: nepara
      character*1 despara(maxfront)
      integer, dimension(maxfront) :: idpara,ifpara,ireppara
      logical, dimension(maxfront) :: qpospara
      real, dimension(maxfront) :: pospara
      real, dimension(maxfront) :: debpara,temppara,creppara
      integer, dimension(maxfront) :: nesppara
      real, dimension(maxesp,maxfront) :: xmolpara,xmaspara
      logical, dimension(maxfront) :: qmpara,qdefpara,qcomppara,qmcomppara
!cth Parois
      integer :: nparoiall
      logical :: qparoiall
      real :: epsparoiall,hparoiall,tparoiall
      integer :: iconvparoiall
      integer :: nparoi
      character*1 desparoi(maxfront)
      character*1 oriparoi(maxfront)
      character*1 typparoi(maxfront)
      character*80 ficparoi(maxfront)
      character*80 ficdegparoi(maxfront)
      integer, dimension(maxfront) :: lficparoi,lficdegparoi
      integer, dimension(maxfront) :: izdparoi,izfparoi
      integer, dimension(maxfront) :: idparoi,ifparoi
      logical, dimension(maxfront) :: qposparoi
      real, dimension(maxfront) :: xdparoi,xfparoi,epsparoi,hparoi,tparoi,degparoi
      integer, dimension(maxfront) :: iconvparoi
!cth Couplage
      integer :: ncoupall
      logical :: qcoupall
      real :: epscoupall
      integer :: iconvcoupall
      integer :: ncouplage
!cth Sortie
      integer :: nsortie
      character*1 dessortie(maxfront)
      character*1 cfsortie(maxfront)
      character*80 ficsortie(maxfront)
      integer, dimension(maxfront) :: idsortie
      logical, dimension(maxfront) :: qdefsortie
      real, dimension(maxfront) :: xdsortie,dsortie

      real :: verdebcomb,verdeboxy
      real :: verfracomb,verfraoxy

      END MODULE
!----------------------------------------------------------------------
      MODULE modespece

      SAVE

      INTEGER, parameter :: ncmaxstd=21
!cth En fait le C4H10 est connu par SMR
!cth au niveau des proprietes
!cth      INTEGER, parameter :: ncmaxsmr=10
      INTEGER, parameter :: ncmaxsmr=11

      INTEGER, parameter :: ncodesp=21
      INTEGER, parameter :: cespsize=5
      character*(cespsize) codesp(ncodesp)

      INTEGER, parameter :: icodch4=1
      INTEGER, parameter :: icodh2o=2
      INTEGER, parameter :: icodco=3
      INTEGER, parameter :: icodh2=4
      INTEGER, parameter :: icodco2=5
      INTEGER, parameter :: icodn2=6
      INTEGER, parameter :: icodar=7
      INTEGER, parameter :: icodc2h6=8
      INTEGER, parameter :: icodc3h8=9
      INTEGER, parameter :: icodo2=10
!cth En fait le C4H10 est connu par SMR
!cth au niveau des proprietes
!cth on le defini donc en icode=11
!cth et on le permute avec le C2H4
!cth      INTEGER, parameter :: icodc2h4=11
      INTEGER, parameter :: icodc4h10=11
      INTEGER, parameter :: icodc3h6=12
      INTEGER, parameter :: icodc4h8=13
!cth      INTEGER, parameter :: icodc4h10=14
      INTEGER, parameter :: icodc2h4=14
      INTEGER, parameter :: icodc5h12=15
      INTEGER, parameter :: icodc5h10=16
      INTEGER, parameter :: icodc6h14=17
      INTEGER, parameter :: icodc7h16=18
      INTEGER, parameter :: icodc6h12=19
      INTEGER, parameter :: icodc7h8=20
      INTEGER, parameter :: icodc8h18=21

      INTEGER, parameter :: ncodsto=4
      INTEGER, parameter :: cstosize=10
      character*(cstosize) codsto(ncodsto)

      INTEGER, parameter :: icodspci=1
      INTEGER, parameter :: icodso2=2
      INTEGER, parameter :: icodsco2=3
      INTEGER, parameter :: icodsh2o=4

      INTEGER, parameter :: ncodvol=3
      INTEGER, parameter :: cvolsize=5
      character*(cvolsize) codvol(ncodvol)

      INTEGER, parameter :: icodvpci=1
      INTEGER, parameter :: icodvair=2
      INTEGER, parameter :: icodvfuel=3

!cth En fait le C4H10 est connu par SMR
!cth au niveau des proprietes
!cth on le defini donc en icode=11
!cth et on le permute avec le C2H4
      data codesp/'CH4  ',&
     &            'H2O  ',&
     &            'CO   ',&
     &            'H2   ',&
     &            'CO2  ',&
     &            'N2   ',&
     &            'AR   ',&
     &            'C2H6 ',&
     &            'C3H8 ',&
     &            'O2   ',&
     &            'C4H10',&
     &            'C3H6 ',&
     &            'C4H8 ',&
     &            'C2H4 ',&
     &            'C5H12',&
     &            'C5H10',&
     &            'C6H14',&
     &            'C7H16',&
     &            'C6H12',&
     &            'C7H8 ',&
     &            'C8H18'/

      data codsto/'PCI       ',&
     &            'R_O2_FUEL ',&
     &            'R_CO2_FUEL',&
     &            'R_H2O_FUEL'/

      data codvol/'PCI  ',&
     &            'VAIR ',&
     &            'VFUEL'/

      END MODULE



