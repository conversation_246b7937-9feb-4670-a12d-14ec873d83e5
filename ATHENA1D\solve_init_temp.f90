	SUBROUTINE solve_init_temp (itercoup)
! *************************************************************************
!
!       LOGICIEL DE MODELISATION DES TRANSFERTS THERMIQUES DANS LA CHAMBRE DE
!       COMBUSTION D UN FOUR.
!       LA CHARGE PEUT ETRE REPRESENTEE PAR DIFFERENTES CONDITIONS AUX LIMITES
!       ON A CONSTRUIT UN MODELE PSEUDO 2D ET UN MODELE 1D
!
!       LE PROGRAMME UTILISE UNE METHODE WSGG (SOM<PERSON> PONDEREE DE GAZ GRIS),
!       ET UNE DISCRETISATION ANGULAIRE POUR LES TRANSFERTS PAR RAYONNEMENT,
!       REALISES D APRES JL Rios Sanchez
!
!
!       Modifications : Florent Cha<PERSON>otte (08.00)
!                       Modifie le 15/02/02
!                       Pour fonctionnement en four seul
!
!                       <PERSON><PERSON><PERSON>
!                       Mise au propre des programmes
!
!						(03/05) Y.<PERSON><PERSON>
!						Suppression de perte-four.out
! *************************************************************************

	USE modTwoDRad
	USE modfacteur
	USE modcodefour
	USE modcouplage
	USE modshared

	IMPLICIT NONE
!
	CHARACTER*130  filename

	INTEGER :: itercoup

	REAL, PARAMETER :: SMALL=1.0e-6

	INTEGER longcar, i
	INTEGER iter,itergaz

	LOGICAL :: QERR, debut

	REAL :: REACNUL,pi
	REAL :: PCI,WM,R_O2_FUEL,R_CO2_FUEL
	REAL :: R_H2O_FUEL,R_N2_FUEL,INVWM,R_H2O_CO2,R_CO2_O2,R_N2_O2

	REAL :: err,errrel

	REAL :: hc0
	INTEGER, DIMENSION(4) :: typecond

	REAL ::  fct_density ! fonctions externes

	REAL :: somme

!cth Pour calcul hinfini ou flux impose polynomial
	INTEGER :: kcoef
	REAL :: varpol,respol

	REAL, PARAMETER :: pg=1.e5 !cth pression pour denmel

!cth Pour test convergence
	INTEGER :: itermax
	INTEGER :: ierrmax
	REAL :: errorconv,erriter1
	REAL :: diff

	INTEGER :: ires (3)
	COMMON/impr/ires
	COMMON/combustible/PCI,WM,R_O2_FUEL,R_CO2_FUEL&
                          &,R_H2O_FUEL,R_N2_FUEL,INVWM,R_H2O_CO2,R_CO2_O2,R_N2_O2
	COMMON/CL/typecond
!
! Debut du programme
!-------------------

        qerr=.false.
	pi=4.*atan(1.)
	reacnul=0.
	debut=.true.
	somme = 0.

	 hc0=5.
    
	 iter=1
	 err=100.
	 errrel=1.

	 itergaz=1
!
! ----------------------------------------------------------------------
!
!    DEBUT DE LA BOUCLE ITERATIVE INTERNE A SOLVE_COMB
!
! ----------------------------------------------------------------------

         PRINT 1016
         PRINT *,' '

1001	 continue

	 Zonegaz=0

!
! Calcul radiatif.
! -----------------------------------------------------------------------------
! Quelques remarques sur le calcul radiatif: on a introduit deux manieres
! de ccalculer les flux radiatifs. L une s apparente a la methode de la sphere
! d Hottel (4), l autre est une methode de transferts discrets (3), plus precise
! On essaye de determiner une solution approximative a l aide de 4, puis
! de converger vers la bonne soution avec 3
!------------------------------------------------------------------------------
! Programme rayonWSGG, sorties:	fradps : phiR paroi sud
!				fradpn : phiR paroi nord
!				fradpe : paroi est (scalaire)
!				fradpw : paroi ouest (scalaire)
!------------------------------------------------------------------------------

	fradpsanc=fradps
	fradpnanc=fradpn
	fradpeanc=fradpe
	fradpwanc=fradpw
        puissradanc=puissrad
	 fradps=1.0e-15
	 fradpn=0.
	 fradpe=0.
	 fradpw=0.
	 puisstr=1.0e-15
	 puissrad=1.0e-15

	IF (modrad.eq.2) THEN
     
	  CALL solve_radiation_simplified (NX,ny,nang,nbtsi,nxp1,nxp2,&
               &nyp1,nyp2,np,tps1,Tpn1,Tpe1,Tpw1,Tgi1,yCO2,yH2O,&
               &yAr,yN,yO,yF,hautfour,largfour,&
               &theta,dtheta,tsi,dtsi,&
               &interdit,x,y,&
               &gridx,gridy,xyp,xycoin,emis,gamma,coin,direc,surf,&
               &fradps,fradpn,fradpe,fradpw,RECTANGULAIRE,&
               &errorrad,nggcod)
        ELSEIF(modrad.ne.0.and.modrad.ne.2) then
          PRINT*,'*********************************************************'
          PRINT*,'                  ARRET dans ATMO'
          PRINT*,' La variable modrad different de 0 ou 2 dans *.ipar'
          PRINT*,'*********************************************************'
          STOP
	ENDIF

!cth Pour voir sous relax de la puissance transmise
        if (indglob.eq.1) then
          do i=1,NX
            fradpsanc(i)=0.9*fradps(i)
            fradpnanc(i)=0.9*fradpn(i)
            fradpeanc=0.9*fradpe
            fradpwanc=0.9*fradpw
          enddo
        endif
        do i=1,NX
          fradps(i)=fradpsanc(i)+relaxrflux*(fradps(i)-fradpsanc(i))
          fradpn(i)=fradpnanc(i)+relaxrflux*(fradpn(i)-fradpnanc(i))
        enddo
        fradpe=fradpeanc+relaxrflux*(fradpe-fradpeanc)
        fradpw=fradpwanc+relaxrflux*(fradpw-fradpwanc)

!
! bilan parois
!------------------

!cth calcul vitesse pour convection
        DO i=1, NX
          vitconv(i)=debitconv(i)/(fct_density(YN(i),YO(i),YCO2(i),YH2O(i),&
     &      YF(i),Tgi1(i),Pg)*Largfour(i)*hautfour(i))
        ENDDO
!ccri calcul vitesse flame pour convection dans la correlation Callidus       
	DO i=1,NX !ccri-compute vitflame
            vitflame(i)=fracomb_grid(i)*(sum(mb))/9./(fct_density(YN(i),YO(i),YCO2(i),YH2O(i),&
     &      YF(i),Tbr(i),Pg)*2.0*3.1415*0.07*0.07)
        ENDDO

	Tgi2=Tgi1
	TpN2=TpN1
	TpW2=TpW1
	TpE2=Tpe1
	tps2=tps1

!cth Calcul en sud hinfini ou flux impose si polynome
        DO i=1, NX
          if (typecondsud(i).eq.1.or.typecondsud(i).eq.3) then
            respol=0.
            if (IVARsud(i).eq.0) varpol=tps1(I)
            if (IVARsud(i).eq.1) varpol=X(I)
            DO kcoef=ncoefsud(i),2,-1
               respol=(respol+COEFSUD(kcoef,I))*varpol
            ENDDO
            respol=respol+COEFSUD(1,i)
            if (typecondsud(i).eq.1) hinfsud(i)=respol
            if (typecondsud(i).eq.3) fimpsud(i)=respol
          endif
        ENDDO

!cth Calcul en nord hinfini ou flux impose si polynome
        DO i=1, NX
          if (typecondnord(i).eq.1.or.typecondnord(i).eq.3) then
            respol=0.
            if (IVARnord(i).eq.0) varpol=TpN1(I)
            if (IVARnord(i).eq.1) varpol=X(I)
            DO kcoef=ncoefnord(i),2,-1
               respol=(respol+COEFNORD(kcoef,I))*varpol
            ENDDO
            respol=respol+COEFNORD(1,i)
            if (typecondnord(i).eq.1) hinfnord(i)=respol
            if (typecondnord(i).eq.3) fimpnord(i)=respol
          endif
        ENDDO
        if (typecondouest.eq.3) fimpouest=fimpnord(1)
        if (typecondest.eq.3) fimpest=fimpnord(nx)

!cth Bilan et temperatures sur parois

        call solve_bc_heat_balance ( nx,        vitconv,      Tgi2,     yO, &  !Paroi nord
                             & yN,        yCo2,         yh2o,     yAr,      &
                             & yF,        typecondnord, Tpn2,     fradpn,   &
                             & epsp,      hinfnord,     tinfnord, fimpnord, &
                             & modtp,     fccpn,        hccpn,    lxmax,    &
                             & tpadmimin, tpadmimax,    errorent, relaxent, &
                             & corcvbrul ,modcvbrul,    Tbr,   vitflame, longzone_grid,hcvbrul)

        call solve_bc_heat_balance ( nx,        vitconv,      Tgi2,     yO, &  !Paroi sud
                             & yN,        yCo2,         yh2o,     yAr,      &
                             & yF,        typecondsud,  tps2,     fradps,   &
                             & epsv,      hinfsud,      tinfsud,  fimpsud,  &
                             & modtp,     fccps,        hccps,    lxmax,    &
                             & tpadmimin, tpadmimax,    errorent, relaxent, &
                             & corcvload,  modcvload,   Tbr,    vitflame, longzone_grid,hcvload)

        call solve_bc_heat_balance ( 1,         vitconv(1),    Tgi2(1),   yO(1), &  !Paroi ouest
                             & yN(1),     yCo2(1),       yh2o(1),   yAr(1),      &
                             & yF(1),     typecondouest, Tpw2,      fradpw,      & 
                             & epsp(1),   hinfouest,     tinfouest, fimpouest,   &
                             & modtp,     fccpw,         hccpw,     hautfour(1), &
                             & tpadmimin, tpadmimax,     errorent,  relaxent,    &
                             & corcvref, modcvref,      Tbr(1),    vitflame(1),longzone_grid(1),hcvref)

        call solve_bc_heat_balance ( 1,         vitconv(nx),   Tgi2(nx),  yO(nx), &  !Paroi est
                             & yN(nx),    yCo2(nx),      yh2o(nx),  yAr(nx),      &
                             & yF(nx),    typecondest,   Tpe2,      fradpe,       &
                             & epsp(nx),  hinfest,       tinfest,   fimpest,      &
                             & modtp,     fccpe,         hccpe,     hautfour(nx), &
                             & tpadmimin, tpadmimax,     errorent,  relaxent,     &
                             & corcvref,  modcvref,     Tbr(nx),    vitflame(nx), longzone_grid(nx),hcvref)

!cth Flux convectif
        DO i=1, NX
          fcctr(i)=fccpn(i)*surfconvpn(i)+fccps(i)*surfconvps(i)
        ENDDO
        fcctr(1)=fcctr(1)+fccpw*surfconvpw
        fcctr(nx)=fcctr(nx)+fccpe*surfconvpe

        ftotps=fradps+fccps
	ftotpN=fradpN+fccpN
	ftotpE=fradpE+fccpE
	ftotpW=fradpW+fccpW

        if (indglobt.eq.1.and.ires(1).ge.1) then
	      filename=casename(1:lcasename)//'_'//moduname(code_max)(1:longcar(moduname(code_max)))//'.flux_ini'
          open(unit=27,file=filename)
          write(27,150) '  Set furnace temperature and fluxes '
          write(27,150) ' Iter  FNORD    FSUD    FWEST    FEST'
          do i=1,nx
            write(27,100) i,ftotpN(i),ftotps(i),ftotpW,ftotpE
          enddo
          close(unit=27)
	      filename=casename(1:lcasename)//'_'//moduname(code_max)(1:longcar(moduname(code_max)))//'.temp_ini'
          open(unit=27,file=filename)
          write(27,150) '  Set furnace temperature and fluxes '
          write(27,150) ' Iter  TGAZ   TNORD   TSUD   TWEST   TEST'
          do i=1,nx
!cth            write(27,100) i,tgi2(i),TpN2(i),tps1(2),TpW2,tpe2
            write(27,100) i,tgi2(i),TpN2(i),tps1(i),TpW2,tpe2
          enddo
          close(unit=27)
	      filename=casename(1:lcasename)//'_'//moduname(code_max)(1:longcar(moduname(code_max)))//'.frad_ini'
          open(unit=27,file=filename)
          write(27,150) '  Set furnace fluxes and radiatif power '
          write(27,150) ' Iter  FNORD    FSUD    FWEST    FEST'
          do i=1,nx
            write(27,100) i,fradpn(i),fradps(i),fradpw,fradpe
          enddo
          close(unit=27)
	      filename=casename(1:lcasename)//'_'//moduname(code_max)(1:longcar(moduname(code_max)))//'.fconv_ini'
          open(unit=27,file=filename)
          write(27,150) '  Furnace walls convective fluxes '
          write(27,150) ' Iter  FNORD    FSUD    FWEST    FEST'
          do i=1,nx
            write(27,100) i,fccpN(i),fccps(i),fccpW,fccpE
          enddo
          close(unit=27)
100       format(i4,15(1x,f10.2))
150       format(1x,a)
        endif

271   format(i4,2x,f8.1,12(2x,f8.1))

!
! Calcul de l ecart sur les temperatures paroi et gaz:
!-----------------------------------------------------

        CALL residu_comb(err,ierrmax)
        errrel=err/Tgi1(ierrmax)

!cth Calcul de l erreur pour test convergence temperature nord
        err=-1.0
        do i=1,NX
          diff=abs(Tpn1(i)-Tpn2(i))
          if (diff.gt.err) then
            ierrmax=i
            err=diff
          endif
        enddo
        errrel=err/Tpn1(ierrmax)

	TpN1=TpN1*(1.0-relaxparoi)+TpN2*relaxparoi
	TpW1=TpW1*(1.0-relaxparoi)+TpW2*relaxparoi
	TpE1=TpE1*(1.0-relaxparoi)+TpE2*relaxparoi

        if (itergaz.eq.1) then
          erriter1=errrel
          errorconv=min(errorgaz*10.,1.0e-3)
        endif

          itermax = min(itergazmax,10)

!cth
	IF (code_calcul.EQ.CODE_FOUR_ONLY) THEN
          itermax = 5
        ENDIF
!cth

        IF (itergaz.ge.itermax) THEN
            if (errrel<errorgaz) TestTwoDRad=.true.
!cth	  WRITE(*,1017) itergaz,errrel,errorgaz
	    GO TO 2000 
        ELSE IF (errrel<errorgaz.and.itergaz.ge.itergazmin) then
            TestTwoDRad=.true.
!cth	  WRITE(*,1017) itergaz,errrel,errorgaz
            GO TO 2000
!cth Arreter iteration si l erreur a diminuee d un facteur ratioconv
!cth Lorsque le calcul est couple.
!cth Car inutile d essayer de converger tres finement
!cth puisque le couplage va de toute facon bouleverser
!cth les C.L.
!cth Si le couplage se stabilise alors de toute facon on doit converger
!cth en dessous de errorgaz
        ELSE IF (code_calcul.ne.CODE_FOUR_ONLY.and.errrel < errorconv.and.itercoup.gt.5.and.itergaz.ge.itergazmin) then
!cth	  WRITE(*,1017) itergaz,errrel,errorgaz
	    GO TO 2000 
        ELSE IF (itergaz.ge.itermax) THEN
            if (errrel<errorgaz) TestTwoDRad=.true.
!cth          WRITE(*,1017) itergaz,errrel,errorgaz
            GO TO 2000
	ENDIF

	PRINT 1017, itergaz,errrel,errorgaz

        itergaz=itergaz+1
        indglob = indglob + 1
        indglobt = indglob0 + indglob

          goto 1001

!---------------------------------------------------------------------------
!
!	FIN ITERATIONS SUR TEMPERATURE NORD
!
!---------------------------------------------------------------------------


2000	<USER>

        <GROUP> (TestTwoDRad) THEN
	  PRINT 1018, itergaz,errrel,errorgaz
        ELSE
	  PRINT 1017, itergaz,errrel,errorgaz
        ENDIF

        PRINT 1015
        PRINT *,' END INITIALISATION ',itergaz,itergazmin,itermax
        PRINT 1015

        RETURN

1015    FORMAT(1x,'----------------------------------------')
1016    FORMAT(1x,'  TEMPERATURE AND FLUX INITIALIZATION   ')
!cth1017 format('        COMBUSTION CHAMBER Iteration #',i5,' / cvg state =',e9.3,' >',e9.3 ' (required)')
1017    FORMAT(1x,'COMBUSTION CHAMBER Iteration #',i4,' / cvg state =',e9.3,' > ',e9.3,'(required)')
1018    FORMAT(1x,'COMBUSTION CHAMBER Iteration #',i4,' / cvg state =',e9.3,' < ',e9.3,'(required)')

!
! Fin de la subroutine
!---------------------
	END
