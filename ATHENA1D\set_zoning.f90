    	 subroutine set_zoning(&
     &     lxmax,longfour,nlongzone,nbzones,nposbrul,&
     &     maxzone,longzone,poszone,posdebzone,posfinzone,posbrul,&
     &     zonebrul,brulzone)
!****************************************************************************
!
!	Calcul de toute les infos sur zone et bruleurs
!		
!****************************************************************************
 
      IMPLICIT NONE
!
! Declaration des variables
!--------------------------
      REAL :: lxmax, longfour
      INTEGER :: nlongzone,nbzones,nposbrul,maxzone
      REAL, DIMENSION(maxzone) :: longzone,posdebzone,posfinzone,posbrul
      REAL, DIMENSION(maxzone) :: poszone
      INTEGER, DIMENSION(maxzone) :: zonebrul,brulzone


      INTEGER :: i,ibrul
      REAL :: sum
      REAL :: pos1,long1,pos2,long2
      REAL :: longtot
      INTEGER :: nzplus1,nzplus2

!cth Impression
      INTEGER :: ires(3)
      COMMON/impr/ires

!
! Debut du programme
!-------------------

!cth Generation des zones si uniquement posbrul
      if (nlongzone.eq.0.and.nposbrul.eq.0) then
        print *,' READ_GEO_COMB : NI ZONE NI POSITION BRULEUR ?? '
        STOP ' MIEUX VAUT S ARRETER '
      else if (nlongzone.eq.0) then
        longtot=0.0
        long1=posbrul(2)-posbrul(1)
        pos1=posbrul(1)-0.5*long1
        ibrul=1
        if (pos1.le.0.0) then
          pos1=0.0
          nzplus1=0
          nlongzone=nlongzone+1
          longzone(nlongzone)=0.5*(posbrul(2)+posbrul(1))
          longtot=longtot+longzone(nlongzone)
          brulzone(ibrul)=nlongzone
          zonebrul(nlongzone)=ibrul
        else
          nzplus1=1
          nlongzone=nlongzone+1
          longzone(nlongzone)=pos1
          longtot=longtot+longzone(nlongzone)
          zonebrul(nlongzone)=0
          nlongzone=nlongzone+1
          longzone(nlongzone)=long1
          longtot=longtot+longzone(nlongzone)
          brulzone(ibrul)=nlongzone
          zonebrul(nlongzone)=ibrul
        endif
        do ibrul=2,nposbrul-1
          nlongzone=nlongzone+1
          longzone(nlongzone)=0.5*(posbrul(ibrul)-posbrul(ibrul-1))+0.5*(posbrul(ibrul+1)-posbrul(ibrul))
          longtot=longtot+longzone(nlongzone)
          brulzone(ibrul)=nlongzone
          zonebrul(nlongzone)=ibrul
        enddo
        long2=posbrul(nposbrul)-posbrul(nposbrul-1)
        pos2=posbrul(nposbrul)+0.5*long2
        if (pos2.ge.longfour) then
          pos2=longfour
          nzplus2=0
          nlongzone=nlongzone+1
          longzone(nlongzone)=longfour-longtot
          brulzone(nposbrul)=nlongzone
          zonebrul(nlongzone)=nposbrul
        else
          nzplus2=1
          nlongzone=nlongzone+1
          longzone(nlongzone)=long2
          longtot=longtot+longzone(nlongzone)
          brulzone(nposbrul)=nlongzone
          zonebrul(nlongzone)=nposbrul
          nlongzone=nlongzone+1
          longzone(nlongzone)=longfour-longtot
          zonebrul(nlongzone)=0
        endif
      endif
      nbzones=nlongzone
      if (nlongzone.ne.0) then
        sum=0.0
        poszone(1)=0.0
        do i=1,nlongzone
          sum=sum+longzone(i)
          poszone(i+1)=sum
        enddo
        poszone(nlongzone+1)=lxmax
        sum=0.0
        do i=1,nlongzone-1
          posdebzone(i)=sum
          sum=sum+longzone(i)
          posfinzone(i)=sum
        enddo
        posdebzone(nlongzone)=posfinzone(nlongzone-1)
        longzone(nlongzone)=longfour-sum
        posfinzone(nlongzone)=longfour
      endif

      if (ires(1).ge.1) then
        print *,' CALCUL_ZONE : ',nlongzone,nposbrul,longfour
        do i=1,nlongzone
         if (zonebrul(i).eq.0) then
          print *,i,longzone(i),posfinzone(i),zonebrul(i),0,0.0
         else
          print *,i,longzone(i),posfinzone(i),zonebrul(i),brulzone(zonebrul(i)),posbrul(zonebrul(i))
         endif
        enddo
      endif

      return

      END
