!cth        subroutine write_example_comb(nx,x,tgi1,tpn1,tps1,dr,iecrini,iecrprof)
        subroutine write_example_comb
!        
!*****************************************************************
!  Ecriture des fichiers .ini et .rec comme exemple
!*****************************************************************
!

        USE modshared
        USE modTwoDRad

	IMPLICIT NONE
!
! Declaration des variables
!--------------------------
!cth        INTEGER :: nx
!cth        REAL, dimension(nx) :: x,tgi1,tpn1,tps1
!cth        REAL, dimension(0:nx+1) :: dr
!cth        INTEGER :: i<PERSON>rini,iecrprof

        CHARACTER*130 filename
        INTEGER :: i
        INTEGER :: longcar

        REAL :: valrec

! Ecriture temperature initiales .ini
!-------------------------------------------------------

      if (iecrini.ne.0) then
!cth 	filename=casename(1:lcasename)//'_'//moduname(CODE_FOUR)(1:longcar(moduname(CODE_FOUR)))//'.ex_ini'
 	filename=namefour(1)(1:lnamefour(1))//'.ex_ini'
        open(unit=27,file=filename,err=9997)
        write(27,100,err=9998) nx,' ! Number of values'
100     format(1x,i5,2x,a)
        write(27,150,err=9998) ' Iter  Tgaz    Tnord'
150     format(1x,a)
        do i=1,NX
          write(27,200,err=9998) x(i),tgi1(i)-273.15,tpn1(i)-273.15
        enddo
        close(unit=27)
200     format(15(1x,f9.4))
      endif

!
! Ecriture debits .rec
!-------------------------------------------------------

      if (iecrini.ne.0) then
!cth 	filename=casename(1:lcasename)//'_'//moduname(CODE_FOUR)(1:longcar(moduname(CODE_FOUR)))//'.ex_rec'
 	filename=namefour(1)(1:lnamefour(1))//'.ex_rec'
        open(unit=27,file=filename,err=9997)
        write(27,100,err=9998) nx,' ! Number of values'
        write(27,150,err=9998) ' Iter  Debit'
        do i=1,NX
          valrec=0.5*(dr(i)+dr(i+1))
          write(27,200,err=9998) x(i),valrec
        enddo
        close(unit=27)
      endif

!
! Ecriture temperature de paroi .ldt
!-------------------------------------------------------

      if (iecrprof.ne.0) then
!cth	filename=casename(1:lcasename)//'_'//moduname(CODE_FOUR)(1:longcar(moduname(CODE_FOUR)))//'.ex_ldt'
 	filename=namefour(1)(1:lnamefour(1))//'.ex_ldt'
        open(unit=27,file=filename,err=9997)
        write(27,100,err=9998) nx,' ! Number of values'
        write(27,150,err=9998) ' Iter  Temp'
        do i=1,NX
          write(27,200,err=9998) x(i),tps1(i)-273.15
        enddo
        close(unit=27)
      endif

!
! Ecriture flux paroi charge .ldf
!-------------------------------------------------------

      if (iecrprof.ne.0) then
!cth	filename=casename(1:lcasename)//'_'//moduname(CODE_FOUR)(1:longcar(moduname(CODE_FOUR)))//'.ex_ldf'
 	filename=namefour(1)(1:lnamefour(1))//'.ex_ldf'
        OPEN(unit=27,file=filename,err=9997)
        write(27,100,err=9998) nx,' ! Number of values'
        write(27,150,err=9998) ' Iter  Flux'
        do i=1,NX
          write(27,300,err=9998) i,ftotps(i)
        enddo
300     format(1x,i5,10(1x,f11.4))
        CLOSE(unit=27)
      endif

        return

9997  CONTINUE
!cth Erreur ouverture fichier
      PRINT *,' WRITE_EXAMPLE_COMB : '
      PRINT *,' OPEN ERROR FILE : ',filename
      STOP ' MIEUX VAUT S ARRETER '

9998  CONTINUE
!cth Erreur ecriture fichier
      PRINT *,' WRITE_EXAMPLE_COMB : '
      PRINT *,' WRITE ERROR FILE : ',filename
      STOP ' MIEUX VAUT S ARRETER '

!
! Fin du programme 
!-----------------
        end
