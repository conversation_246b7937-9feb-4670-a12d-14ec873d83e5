# SMR1D CMakeLists.txt
# SMR module executable

# Find all source files
file(GLOB SMR_SOURCES "*.f90")
file(GLOB SMR_F77_SOURCES "*.f")

# Main executable sources
set(SMR_MAIN_SOURCES
    main_smr.f90
    modules_smr.f90
    # Old files
    bindif.f
    dffs.f
    dfopr.f
    etacol.f
    heatef.f
    iniparp.f
    jcobi.f
    loco.f
    molfr.f
    molfr1.f
    outputip.f
    outputsu.f
    parameter.f
    radau.f
    reactor.f
    result.f
    rgkt.f
    rightm.f
    speed.f
    smr_gent_tubes.f
    thercon.f
    twinve.f
    visco.f
    vites.f
    # New files
    bilans_smr.f90
    deteta_tubes.f90
    detflux_tubes.f90
    detlam_tubes.f90
    det_temp_2d.f90
    init_data_al.f90
    init_tubes.f90
    init_geo_tubes.f90
    init_gent_tubes.f90
    init_cst_keq.f90
    init_props_tubes.f90
    compute_keq_p.f90
    compute_keq_t.f90
    approche_reac.f90
    mesh_settings_tubes.f90
    read_par_tubes.f90
    read_geo_tubes.f90
    read_phy_tubes.f90
    read_bc_tubes.f90
    read_flux_tubes.f90
    integ_flux_tubes.f90
    repart_temp_tubes.f90
    residu_tubes.f90
    solve_tubes.f90
    write_data_tubes.f90
    write_example_tubes.f90
    write_restart_tubes.f90
    test_props.f90
    put_tagcvs_tubes.f90
    init_graph_smr.f90
    draw_graph_smr.f90
    write_bal_tubes.f90
)

# Test executable sources
set(SMR_TEST_SOURCES
    main_test_props.f90
    modules_smr.f90
    # Include all other sources for test
    ${SMR_MAIN_SOURCES}
)

# Create main executable
add_executable(smr1d ${SMR_MAIN_SOURCES})

# Create test executable
add_executable(smrprops ${SMR_TEST_SOURCES})

# Set module directory
set_target_properties(smr1d smrprops PROPERTIES
    Fortran_MODULE_DIRECTORY ${CMAKE_Fortran_MODULE_DIRECTORY}
)

# Include directories
target_include_directories(smr1d PRIVATE
    ${CMAKE_Fortran_MODULE_DIRECTORY}
    ${CMAKE_SOURCE_DIR}/GRAPH
)

target_include_directories(smrprops PRIVATE
    ${CMAKE_Fortran_MODULE_DIRECTORY}
    ${CMAKE_SOURCE_DIR}/GRAPH
)

# Compiler definitions
target_compile_definitions(smr1d PRIVATE
    SMRCHARGE
    $<$<BOOL:${WIN32}>:WIN32>
    $<$<BOOL:${WIN32}>:_WINDOWS>
)

target_compile_definitions(smrprops PRIVATE
    SMRCHARGE
    $<$<BOOL:${WIN32}>:WIN32>
    $<$<BOOL:${WIN32}>:_WINDOWS>
)

# Link dependencies
target_link_libraries(smr1d
    athena1d
    stdprops
    usbkey
    modules
)

target_link_libraries(smrprops
    athena1d
    stdprops
    usbkey
    modules
)

# Fortran compiler flags for fixed format files
if(WIN32)
    set_source_files_properties(${SMR_F77_SOURCES} PROPERTIES
        Fortran_FORMAT FIXED
        COMPILE_FLAGS "/extend_source:132"
    )
else()
    set_source_files_properties(${SMR_F77_SOURCES} PROPERTIES
        Fortran_FORMAT FIXED
        COMPILE_FLAGS "-FI -extend-source"
    )
endif()

message(STATUS "SMR1D: Main sources: ${SMR_MAIN_SOURCES}")
message(STATUS "SMR1D: F77 sources: ${SMR_F77_SOURCES}") 