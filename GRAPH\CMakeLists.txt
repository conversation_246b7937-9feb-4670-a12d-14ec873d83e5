# GRAPH CMakeLists.txt
# Graphics module library

# Find all source files
file(GLOB GRAPH_SOURCES "*.f90")
file(GLOB GRAPH_F77_SOURCES "*.f")

# Create static library
add_library(graph STATIC ${GRAPH_SOURCES} ${GRAPH_F77_SOURCES})

# Set module directory
set_target_properties(graph PROPERTIES
    Fortran_MODULE_DIRECTORY ${CMAKE_Fortran_MODULE_DIRECTORY}
)

# Include directories
target_include_directories(graph PUBLIC
    ${CMAKE_Fortran_MODULE_DIRECTORY}
)

# Compiler definitions
target_compile_definitions(graph PRIVATE
    $<$<BOOL:${WIN32}>:WIN32>
    $<$<BOOL:${WIN32}>:_WINDOWS>
)

# Link dependencies
target_link_libraries(graph modules)

# Windows-specific libraries for graphics
if(WIN32)
    target_link_libraries(graph
        user32
        gdi32
        winspool
        comdlg32
        advapi32
        shell32
        ole32
        oleaut32
        uuid
        odbc32
        odbccp32
    )
endif()

# Fortran compiler flags for fixed format files
if(WIN32)
    set_source_files_properties(${GRAPH_F77_SOURCES} PROPERTIES
        Fortran_FORMAT FIXED
        COMPILE_FLAGS "/extend_source:132"
    )
else()
    set_source_files_properties(${GRAPH_F77_SOURCES} PROPERTIES
        Fortran_FORMAT FIXED
        COMPILE_FLAGS "-FI -extend-source"
    )
endif()

message(STATUS "GRAPH: Sources: ${GRAPH_SOURCES}")
message(STATUS "GRAPH: F77 sources: ${GRAPH_F77_SOURCES}") 