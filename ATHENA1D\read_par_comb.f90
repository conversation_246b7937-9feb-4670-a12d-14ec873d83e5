    	 SUBROUTINE read_par_comb(basename,lbasename,&
     &     nbtsi,icor_ray,errorgaz,itergazmin,itergazmax,&
     &     redem,modtp,modrad,CONTROLMAILLRAYON,nbrayon4,ang1,raison,nggcod,&
     &     errorrad,relaxgaz,relaxent,errorent,&
     &     modcvref,modcvbrul,modcvload,corcvref,corcvbrul,corcvload,&
     &     hcvref,hcvbrul,hcvload,&
     &     relaxgdon,relaxedon,&
     &     ratioconv,nitergmax,relaxgmin,niteremax,relaxemin,&
     &     relaxpdon,relaxpmin,niterpmax,&
     &     relaxrfdon,relaxrfmin,niterrfmax,&
     &     relaxrpdon,relaxrpmin,niterrpmax,&
     &     modresol,nbresol,reacbrul,reacfour,linptr,&
     &     qremail,autoremail,autocorrec,nxremail,nyremail,dxremail,dyremail,&
     &     criterdist,nitermail,ioptimail,&
     &     crecirc,valrecirc,&
     &     ctempini,tginideb,tginifin,tpninideb,tpninifin,tpwini,tpeini,&
     &     i<PERSON>rini,iecrprof,iecrray,iecrflu,iecrcoef)
!****************************************************************************
!
!	Lecture du fichier .par d un four
!	Seuls arguments d entree basename et lbasename pour nom de fichier
!		
!****************************************************************************

         USE modshared
 
    	 IMPLICIT NONE
!
! Declaration des variables
!--------------------------
         CHARACTER*(*) basename
         INTEGER :: lbasename
    	 INTEGER :: redem
  	 INTEGER :: nbtsi, nbrayon4, nggcod
         INTEGER :: icor_ray, controlmaillrayon	 
         INTEGER :: modrad
         INTEGER :: itergazmin,itergazmax,modtp
   	 REAL :: relaxgaz
         REAL :: relaxent, errorgaz
         REAL :: errorent, errorrad
	 INTEGER :: modcvref, modcvload, modcvbrul
         REAL :: corcvref, corcvload, corcvbrul
         REAL :: hcvref, hcvload, hcvbrul
         REAL :: reacbrul, reacfour
         REAL :: raison, ang1
         REAL :: ratioconv ! cth pour test convergence
         INTEGER :: nitergmax,niteremax
         REAL :: relaxgdon,relaxedon ! cth pour test convergence
         REAL :: relaxgmin,relaxemin ! cth pour test convergence
         REAL :: relaxpdon,relaxpmin
         REAL :: relaxrfdon,relaxrfmin
         REAL :: relaxrpdon,relaxrpmin
         INTEGER :: niterpmax,niterrfmax,niterrpmax
         INTEGER :: modresol
         INTEGER :: nbresol
         INTEGER :: linptr

         LOGICAL :: qremail
         INTEGER :: autoremail,autocorrec,nxremail,nyremail
         REAL :: dxremail,dyremail
         real :: criterdist
         integer :: nitermail
         integer :: ioptimail
         CHARACTER*1 crecirc
         REAL :: valrecirc
!cth         REAL :: tgadmimin,tgadmimax,tpadmimin,tpadmimax
         CHARACTER*1 ctempini
         REAL :: tginideb,tginifin
         REAL :: tpninideb,tpninifin
         REAL :: tpwini,tpeini
         INTEGER :: iecrini,iecrprof,iecrray,iecrflu,iecrcoef


         INTEGER, PARAMETER :: CODEEXPERT=1234567890
         INTEGER :: expertcode
         INTEGER :: longcar
	 CHARACTER*8 cvers
         CHARACTER*130 filename
         INTEGER :: lname
         CHARACTER*130 line
         INTEGER :: lline
         INTEGER :: iline
         CHARACTER*130 cparam
         INTEGER :: lcparam
         INTEGER :: icparam
         CHARACTER*1 cc
         INTEGER :: nreserve
    	 INTEGER :: numline
    	 INTEGER :: nunit

!cth Pour coeff. convection impose constant
         REAL :: convref,convbrul,convload

!cth Temperatures admissibles
        include 'temp_admis.cmn'

!cth Niveau impression
        INTEGER ires(3)
        COMMON/impr/ires

!
! Debut du programme
!-------------------

      cvers=' '

      numline=0
      nunit=99

      filename=' '
      filename=basename(1:lbasename)//'.ipar'
      lname=longcar(filename)

      OPEN(unit=nunit,file=filename,STATUS='OLD',ERR=9997)

      numline=numline+1
      READ(nunit,170,ERR=9998,END=9999) cvers
170   FORMAT(2x,a8)
100      FORMAT(a)
      if (cvers.ne.cversion) then
        print *,' READ_PAR_COMB : FILE : VERSION ERROR'
        print *,' FILE NAME : ',filename(1:lname)
        print *,' VERSION : EXPECTED : ',cversion
        print *,' VERSION : GOT      : ',cvers
        stop ' CORRECT THIS, AND TRY AGAIN '
      endif

!cth !ere ligne commentaire

         numline=numline+1
         READ(nunit,100,ERR=9998,END=9999) line
         numline=numline+1
         READ(nunit,100,ERR=9998,END=9999) line
 
!gm CALCULATION OPTIONS
         if (redem.eq.0) then
           numline=numline+1
           READ(nunit,*,ERR=9998,END=9999) redem
         else
           numline=numline+1
    	   READ(nunit,100,ERR=9998,END=9999) line
           lline=longcar(line)
         endif
!cth Ecriture fichiers
         numline=numline+1
    	 READ(nunit,*,ERR=9998,END=9999) iecrini
         numline=numline+1
    	 READ(nunit,*,ERR=9998,END=9999) iecrprof
         nreserve=2
         do iline=1,nreserve
           numline=numline+1
    	   READ(nunit,100,ERR=9998,END=9999) line
           lline=longcar(line)
         enddo

         numline=numline+1
         READ(nunit,100,ERR=9998,END=9999) line
         numline=numline+1
         READ(nunit,100,ERR=9998,END=9999) line
 
!gm MESH PARAMETERS
!cth Info pour remaillage et correction automatique
         numline=numline+1
         READ(nunit,*,ERR=9998,END=9999) autoremail
         numline=numline+1
	 READ(nunit,*,ERR=9998,END=9999) nxremail
         numline=numline+1
         READ(nunit,*,ERR=9998,END=9999) dxremail
         numline=numline+1
         READ(nunit,*,ERR=9998,END=9999) autocorrec
         nreserve=2
         do iline=1,nreserve
           numline=numline+1
    	   READ(nunit,100,ERR=9998,END=9999) line
           lline=longcar(line)
         enddo
         numline=numline+1
         READ(nunit,*,ERR=9998,END=9999) nyremail
         numline=numline+1
         READ(nunit,*,ERR=9998,END=9999) dyremail
         nreserve=2
         do iline=1,nreserve
           numline=numline+1
    	   READ(nunit,100,ERR=9998,END=9999) line
           lline=longcar(line)
         enddo
!cth Info maillage angulaire pour rayonnement
         numline=numline+1
    	 READ(nunit,*,ERR=9998,END=9999) NBRAYON4
         numline=numline+1
    	 READ(nunit,*,ERR=9998,END=9999) NBTSI
         numline=numline+1
    	 READ(nunit,*,ERR=9998,END=9999) CONTROLMAILLRAYON
         numline=numline+1
   	 READ(nunit,*,ERR=9998,END=9999) ANG1
         numline=numline+1
    	 READ(nunit,*,ERR=9998,END=9999) RAISON
         nreserve=2
         do iline=1,nreserve
           numline=numline+1
    	   READ(nunit,100,ERR=9998,END=9999) line
           lline=longcar(line)
         enddo

         numline=numline+1
         READ(nunit,100,ERR=9998,END=9999) line
         numline=numline+1
         READ(nunit,100,ERR=9998,END=9999) line
 
!gm PHYSICAL MODELS AND NUMERICAL SOLUTION
!cth Parametre pour rayonnement
         numline=numline+1
         READ(nunit,*,ERR=9998,END=9999) modrad
         modrad=2   !gm Seul DTRM-rayonsgg3 est disponible pour le moment
         numline=numline+1
   	 READ(nunit,*,ERR=9998,END=9999) NGGCOD
         numline=numline+1
    	 READ(nunit,*,ERR=9998,END=9999) icor_ray
         nreserve=4
         do iline=1,nreserve
           numline=numline+1
    	   READ(nunit,100,ERR=9998,END=9999) line
           lline=longcar(line)
         enddo
!gm  Parametre pour le transfert aux parois
         numline=numline+1
    	 READ(nunit,*,ERR=9998,END=9999) modtp
         numline=numline+1
    	 READ(nunit,*,ERR=9998,END=9999) modcvref 
         numline=numline+1
!cth    	 READ(nunit,*,ERR=9998,END=9999) corcvref
    	 READ(nunit,*,ERR=9998,END=9999) convref
         numline=numline+1
    	 READ(nunit,*,ERR=9998,END=9999) modcvload
         numline=numline+1
!cth    	 READ(nunit,*,ERR=9998,END=9999) corcvload
    	 READ(nunit,*,ERR=9998,END=9999) convload
         numline=numline+1
    	 READ(nunit,*,ERR=9998,END=9999) modcvbrul
         numline=numline+1
!cth    	 READ(nunit,*,ERR=9998,END=9999) corcvbrul
    	 READ(nunit,*,ERR=9998,END=9999) convbrul
         if (modcvref.eq.0) then
    	   hcvref=convref
           corcvref=1.0
         else
           hcvref=1.0e+30
           corcvref=convref
         endif
         if (modcvload.eq.0) then
    	   hcvload=convload
           corcvload=1.0
         else
           hcvload=1.0e+30
           corcvload=convload
         endif
         if (modcvbrul.eq.0) then
    	   hcvbrul=convbrul
           corcvbrul=1.0
         else
           hcvbrul=1.0e+30
           corcvbrul=convbrul
         endif
         nreserve=1
         do iline=1,nreserve
           numline=numline+1
    	   READ(nunit,100,ERR=9998,END=9999) line
           lline=longcar(line)
         enddo
!cth Parametre de recirculation du gaz
         numline=numline+1
    	 READ(nunit,100,ERR=9998,END=9999) line
         lline=longcar(line)
         CALL CLEANLINE(line,cparam,lcparam)
         if (lcparam.eq.0) then
           print *,' READ_PAR_COMB : PAS D INFO SUR RECIRCULATION '
           print *,' Line : ',line(1:lline)
           print *,' Longueur : ',lline,lcparam
           cparam(1:1)='N'
           lcparam=1
           print *,' Correction : ',cparam(1:lcparam)
         else if (lcparam.ne.1) then
           print *,' READ_PAR_COMB : INFO SUR RECIRCULATION ??'
           print *,' Line : ',line(1:lline)
           print *,' Longueur : ',lline,lcparam
           cc=cparam(1:1)
           lcparam=1
           icparam=index('NUF',cc)
           if (icparam.eq.0) then
             cparam(1:1)='N'
             lcparam=1
             print *,' Correction : ',cparam(1:lcparam)
           endif
         endif
         crecirc=cparam(1:1)
         numline=numline+1
    	 READ(nunit,*,ERR=9998,END=9999) valrecirc
         if (crecirc(1:1).eq.'N') valrecirc=0.0
         nreserve=2
         do iline=1,nreserve
           numline=numline+1
    	   READ(nunit,100,ERR=9998,END=9999) line
           lline=longcar(line)
         enddo

!cth Critere convergence et sous-relaxation
         numline=numline+1
    	 READ(nunit,*,ERR=9998,END=9999) relaxgaz
         numline=numline+1
    	 READ(nunit,*,ERR=9998,END=9999) relaxent
         numline=numline+1
    	 READ(nunit,*,ERR=9998,END=9999) errorgaz
!cth Temperature admissibles
         numline=numline+1
    	 READ(nunit,*,ERR=9998,END=9999) tpadmimin,tpadmimax
         numline=numline+1
    	 READ(nunit,*,ERR=9998,END=9999) tgadmimin,tgadmimax

         nreserve=2
         do iline=1,nreserve
           numline=numline+1
    	   READ(nunit,100,ERR=9998,END=9999) line
           lline=longcar(line)
         enddo

         numline=numline+1
         READ(nunit,100,ERR=9998,END=9999) line
         numline=numline+1
         READ(nunit,100,ERR=9998,END=9999) line
 
!gm INITIALIZATION
!cth Temperatures initiales
         numline=numline+1
    	 READ(nunit,100,ERR=9998,END=9999) line
         lline=longcar(line)
         CALL CLEANLINE(line,cparam,lcparam)
         if (lcparam.eq.0) then
           print *,' READ_PAR_COMB : PAS D INFO SUR TEMP. INITIALE '
           print *,' Line : ',line(1:lline)
           print *,' Longueur : ',lline,lcparam
           cparam(1:1)='U'
           lcparam=1
           print *,' Correction : ',cparam(1:lcparam)
         else if (lcparam.ne.1) then
           print *,' READ_PAR_COMB : INFO SUR TEMP. INITIALE ??'
           print *,' Line : ',line(1:lline)
           print *,' Longueur : ',lline,lcparam
           cc=cparam(1:1)
           lcparam=1
           icparam=index('ULF',cc)
           if (icparam.eq.0) then
             cparam(1:1)='N'
             print *,' Correction : ',cparam(1:lcparam)
           endif
         endif
         ctempini(1:1)=cparam(1:1)
         numline=numline+1
    	 READ(nunit,*,ERR=9998,END=9999) tginideb,tginifin
         numline=numline+1
    	 READ(nunit,*,ERR=9998,END=9999) tpninideb,tpninifin
         numline=numline+1
    	 READ(nunit,*,ERR=9998,END=9999) tpwini
         numline=numline+1
    	 READ(nunit,*,ERR=9998,END=9999) tpeini

!cth Passage des temperatures en Kelvin
         tpadmimin=tpadmimin+273.15
         tpadmimax=tpadmimax+273.15
         tgadmimin=tgadmimin+273.15
         tgadmimax=tgadmimax+273.15
!cth         tpadmimin=max(tpadmimin,300.0)
!cth         tpadmimax=min(tpadmimax,1800.0)
!cth         tpadmimax=max(tpadmimax,300.0)
!cth         tgadmimin=max(tgadmimin,300.0)
!cth         tgadmimax=min(tgadmimax,3000.0)
!cth         tgadmimax=max(tgadmimax,300.0)
         tpadmimin=max(tpadmimin,tpdefmin)
         tpadmimax=min(tpadmimax,tpdefmax)
         tpadmimax=max(tpadmimax,tpdefmin)
         tgadmimin=max(tgadmimin,tgdefmin)
         tgadmimax=min(tgadmimax,tgdefmax)
         tgadmimax=max(tgadmimax,tgdefmin)
!
         tginideb=tginideb+273.15
         tginifin=tginifin+273.15
         tpninideb=tpninideb+273.15
         tpninifin=tpninifin+273.15
         tpwini=tpwini+273.15
         tpeini=tpeini+273.15

         if (.not.qremail) qremail=(autoremail.eq.1)

!cth Init et valeur par defaut
!cth Valeur par defaut modifiable par CODE EXPERT
!cth CHANGEMENT DE VALEUR PAR DEFAUT POUR SMR
!cth POUR LE MOMENT ON DISSOCIE EN FONCTION DE LA CHARGE
!cth DANS L AVENIR TROUVER DES VALEURS COMMUNES GARANTIES

         ires(1)=0
         ires(2)=0
         ires(3)=0
         iecrray=0
         iecrflu=0
         iecrcoef=0
         criterdist=0.15
         nitermail=10
         ioptimail=1
         if (modtp.le.0) modtp=2
         modtp=min(modtp,2)
         modtp=max(modtp,1)
         modresol=2
         nbresol=10
         linptr=1
         REACBRUL=1.0
         REACFOUR=1.0
         itergazmin=3
         itergazmax=10
!cth
!cth errorent est une erreur relative sur temperature
!cth surtout pour Newton-Raphson
         errorent=1.0e-3
         errorrad=1.0e-6
         ratioconv=0.2
         nitergmax=1
         relaxgdon=relaxgaz
         relaxgmin=relaxgaz
         niterpmax=1
         relaxpdon=relaxgaz
         relaxpmin=relaxgaz
         niteremax=1
         relaxedon=relaxent
         relaxemin=relaxent
         niterrfmax=1
         relaxrfdon=1.0
         relaxrfmin=1.0
         niterrpmax=1
         relaxrpdon=1.0
         relaxrpmin=1.0

         IF (CODE_CHARGE.EQ.CODE_SMR) THEN
           itergazmin=5
           itergazmax=10
           errorent=1.0e-2
           niteremax=1
           relaxemin=0.7
           relaxedon=0.7
           nitergmax=15
           relaxgmin=0.3
           relaxgdon=0.5
           niterpmax=15
           relaxpmin=0.4
           relaxpdon=0.7
           niterrfmax=15
           relaxrfmin=0.6
           relaxrfdon=0.7
           niterrpmax=1
           relaxrpmin=0.6
           relaxrpdon=0.6
         ENDIF

         if (code_calcul.eq.CODE_FOUR_ONLY) itergazmax=200

!cth ATTENTION POUR RAISON D EFFICACITE 
!cth ON CONSERVE POUR LE MOMENT LES ANCIENNES VARIABLES

!cth Ligne blanche de separation
         numline=numline+1
         READ(nunit,100,ERR=9998,END=1000) line
 
!cth Options expert
         numline=numline+1
        READ(nunit,*,ERR=9998,END=1000) expertcode

        IF (expertcode.eq.CODEEXPERT) then
         print *,' FURNACE : EXPERT MODE '

         numline=numline+1
         READ(nunit,100,ERR=9998,END=1000) line
 
!cth Options expert
!gm VERBOSE OPTIONS
         numline=numline+1
    	 READ(nunit,*,ERR=9998,END=1000) ires(1)
         numline=numline+1
    	 READ(nunit,*,ERR=9998,END=1000) ires(2)
         numline=numline+1
    	 READ(nunit,*,ERR=9998,END=1000) ires(3)
         nreserve=2
         do iline=1,nreserve
           numline=numline+1
    	   READ(nunit,100,ERR=9998,END=1000) line
           lline=longcar(line)
         enddo

         numline=numline+1
         READ(nunit,100,ERR=9998,END=1000) line
         numline=numline+1
         READ(nunit,100,ERR=9998,END=1000) line

!gm MESH PARAMETERS
         numline=numline+1
    	 READ(nunit,*,ERR=9998,END=1000) criterdist
         numline=numline+1
    	 READ(nunit,*,ERR=9998,END=1000) nitermail
         numline=numline+1
    	 READ(nunit,*,ERR=9998,END=1000) ioptimail
         nreserve=2
         do iline=1,nreserve
           numline=numline+1
    	   READ(nunit,100,ERR=9998,END=1000) line
           lline=longcar(line)
         enddo

         numline=numline+1
         READ(nunit,100,ERR=9998,END=1000) line
         numline=numline+1
         READ(nunit,100,ERR=9998,END=1000) line

!gm PHYSICAL MODELS AND NUMERICAL SOLUTION
         numline=numline+1
    	 READ(nunit,*,ERR=9998,END=1000) modresol
         numline=numline+1
    	 READ(nunit,*,ERR=9998,END=1000) nbresol
         numline=numline+1
    	 READ(nunit,*,ERR=9998,END=1000) linptr
         nreserve=2
         do iline=1,nreserve
           numline=numline+1
    	   READ(nunit,100,ERR=9998,END=1000) line
           lline=longcar(line)
         enddo
         numline=numline+1
   	 READ(nunit,*,ERR=9998,END=1000) REACBRUL
         numline=numline+1
    	 READ(nunit,*,ERR=9998,END=1000) REACFOUR
         nreserve=2
         do iline=1,nreserve
           numline=numline+1
    	   READ(nunit,100,ERR=9998,END=1000) line
           lline=longcar(line)
         enddo

!gm RELAXATION PARAMETERS
         numline=numline+1
    	 READ(nunit,*,ERR=9998,END=1000) niteremax,relaxemin,relaxedon
         numline=numline+1
    	 READ(nunit,*,ERR=9998,END=1000) nitergmax,relaxgmin,relaxgdon
         numline=numline+1
    	 READ(nunit,*,ERR=9998,END=1000) niterpmax,relaxpmin,relaxpdon
         numline=numline+1
    	 READ(nunit,*,ERR=9998,END=1000) niterrfmax,relaxrfmin,relaxrfdon
         numline=numline+1
    	 READ(nunit,*,ERR=9998,END=1000) niterrpmax,relaxrpmin,relaxrpdon

!gm Min/Max Iter and criteria
         numline=numline+1
    	 READ(nunit,*,ERR=9998,END=1000) itergazmin,itergazmax
         numline=numline+1
  	 READ(nunit,*,ERR=9998,END=1000) errorrad
         numline=numline+1
    	 READ(nunit,*,ERR=9998,END=1000) errorent
         numline=numline+1
    	 READ(nunit,*,ERR=9998,END=1000) ratioconv
         nreserve=2
         do iline=1,nreserve
           numline=numline+1
    	   READ(nunit,100,ERR=9998,END=1000) line
           lline=longcar(line)
         enddo

        ENDIF

!cth Pas code expert
1000    CONTINUE

        CLOSE(UNIT=99)

        IF (expertcode.eq.CODEEXPERT) then
          print *,' '
          print *,' --------------------------- '
          print *,' FURNACE : USING EXPERT MODE '
          print *,' --------------------------- '
          print *,' '
        ENDIF

!cth Verif resolution
        
        nbresol=max(nbresol,5)
        linptr=min(linptr,1)
        linptr=max(linptr,0)

        if (modresol.gt.2) then
          print *,' '
          print *,' MODELE DE RESOLUTION ENTHALPIQUE INCONNU ==> FIXE a 2 '
          print *,' '
          modresol=2
        endif

        RETURN

9997    CONTINUE
!cth Erreur ouverture fichier
        print *,' READ_PAR_COMB : '
        PRINT *,' OPEN ERROR : ',filename(1:lname)
        STOP ' CORRECT THIS, AND TRY AGAIN '

9998    CONTINUE
!cth Erreur lecture
        print *,' READ_PAR_COMB : '
        PRINT *,' READ ERROR : ',filename(1:lname)
        PRINT *,' LINE NUMBER : ',numline
        STOP ' CORRECT THIS, AND TRY AGAIN '

9999    CONTINUE
!cth Fin de fichier
        print *,' READ_PAR_COMB : '
        PRINT *,' END-OF-FILE : ',filename(1:lname)
        PRINT *,' LINE NUMBER : ',numline
        STOP ' CORRECT THIS, AND TRY AGAIN '

        END
