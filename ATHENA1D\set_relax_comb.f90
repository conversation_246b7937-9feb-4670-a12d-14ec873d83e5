      SUBROUTINE set_relax_comb(ITER)

      USE modTwoDRad
      USE modshared

      IMPLICIT NONE

      INTEGER :: ITER

      REAL :: DRELAX

!cth Impression
      INTEGER :: ires(3)
      COMMON/impr/ires

      if (iter.ge.itminrelax.and.iter.lt.nitergmax) then
        drelax=(relaxgdon-relaxgmin)*float(iter-itminrelax)/float(nitergmax-itminrelax)
        relaxgaz=relaxgmin+drelax
        if (ires(1).ge.1) then
          print 100,' Gas evolutive relax. : ',iter,relaxgaz
        endif
      else if (iter.eq.nitergmax) then
        relaxgaz=relaxgdon
        if (ires(1).ge.1) then
          print 100,' Gas final relax. : ',iter,relaxgaz
        endif
      endif
      if (iter.ge.itminrelax.and.iter.lt.niterpmax) then
        drelax=(relaxpdon-relaxpmin)*float(iter-itminrelax)/float(niterpmax-itminrelax)
        relaxparoi=relaxpmin+drelax
        if (ires(1).ge.1) then
          print 100,' Wall temp. evolutive relax. : ',iter,relaxparoi
        endif
      else if (iter.eq.niterpmax) then
        relaxparoi=relaxpdon
        if (ires(1).ge.1) then
          print 100,' Wall temp. final relax. : ',iter,relaxparoi
        endif
      endif
      if (iter.ge.itminrelax.and.iter.lt.niterrfmax) then
        drelax=(relaxrfdon-relaxrfmin)*float(iter-itminrelax)/float(niterrfmax-itminrelax)
        relaxrflux=relaxrfmin+drelax
        if (ires(1).ge.1) then
          print 100,' Wall flux evolutive relax. : ',iter,relaxrflux
        endif
      else if (iter.eq.niterrfmax) then
        relaxrflux=relaxrfdon
        if (ires(1).ge.1) then
          print 100,' Wall flux final relax. : ',iter,relaxrflux
        endif
      endif
      if (iter.ge.itminrelax.and.iter.lt.niterrpmax) then
        drelax=(relaxrpdon-relaxrpmin)*float(iter-itminrelax)/float(niterrpmax-itminrelax)
        relaxrpuis=relaxrpmin+drelax
        if (ires(1).ge.1) then
          print 100,' Power evolutive relax. : ',iter,relaxrpuis
        endif
      else if (iter.eq.niterrpmax) then
        relaxrpuis=relaxrpdon
        if (ires(1).ge.1) then
          print 100,' Power final relax. : ',iter,relaxrpuis
        endif
      endif
      if (iter.lt.niteremax) then
        drelax=(relaxedon-relaxemin)*float(iter)/float(niteremax)
        relaxent=relaxemin+drelax
        if (ires(1).ge.1) then
          print 100,' Enthalpy evolutive relax. : ',iter,relaxent
        endif
      else if (iter.eq.niteremax) then
        relaxent=relaxedon
        if (ires(1).ge.1) then
          print 100,' Enthalpy final relax. : ',iter,relaxent
        endif
      endif
      if (ires(1).ge.1) then
        print 100,' Furnace relax. : ',iter,relaxgaz,relaxparoi,relaxrflux,relaxrpuis,relaxent
      endif



100   FORMAT (A,i5,10f7.3)
      END
