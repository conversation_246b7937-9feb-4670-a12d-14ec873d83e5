      subroutine read_real_real(nunit,nvaleur,rval1,rval2,qerr)

      implicit none

      integer :: nunit
      integer :: nvaleur
      real :: rval1(nvaleur)
      real :: rval2(nvaleur)
      logical :: qerr

      integer :: i

      qerr=.false.

      do i=1,nvaleur
        read(nunit,*,ERR=9998,END=9999) rval1(i),rval2(i)
      enddo

      return

9998  continue
      print *,' READ_REAL_REAL : ERREUR LECTURE '
      print *,' NVALEUR : ',nvaleur
      qerr=.true.
      return

9999  continue
      print *,' READ_REAL_REAL : END OF FILE '
      print *,' NVALEUR : ',nvaleur
      qerr=.true.
      return

      end
