      subroutine read_phy_comb(basename,lbasename,&
     &     ncodmax,ncombesp,noxyesp,&
     &     maxoxydant,nboxydant,maxcaroxy,nomoxydant,lnomoxydant,&
     &     xcombmol,xcombmas,xoxymol,xsoxymol,xoxymas,xsoxymas,&
     &     pcisto,r_o2_fuel,r_co2_fuel,r_h2o_fuel,&
     &     pcivol,volair,volfuel,&
     &     humid,thumid,&
     &     qcompo,qstoechio,qvolume,qcombmas,&
     &     qoxydant,qhumid,qoxymas)
!****************************************************************************
!
!       Lecture du fichier .geo SMR
!
!****************************************************************************

      USE modshared
      USE modespece

      implicit none

!
! Declaration des variables
!--------------------------
      character*(*) basename
      integer :: lbasename
      integer :: ncodmax
      integer :: ncombesp,noxyesp
      real :: xcombmol(ncodesp),xcombmas(ncodesp)
      integer :: maxoxydant,nboxydant,maxcaroxy
      character*(maxcaroxy) :: nomoxydant(maxoxydant)
      integer :: lnomoxydant(maxoxydant)
      real :: xoxymol(ncodesp,maxoxydant),xsoxymol(ncodesp,maxoxydant)
      real :: xoxymas(ncodesp,maxoxydant),xsoxymas(ncodesp,maxoxydant)
      real :: pcisto,r_o2_fuel,r_co2_fuel,r_h2o_fuel
      real :: pcivol,volair,volfuel
      real :: humid,thumid
      logical :: qcompo,qstoechio,qvolume,qcombmas
      logical :: qoxydant,qoxymas,qhumid

      character*8 cvers
      character*130 filename
      integer :: lname
      integer :: nunit
      character*130 cline
      integer :: lline
      character*20 code
      integer :: lcode
      integer :: nvaleur
      logical :: qcgot,qvgot
      logical :: qerr
      integer :: longcar
      integer, parameter :: maxgaz=3
      integer :: ngaz
      integer :: nesp(maxgaz)
      character*(cespsize) tabesp(ncodesp)
      integer :: ltabesp(ncodesp)
      real :: valesp(ncodesp)
      character*(cstosize) tabsto(ncodsto)
      integer :: ltabsto(ncodsto)
      real :: valsto(ncodsto)
      character*(cvolsize) tabvol(ncodvol)
      integer :: ltabvol(ncodvol)
      real :: valvol(ncodvol)
      logical :: qespmol(maxgaz),qespmas(maxgaz)
      logical :: qmelmol,qmelmas
      logical :: qoxymol
      real :: yesp(ncodesp,maxgaz)
      real :: yoxy(ncodesp,maxoxydant)
      real :: ymel(ncodesp)
      real :: ratiomel(maxgaz)
      real :: sommel,ecartmel
      real :: sommol

      integer :: igaz,j
      integer :: indice
      integer :: iesp,isto,ivol

      integer , parameter :: cnamesize=10
      integer :: longname
      character *(cnamesize) cname

!cth Impression
      INTEGER :: ires(3)
      COMMON/impr/ires

!cth Init
      qcompo=.false.
      qstoechio=.false.
      qvolume=.false.
      qcombmas=.false.
      qmelmol=.false.
      qmelmas=.false.
      qoxydant=.false.
      qhumid=.false.
      qoxymol=.false.
      qoxymas=.false.
      ncombesp=0
      noxyesp=0
      nboxydant=0
      nomoxydant=' '
      lnomoxydant=0
      ngaz=0
      do igaz=1,maxgaz
        ratiomel(igaz)=1.0
        nesp(igaz)=0
        qespmol(igaz)=.false.
        qespmas(igaz)=.false.
        do iesp=1,ncodesp
          yesp(iesp,igaz)=0.0
        enddo
      enddo
      ltabesp=0
      valesp=0.0
      tabesp=' '
      ymel=0.0
      yoxy=0.0
      xcombmol=0.0
      xcombmas=0.0
      xoxymol=0.0
      xoxymas=0.0
      xsoxymol=0.0
      xsoxymas=0.0
      volair=0.0
      volfuel=0.0
      humid=0.0
      thumid=0.0

!
! Debut du programme
!-------------------

      cvers=' '

      nunit=99

      filename=' '
      filename=basename(1:lbasename)//'.iphy'
      lname=longcar(filename)

      open(unit=nunit,file=filename,STATUS='OLD',ERR=9997)

!cth 1ere ligne = numero de version
 170  FORMAT(2x,a8)
      READ(nunit,170,ERR=9998,END=9999) cvers
      if (cvers.ne.cversion) then
        print *,' READ_PHY_COMB : FILE : VERSION ERROR'
        print *,' FILE NAME : ',filename(1:lname)
        print *,' VERSION : EXPECTED : ',cversion
        print *,' VERSION : GOT      : ',cvers
        stop ' CORRECT THIS, AND TRY AGAIN '
      endif

100   format(a)

!cth Composition combustibles
      code=' '
      code='COMBUS'
      lcode=longcar(code)
      call get_code_infile(nunit,code,nvaleur,qcgot,qvgot)
      if (.not.qcgot) go to 9000
!cth      if (qvgot.and.nvaleur.ne.1) go to 9010
      if (.not.qvgot) nvaleur=1
      ngaz=nvaleur
      if (ngaz.gt.maxgaz) go to 9060
!cth      igaz=ngaz
      do igaz=1,ngaz

        read(nunit,100,ERR=9998,END=9999) cline
        lline=longcar(cline)
!cth   Composition molaire
        code=' '
        code='COMPOMOL'
        lcode=longcar(code)
        call get_code_inline(cline,code,nvaleur,qcgot,qvgot)
        if (qcgot) then
          if (.not.qvgot) go to 9020
          nesp(igaz)=nvaleur
          qespmol(igaz)=.true.
          qespmas(igaz)=.false.
          qcompo=.true.
        else
!cth   Composition massique
          code=' '
          code='COMPOMAS'
          lcode=longcar(code)
          call get_code_inline(cline,code,nvaleur,qcgot,qvgot)
          if (qcgot) then
            if (.not.qvgot) go to 9020
            nesp(igaz)=nvaleur
            qespmol(igaz)=.false.
            qespmas(igaz)=.true.
            qcompo=.true.
          endif
        endif
!cth   Verification nombre d especes
!cth SMR et ATHENA1D ne joue pas avec le meme nombre d espece
!cth on teste sur ncodmax et non ncodesp
!cth        if (nesp(igaz).gt.ncodesp) then
        if (nesp(igaz).gt.ncodmax) then
          print *,' READ_PHY_COMB : TROP D ESPECES POUR COMBUSTIBLE'
          print *,' CODE : ',code(1:lcode)
          print *,' NB VALEUR : ',nesp(igaz)
          print *,' NB MAX. ESPECES : ',ncodmax
          stop ' MIEUX VAUT S ARRETER '
        endif
!cth   Lecture des differentes especes
        if (qespmol(igaz).or.qespmas(igaz)) then
          call read_char_real(nunit,cespsize,nesp(igaz),tabesp,ltabesp,valesp,qerr)
          if (qerr) go to 9030
!cth   Decodage nom espece et affectation
          do iesp=1,nesp(igaz)
            code=' '
            code=tabesp(iesp)(1:ltabesp(iesp))
            lcode=longcar(code)
            call get_index_code(ncodesp,cespsize,codesp,code,indice,qcgot,qerr)
            if (qerr) go to 9040
            if (.not.qcgot) go to 9005
            if (indice.gt.ncodmax) then
              print *,' READ_PHY_COMB : ESPECE NON TRAITEE'
              print *,' CODE : ',code(1:lcode)
              print *,' NB VALEUR : ',nesp(igaz)
              print *,' NB MAX. ESPECES : ',ncodmax
              print *,' ESPECE INCONNUE : ',indice,code,codesp(indice)
              stop ' MIEUX VAUT S ARRETER '
            endif
            yesp(indice,igaz)=valesp(iesp)
          enddo
        else
          qcompo=.false.
        endif

      enddo  !Fin boucle sur ngaz

!cth Definition du melange
      if (ngaz.gt.1) then
!cth   Composition molaire
        code=' '
        code='MELANMOL'
        lcode=longcar(code)
        read(nunit,100,ERR=9998,END=9999) cline
        lline=longcar(cline)
        call get_code_inline(cline,code,nvaleur,qcgot,qvgot)
        if (qcgot) then
          if (qvgot.and.nvaleur.ne.ngaz) go to 9010
          qmelmol=.true.
        else
!cth   Composition massique
          code=' '
          code='MELANMAS'
          lcode=longcar(code)
          call get_code_inline(cline,code,nvaleur,qcgot,qvgot)
          if (.not.qcgot) go to 9000
          if (qvgot.and.nvaleur.ne.ngaz) go to 9010
          qmelmas=.true.
        endif
        do igaz=1,ngaz
          read(nunit,*,ERR=9998,END=9999) ratiomel(igaz)
        enddo
      endif

!cth Rapports stoechio
      if (.not.(qcompo.or.qvolume)) then
        code=' '
        code='COMBUS'
        lcode=longcar(code)
        call get_code_infile(nunit,code,nvaleur,qcgot,qvgot)
        if (.not.qcgot) go to 9000
        if (qvgot.and.nvaleur.ne.1) go to 9010
        ngaz=1
!cth   Rapports
        code=' '
        code='STOECHIO'
        lcode=longcar(code)
        read(nunit,100,ERR=9998,END=9999) cline
        lline=longcar(cline)
        call get_code_inline(cline,code,nvaleur,qcgot,qvgot)
        if (qcgot) then
          if (qvgot.and.nvaleur.ne.4) go to 9010
          nvaleur=4
          qstoechio=.true.
        else
          nvaleur=0
          qstoechio=.false.
        endif
!cth   Lecture des differentes valeurs
        if (qstoechio) then
          call read_char_real(nunit,cstosize,nvaleur,tabsto,ltabsto,valsto,qerr)
          if (qerr) go to 9030
!cth   Decodage info : PCI et Rapports stoechio
          do isto=1,nvaleur
            code=' '
            code=tabsto(isto)(1:ltabsto(isto))
            lcode=longcar(code)
            call get_index_code(ncodsto,cstosize,codsto,code,indice,qcgot,qerr)
            if (qerr) go to 9040
            if (.not.qcgot) go to 9000
            if (indice.eq.icodspci) then
              pcisto=valsto(isto)
            else if (indice.eq.icodso2) then
              r_o2_fuel=valsto(isto)
            else if (indice.eq.icodsco2) then
              r_co2_fuel=valsto(isto)
            else if (indice.eq.icodsh2o) then
              r_h2o_fuel=valsto(isto)
            else
              print *,' READ_PHY_COMB : CODE POUR STOECHIO INCONNU '
              print *,' CODE : ',code(1:lcode)
              print *,' NVALEUR : ',nvaleur
              print *,' IVAL    : ',isto
              print *,' VALEUR : ',tabsto(isto)(1:ltabsto(isto)),valsto(isto)
              print *,' INDICE : ',indice
              print *,' TABLE  : ',codsto(indice)(1:cstosize)
            endif
          enddo
        endif
      endif

!cth Rapports volumique
      if (.not.(qcompo.or.qstoechio)) then
        code=' '
        code='COMBUS'
        lcode=longcar(code)
        call get_code_infile(nunit,code,nvaleur,qcgot,qvgot)
        if (.not.qcgot) go to 9000
        if (qvgot.and.nvaleur.ne.1) go to 9010
        ngaz=1
!cth   Rapports
        code=' '
        code='VOLUME'
        lcode=longcar(code)
        read(nunit,100,ERR=9998,END=9999) cline
        lline=longcar(cline)
        call get_code_inline(cline,code,nvaleur,qcgot,qvgot)
        if (qcgot) then
          if (qvgot.and.nvaleur.ne.3) go to 9010
          qvolume=.true.
        endif
!cth   Lecture des differentes valeurs
        if (qvolume) then
          call read_char_real(nunit,cvolsize,nvaleur,tabvol,ltabvol,valvol,qerr)
          if (qerr) go to 9030
!cth   Decodage nom espece et affectation
          do ivol=1,nvaleur
            code=' '
            code=tabvol(ivol)(1:ltabvol(ivol))
            lcode=longcar(code)
            call get_index_code(ncodvol,cvolsize,codvol,code,indice,qcgot,qerr)
            if (qerr) go to 9040
            if (.not.qcgot) go to 9000
            if (indice.eq.icodvpci) then
              pcivol=valvol(ivol)
            else if (indice.eq.icodvair) then
              volair=valvol(ivol)
            else if (indice.eq.icodvfuel) then
              volfuel=valvol(ivol)
            endif
          enddo
        endif
      endif

      if (.not.(qcompo.or.qstoechio.or.qvolume)) then
          print *,' READ_PHY_COMB : COMBUSTIBLE NON DEFINI ?? '
          print *,' COMPO    : ',qcompo
          print *,' STOECHIO : ',qstoechio
          print *,' VOLUME   : ',qvolume
          stop ' MIEUX VAUT S ARRETER '
      endif

!cth Composition oxydant
      code=' '
      code='OXYDANT'
      lcode=longcar(code)
      call get_code_infile(nunit,code,nvaleur,qcgot,qvgot)
      if (.not.qcgot) go to 9000
!cth      if (qvgot.and.nvaleur.ne.1) go to 9010
      if (.not.qvgot) nvaleur=1
      nboxydant=nvaleur
      if (qvgot.and.nboxydant.gt.maxoxydant) go to 9070

      do igaz=1,nboxydant
!cth   Composition molaire
        code=' '
        code='COMPOMOL'
        lcode=longcar(code)
        read(nunit,100,ERR=9998,END=9999) cline
        lline=longcar(cline)
        call get_code_inline(cline,code,nvaleur,qcgot,qvgot)
        if (qcgot) then
          if (.not.qvgot) go to 9020
          noxyesp=nvaleur
          qoxydant=.true.
          if (qoxymas) go to 9090
          qoxymol=.true.
          qoxymas=.false.
        else
!cth   Composition massique
          code=' '
          code='COMPOMAS'
          lcode=longcar(code)
          call get_code_inline(cline,code,nvaleur,qcgot,qvgot)
          if (.not.qcgot) go to 9000
          if (.not.qvgot) go to 9020
          noxyesp=nvaleur
          qoxydant=.true.
          if (qoxymol) go to 9090
          qoxymol=.false.
          qoxymas=.true.
        endif
!cth   Nom eventuel de l oxydant
        call get_name_inline(cline,code,cnamesize,cname,longname,qcgot,qvgot)
        if (qcgot) then
          if (qvgot) then
            nomoxydant(igaz)(1:longname)=cname(1:longname)
            lnomoxydant(igaz)=longname
          else
            if (nboxydant.gt.1) go to 9100
            nomoxydant(igaz)='oxydant'
            lnomoxydant(igaz)=longcar(nomoxydant(igaz))
          endif
        endif
!cth   Verif nombre d especes
        if (noxyesp.gt.ncodesp) then
          print *,' READ_PHY_COMB : TROP D ESPECES POUR OXYDANT'
          print *,' CODE : ',code(1:lcode)
          print *,' NB VALEUR : ',noxyesp
          print *,' NB MAX. ESPECES : ',ncodesp
          stop ' MIEUX VAUT S ARRETER '
        endif
!cth   Lecture des differentes especes
        call read_char_real(nunit,cespsize,noxyesp,tabesp,ltabesp,valesp,qerr)
        if (qerr) go to 9030
!cth   Decodage nom espece et affectation
        do iesp=1,noxyesp
          code=' '
          code=tabesp(iesp)(1:ltabesp(iesp))
          lcode=longcar(code)
          call get_index_code(ncodesp,cespsize,codesp,code,indice,qcgot,qerr)
          if (qerr) go to 9040
          if (.not.qcgot) go to 9005
          yoxy(indice,igaz)=valesp(iesp)
        enddo

      enddo ! Boucle sur Nb oxydant

!cth   Taux d humidite
      if (nboxydant.eq.1) then
        igaz=1
        code=' '
        code='HUMID'
        lcode=longcar(code)
        read(nunit,100,ERR=9998,END=1000) cline
        lline=longcar(cline)
        call get_code_inline(cline,code,nvaleur,qcgot,qvgot)
        if (qcgot.and.nboxydant.ne.1) go to 9080
        if (qcgot) then
          if (qvgot.and.nvaleur.ne.1) go to 9010
          qhumid=.true.
          read(nunit,*,ERR=9998,END=9999) humid
!cth   Temperature pour taux humidite
          read(nunit,100,ERR=9998,END=9999) cline
          code=' '
          code='THUMID'
          lcode=longcar(code)
          call get_code_inline(cline,code,nvaleur,qcgot,qvgot)
          if (qcgot) then
            if (qvgot.and.nvaleur.ne.1) go to 9010
            read(nunit,*,ERR=9998,END=9999) thumid
            thumid=thumid+273.15
          else
            go to 9000
          endif
          if (qhumid.and.abs(yoxy(icodh2o,igaz)).gt.1.0e-6) then
            print *,' READ_PHY_COMB : HUMIDITE ET FRACTION DE H2O ?? '
            stop ' MIEUX VAUT S ARRETER '
          endif
        endif
      endif

1000  continue

      close(unit=nunit)

!cth Verif fraction des especes
      if (qcompo) then
        do igaz=1,ngaz
          sommel=0.0
          do iesp=1,ncodesp
            sommel=sommel+yesp(iesp,igaz)
          enddo
          ecartmel=abs(1.0-sommel)
          if (ecartmel.gt.1.0e-4) then
            print *,' READ_PHY_COMB : SOMME FRACTION COMBU ?? '
            print *,' IGAZ : ',igaz
            print *,' SOMME : ',sommel,ecartmel
            print *,' CORRECTION AUTOMATIQUE ?????? '
            stop ' MIEUX VAUT S ARRETER '
          endif
        enddo
      endif
      if (qoxydant) then
        do igaz=1,nboxydant
          sommel=0.0
          do iesp=1,ncodesp
            sommel=sommel+yoxy(iesp,igaz)
          enddo
          ecartmel=abs(1.0-sommel)
          if (ecartmel.gt.1.0e-4) then
            print *,' READ_PHY_COMB : SOMME FRACTION OXYDANT ?? '
            print *,' SOMME : ',sommel,ecartmel
            print *,' NB ESP : ',ncodesp
            do iesp=1,ncodesp
              print *,iesp,yoxy(iesp,igaz)
            enddo
            print *,' CORRECTION AUTOMATIQUE ?????? '
            stop ' MIEUX VAUT S ARRETER '
          endif
          if (qhumid) then
            if (yoxy(icodh2o,igaz).gt.1.0e-6) then
              print *,' READ_PHY_COMB : OXYDANT SEC AVEC H2O ?? '
              print *,' NB ESP : ',ncodesp
              do iesp=1,ncodesp
                print *,iesp,yoxy(iesp,igaz)
              enddo
              stop ' MIEUX VAUT S ARRETER '
            else
              yoxy(icodh2o,igaz)=0.0
            endif
          endif
        enddo
      endif

!cth Verification du melange eventuel
      if (.not.(qmelmol.or.qmelmas)) then
        if (ngaz.eq.1) then
          ratiomel(ngaz)=1.0
        else
          print *,' READ_PHY_COMB : MULTIGAZ SANS MELANGE ?? '
          print *,' NGAZ : ',ngaz
          print *,' MELANGE : ',qmelmol,qmelmas
          do igaz=1,ngaz
            print *,igaz,ratiomel(igaz)
          enddo
          stop ' MIEUX VAUT S ARRETER '
        endif
      else
        sommel=0.0
        do igaz=1,ngaz
          sommel=sommel+ratiomel(igaz)
        enddo
        ecartmel=abs(1.0-sommel)
        if (ecartmel.gt.1.0e-4) then
          print *,' READ_PHY_COMB : SOMME MELANGE ?? '
          print *,' NGAZ : ',ngaz
          print *,' SOMME : ',sommel,ecartmel
          print *,' CORRECTION AUTOMATIQUE ?????? '
          stop ' MIEUX VAUT S ARRETER '
        endif
      endif

!cth Conversion si necessaire
      if (qcompo) then
        do igaz=1,ngaz
          if (qespmas(igaz)) then
            print *,' READ_PHY_COMB : COMBU FRACTION MASSIQUE NON IMPLANTE '
            print *,' SORRY '
            stop ' MIEUX VAUT S ARRETER '
          endif
        enddo
      endif
      if (qmelmas) then
        qcombmas=.true.
        print *,' READ_PHY_COMB : MELANGE FRACTION MASSIQUE NON IMPLANTE '
        print *,' SORRY '
        stop ' MIEUX VAUT S ARRETER '
      endif

!cth Creation du melange de gaz eventuel
      if (qcompo) then
        if (qmelmas) then
          print *,' READ_PHY_COMB : MELANGE FRACTION MASSIQUE NON IMPLANTE '
          print *,' SORRY '
          stop ' MIEUX VAUT S ARRETER '
        else
          do igaz=1,ngaz
            if (.not.qespmol(igaz)) then
              print *,' READ_PHY_COMB : COMPOSITION MOLAIRE INCONNUE  '
              print *,' IGAZ : ',igaz
              print *,' COMPO : ',qespmol(igaz),qespmas(igaz)
              stop ' MIEUX VAUT S ARRETER '
            endif
          enddo
          do iesp=1,ncodesp
            ymel(iesp)=0.0
            do igaz=1,ngaz
              ymel(iesp)=ymel(iesp)+ratiomel(igaz)*yesp(iesp,igaz)
            enddo
          enddo
        endif
      endif

!cth Test unicite des noms des oxydant
      if (nboxydant.gt.1) then
        do igaz=1,nboxydant-1
          do j=igaz+1,nboxydant
            if(nomoxydant(igaz).eq.nomoxydant(j)) then
              print *,' 2 OXYDANT ONT LE MEME NOM : ',igaz,j
              print *,' OXYDANT 1 : ',nomoxydant(igaz)
              print *,' OXYDANT 2 : ',nomoxydant(j)
              stop ' MIEUX VAUT S ARRETER '
            endif
          enddo
        enddo
      endif

!cth Correction des fractions oxydant et melange
      do igaz=1,nboxydant
        sommol=0.0
        do iesp=1,ncodesp
          sommol=sommol+yoxy(iesp,igaz)
        enddo
        do iesp=1,ncodesp
          yoxy(iesp,igaz)=yoxy(iesp,igaz)*1.0/sommol
        enddo
      enddo
      sommol=0.0
      do iesp=1,ncodesp
        sommol=sommol+ymel(iesp)
      enddo
      do iesp=1,ncodesp
        ymel(iesp)=ymel(iesp)*1.0/sommol
      enddo

!cth Affectation des tableaux four
      if (qcompo) then
        ncombesp=ncodesp
        do iesp=1,ncodesp
          xcombmol(iesp)=ymel(iesp)
        enddo
      endif

      if (qoxydant) then
        if (qoxymas) then
          do igaz=1,nboxydant
            do iesp=1,ncodesp
              if (qhumid) then
                xsoxymas(iesp,igaz)=yoxy(iesp,igaz)
              else
                xoxymas(iesp,igaz)=yoxy(iesp,igaz)
              endif
            enddo
          enddo
        else
          do igaz=1,nboxydant
            do iesp=1,ncodesp
              if (qhumid) then
                xsoxymol(iesp,igaz)=yoxy(iesp,igaz)
              else
                xoxymol(iesp,igaz)=yoxy(iesp,igaz)
              endif
            enddo
          enddo
        endif
      endif

      if (ires(1).ge.1) then
        print *,' READ_PHY_COMB : '
        print *,' COMBU : '
        if (qcompo) then
          print *,' COMPO : ',qcompo
          sommol=0.0
          do iesp=1,ncodesp
            sommol=sommol+xcombmol(iesp)
            print 600,codesp(iesp),xcombmol(iesp),sommol
          enddo
        else if (qstoechio) then
          print *,' STOECHIO : ',qstoechio
          print *,' PCI    : ',pcisto
          print *,' R_O2   : ',r_o2_fuel
          print *,' R_CO2  : ',r_co2_fuel
          print *,' R_H2O  : ',r_h2o_fuel
        else if (qvolume) then
          print *,' VOLUME  : ',qvolume
          print *,' PCI     : ',pcivol
          print *,' VOLAIR  : ',volair
          print *,' VOLFUEL : ',volfuel
        endif
        
600     format(1x,a,1x,7f10.6)
        print *,' OXYDANT : '
        print *,' COMPO : ',qoxydant,qoxymol,qoxymas
        do igaz=1,nboxydant
          print *,' NOM : ',nomoxydant(igaz)
          sommol=0.0
          do iesp=1,ncodesp
            sommol=sommol+xoxymol(iesp,igaz)
            print 600,codesp(iesp),xoxymol(iesp,igaz),xsoxymol(iesp,igaz),xoxymas(iesp,igaz),xsoxymas(iesp,igaz),sommol
          enddo
          if (qhumid) print *,' HUMIDITE : ',humid,thumid
        enddo
      endif

      return

9000  CONTINUE
!cth Erreur code
      PRINT 200,' Code non trouve : ',code(1:lcode),filename(1:lname)
200   format(a,10(1x,a))
      STOP ' MIEUX VAUT S ARRETER '

9005  CONTINUE
!cth Erreur code
      PRINT 200,' Code espece non trouve : ',code(1:lcode),filename(1:lname)
      STOP ' MIEUX VAUT S ARRETER '

9010  CONTINUE
!cth Erreur nvaleur
      PRINT 300,' Nombre de valeur ?? : ',nvaleur,code(1:lcode),filename(1:lname)
300   format(a,1x,i3,10(1x,a))
      STOP ' MIEUX VAUT S ARRETER '

9020  CONTINUE
!cth Erreur nvaleur non trouve
      PRINT 300,' Nombre de valeur non trouve : ',nvaleur,code(1:lcode),filename(1:lname)
      STOP ' MIEUX VAUT S ARRETER '

9030  CONTINUE
!cth Erreur lecture valeurs
      PRINT 200,' Erreur lecture valeur : ',code(1:lcode),filename(1:lname)
      STOP ' MIEUX VAUT S ARRETER '

9040  CONTINUE
!cth Erreur recherche indice surement codesize
      PRINT 200,' Erreur indice code : ',code(1:lcode),filename(1:lname)
      STOP ' MIEUX VAUT S ARRETER '

9060  CONTINUE
!cth Erreur nombre de gaz trop grand
      PRINT 400,' Nombre de gaz trop grand : ',ngaz
400   format(a,1x,i3)
      PRINT 200,' Code : ',code(1:lcode),filename(1:lname)
      STOP ' MIEUX VAUT S ARRETER '

9070  CONTINUE
!cth Erreur nombre d oxydant trop grand
      PRINT 400,' Nombre d oxydant trop grand : ',nboxydant
      PRINT 200,' Code : ',code(1:lcode),filename(1:lname)
      STOP ' MIEUX VAUT S ARRETER '

9080  CONTINUE
!cth Erreur taux d humidite avec plusieurs oxydant
      PRINT 400,' Pas de taux d humidite avec plusieurs oxydant : '
      PRINT 200,' Code : ',code(1:lcode),filename(1:lname)
      STOP ' MIEUX VAUT S ARRETER '

9090  CONTINUE
!cth Erreur composition molaire et massique pour differents oxydant
      PRINT 400,' Pour plusieurs oxydant, toutes les compositions '
      PRINT 400,' doivent etre de meme type : soit molaire, soit massique ! '
      PRINT 200,' Code : ',code(1:lcode),filename(1:lname)
      STOP ' MIEUX VAUT S ARRETER '

9100  CONTINUE
!cth Erreur plusieurs oxydant et pas de nom associe
      PRINT 400,' Pour plusieurs oxydant, il est obligatoire de nommer chacun d eux '
      PRINT 200,' Code : ',code(1:lcode),filename(1:lname)
      STOP ' MIEUX VAUT S ARRETER '
9997  CONTINUE
!cth Erreur ouverture fichier
      PRINT *,' Erreur ouverture fichier : ',filename(1:lname)
      STOP ' MIEUX VAUT S ARRETER '

9998  CONTINUE
!cth Erreur lecture
      PRINT *,' Erreur lecture sur : ',filename(1:lname)
      PRINT 200,' Code : ',code(1:lcode)
      STOP ' MIEUX VAUT S ARRETER '

9999  CONTINUE
!cth Fin de fichier
      PRINT *,' Fin de fichier sur : ',filename(1:lname)
      PRINT 200,' Code : ',code(1:lcode)
      STOP ' MIEUX VAUT S ARRETER '

      end
