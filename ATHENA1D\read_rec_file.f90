	SUBROUTINE read_rec_file(filename,nx,x,recirc)

! ***********************************************************************
!
! Lecture et interpolation de la recirculation
!
! ***********************************************************************
        
        USE modshared

	IMPLICIT NONE

        CHARACTER*(*) filename
	INTEGER :: nx
	REAL, dimension(nx) :: x,recirc

	INTEGER :: i
        INTEGER :: lname
        CHARACTER*130 line
	CHARACTER*8 cvers
        INTEGER :: nxlu
        REAL, allocatable,dimension(:) :: xpos,valrec
        integer :: ierror

        INTEGER :: longcar

          cvers=' '

          lname=longcar(filename)
          open(unit=99,file=filename,STATUS='OLD',ERR=9997)
170       FORMAT(2x,a8)
          READ(99,170,ERR=9998,END=9999) cvers
	  if(cvers(1:1).eq.'v') then
            if (cvers.ne.cversion) then
              print *,' READ_REC_FILE : FILE : VERSION ERROR'
              print *,' FILE NAME : ',filename(1:lname)
              print *,' VERSION : EXPECTED : ',cversion
              print *,' VERSION : GOT      : ',cvers
              stop ' CORRECT THIS, AND TRY AGAIN '
            endif
          else
	    backspace(99)
          endif     
	  read(99,*,ERR=9998,END=9999) nxlu
          read(99,100,ERR=9998,END=9999) line
100       format(a)

          allocate(xpos(nxlu),stat=ierror)
          if (ierror.ne.0) go to 9000
          allocate(valrec(nxlu),stat=ierror)
          if (ierror.ne.0) go to 9000

          do i=1,nxlu
            read(99,*,ERR=9998,END=9999) xpos(i),valrec(i)
          enddo
          close(unit=99)

!cth Interpolation sur maillage 
          call value_mesh_interpolation(nxlu,xpos,valrec,nx,x,recirc)

          do i=nx,2,-1
            recirc(i)=0.5*(recirc(i)+recirc(i-1))
          enddo
          recirc(1)=0.0

          deallocate(xpos)
          deallocate(valrec)

        return

9000    CONTINUE
!cth Erreur allocation
        PRINT *,' READ_REC_FILE : '
        PRINT *,' Erreur allocation : ',ierror
        print *,' Nombre de valeurs : ',nxlu
        STOP ' MIEUX VAUT S ARRETER '

9997    CONTINUE
!cth Erreur ouverture fichier
        PRINT *,' READ_REC_FILE : '
        PRINT *,' Erreur ouverture fichier : ',filename(1:lname)
        STOP ' MIEUX VAUT S ARRETER '

9998    CONTINUE
!cth Erreur lecture
        PRINT *,' READ_REC_FILE : '
        PRINT *,' Erreur lecture sur : ',filename(1:lname)
        STOP ' MIEUX VAUT S ARRETER '

9999    CONTINUE
!cth Fin de fichier
        PRINT *,' READ_REC_FILE : '
        PRINT *,' Fin de fichier sur : ',filename(1:lname)
        STOP ' MIEUX VAUT S ARRETER '

	END
