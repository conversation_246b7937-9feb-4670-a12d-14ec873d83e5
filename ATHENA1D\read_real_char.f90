      subroutine read_real_char(nunit,ncarsize,nvaleur,rval,chaine,lchaine,qerr)

      implicit none

      integer :: nunit
      integer :: ncarsize
      integer :: nvaleur
      character*(ncarsize) chaine(nvaleur)
      integer :: lchaine(nvaleur)
      real :: rval(nvaleur)
      logical :: qerr

      character*130 cline
      character*130 ctamp
      integer :: lline,ltamp
      integer, parameter :: ntabsize=30
      integer, parameter :: maxtab=30
      integer :: nchamp
      character*(ntabsize) champ(maxtab)
      integer :: lchamp(maxtab)
      integer :: longcar
      integer :: i
      integer :: ichamp

      qerr=.false.
      cline=' '

      do i=1,nvaleur
        read(nunit,150,ERR=9998,END=9999) cline
150     format(a)

        lline=longcar(cline)

        call cleanline(cline,ctamp,ltamp)

        call decode_champs(ctamp,ntabsize,maxtab,nchamp,champ,lchamp)

        if (nchamp.lt.1) then
          go to 9995
        endif

        read(champ(1),*,ERR=9996,END=9997) rval(i)

        chaine(i)=' '
        lchaine(i)=0
        if (nchamp.ge.2) then
          if (lchamp(2).gt.0) then
            chaine(i)(1:lchamp(2))=champ(2)(1:lchamp(2))
            lchaine(i)=lchamp(2)
          endif
        endif

100     format(a,a)
200     format(a,a,1x,e13.6)
      enddo

      return

9995  continue
      print *,' READ_REAL_CHAR : NOMBRE DE CHAMPS < 1'
      qerr=.true.
      return

9996  continue
      print *,' READ_REAL_CHAR : ERREUR LECTURE CHAMP 1'
      print *,' LINE : ',cline(1:lline)
      print *,' CHAMPS : ',nchamp
      do ichamp=1,nchamp
        print *,ichamp,champ(ichamp)(1:lchamp(ichamp))
        print *,' -->',champ(ichamp)(lchamp(ichamp)+1:ntabsize)
      enddo
      qerr=.true.
      return

9997  continue
      print *,' READ_REAL_CHAR : END OF FILE LECTURE CHAMP 1'
      print *,' LINE : ',cline(1:lline)
      print *,' CHAMPS : ',nchamp
      do ichamp=1,nchamp
        print *,ichamp,champ(ichamp)(1:lchamp(ichamp))
      enddo
      qerr=.true.
      return

9998  continue
      print *,' READ_REAL_CHAR : ERREUR LECTURE LIGNE '
      qerr=.true.
      return

9999  continue
      print *,' READ_REAL_CHAR : END OF FILE '
      qerr=.true.
      return

      end
