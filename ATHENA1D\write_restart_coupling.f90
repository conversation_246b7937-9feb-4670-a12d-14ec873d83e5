      SUBROUTINE WRITE_RESTART_COUPLING()

      USE modcouplage
      USE modshared

      IMPLICIT NONE

      CHARACTER*130 filename

!cth Informations couplage
 90     format('# ',a8,' # RESTART FILE FOR COUPLING')
        filename=casename(1:lcasename)//'_coupling.rest'
        OPEN(unit=14,file=filename)
		WRITE(14,90) cversout
        WRITE(14,100) itercoupt,itminrelax
100     format(1x,3i6)
        CLOSE(14)

      RETURN
      END
