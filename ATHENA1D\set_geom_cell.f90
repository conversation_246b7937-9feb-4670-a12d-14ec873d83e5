	SUBROUTINE set_geom_cell( &
	&	nposval,posval,valpos,&
	&	posmax ,valmax,       &
	&	nx     ,gridx ,valmail)
!
!**************************************************************
! On affecte le tableau valmail d information par maille
! a partir du tableau valpos d information par position (posval)
!**************************************************************
!
	IMPLICIT NONE

	INTEGER :: nposval
	INTEGER, INTENT (IN) :: nx
	REAL, INTENT (IN)  :: posmax,valmax
	REAL, DIMENSION (nx+1) :: gridx
	REAL, DIMENSION (nposval) :: posval,valpos
	REAL, DIMENSION (nx), INTENT (OUT)  :: valmail

	INTEGER :: i,j
        REAL :: small

        small=1.0e-3

!cth Verif definition de valpos
        if (nposval.lt.1) then
          nposval=1
          posval(nposval)=posmax
          valpos(nposval)=valmax
        endif
!
! Construction de valmail : valeur en chaque maille
!------------------------------------------------------
	valmail=0.
        j=1
        i=1
        do j=1,nposval
          do while(gridx(i).lt.posval(j))
            valmail(i)=valpos(j)
            if (i.eq.nx) exit
            i=i+1
          enddo
        enddo
        do j=i,nx
          valmail(j)=valmail(j-1)
        enddo

	END
