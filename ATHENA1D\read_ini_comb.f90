	SUBROUTINE read_ini_comb(filename,nx,x,tg,tpn)

! ***********************************************************************
!
! Lecture et interpolation des champs initiaux a partir du fichier .ini
!
! ***********************************************************************
        
        USE modshared

	IMPLICIT NONE

        CHARACTER*(*) filename
	INTEGER :: nx
	REAL, dimension(nx) :: x,tg,tpn

	INTEGER :: i
        INTEGER :: lname
        CHARACTER*130 line
        INTEGER :: nxlu
        REAL, allocatable,dimension(:) :: xpos,valgaz,valnord
        integer :: ierror

        INTEGER :: longcar

          lname=longcar(filename)
          open(unit=99,file=filename,STATUS='OLD',ERR=9997)

          read(99,*,ERR=9998,END=9999) nxlu
          read(99,100,ERR=9998,END=9999) line
100       format(a)

          allocate(xpos(nxlu),stat=ierror)
          if (ierror.ne.0) go to 9000
          allocate(valgaz(nxlu),stat=ierror)
          if (ierror.ne.0) go to 9000
          allocate(valnord(nxlu),stat=ierror)
          if (ierror.ne.0) go to 9000

          do i=1,nxlu
            read(99,*,ERR=9998,END=9999) xpos(i),valgaz(i),valnord(i)
            valgaz(i)=valgaz(i)+273.15
            valnord(i)=valnord(i)+273.15
          enddo
          close(unit=99)

!cth Interpolation sur maillage 
          call value_mesh_interpolation(nxlu,xpos,valgaz,nx,x,tg)
          call value_mesh_interpolation(nxlu,xpos,valnord,nx,x,tpn)

          deallocate(xpos)
          deallocate(valgaz)
          deallocate(valnord)

        return

9000    CONTINUE
!cth Erreur allocation
        PRINT *,' READ_INI_COMB : '
        PRINT *,' Erreur allocation : ',ierror
        print *,' Nombre de valeurs : ',nxlu
        STOP ' MIEUX VAUT S ARRETER '

9997    CONTINUE
!cth Erreur ouverture fichier
        PRINT *,' READ_INI_COMB : '
        PRINT *,' Erreur ouverture fichier : ',filename(1:lname)
        STOP ' MIEUX VAUT S ARRETER '

9998    CONTINUE
!cth Erreur lecture
        PRINT *,' READ_INI_COMB : '
        PRINT *,' Erreur lecture sur : ',filename(1:lname)
        STOP ' MIEUX VAUT S ARRETER '

9999    CONTINUE
!cth Fin de fichier
        PRINT *,' READ_INI_COMB : '
        PRINT *,' Fin de fichier sur : ',filename(1:lname)
        STOP ' MIEUX VAUT S ARRETER '

	END
