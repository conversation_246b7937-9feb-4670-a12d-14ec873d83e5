      SUBROUTINE set_boundary_condition(NX,NY,Tinf,Tsup,epsv,epsp,typecondsud,&
     &  typecondnord,typecondouest,typecondest,&
     &  COEFSUD,COEFNORD,Ivarsud,IvarNord,&
     &  hpouest,hpest,tpouest,tpest,&
     &  nxp1,nyp1,nxp2,nyp2,np,emis,&
     &  interdit,RECTANGULAIRE,ncoefsud,ncoefnord,ncoefmax,&
     &  x,tpn1,tps1,tpw1,tpe1)

!---------------------------------------------------------------------
!
!	Lecture des conditions aux limites du four
!
!---------------------------------------------------------------------
!

        USE modshared
        USE modlim

	IMPLICIT NONE
!
! Declaration des variables
!--------------------------
        
	INTEGER :: NX,NY
	LOGICAL,DIMENSION(NX,NY) :: interdit
	LOGICAL :: RECTANGULAIRE

	INTEGER :: count,nbinter
	INTEGER :: I,J,typecond
	INTEGER, DIMENSION(NX), INTENT(OUT) :: typecondsud,typecondnord
	INTEGER                             :: typecondouest,typecondest
	INTEGER, INTENT(IN) :: ncoefmax
	INTEGER, DIMENSION(NX), INTENT(OUT) :: ncoefsud,ncoefNord
	INTEGER, DIMENSION(NX), INTENT(OUT) :: Ivarsud,IvarNord
	INTEGER :: nxp1,nyp1,nyp2,np,ip,nxp2
	REAL, DIMENSION(ncoefmax,NX), INTENT(OUT) :: COEFSUD,COEFNORD	
	REAL, DIMENSION(NX), INTENT(OUT) :: Tinf,Tsup,epsv,epsp
	REAL :: hpouest,hpest,tpouest,tpest
	REAL, DIMENSION(np) :: emis
	REAL, DIMENSION(NX) :: x
	REAL, DIMENSION(NX), INTENT(INOUT) :: tpn1,tps1,tpw1,tpe1

	INTEGER :: nparest,nparouest,nparnord,nparsud
	INTEGER :: nficest,nficouest,nficnord,nficsud
	REAL , ALLOCATABLE, DIMENSION(:) :: hperte
	CHARACTER*130 filename
	INTEGER :: lname
	INTEGER :: ierror

	INTEGER :: longcar

!cth Impression
	INTEGER :: ires(3)
	COMMON/impr/ires

!
! Debut du programme
!-------------------
	Ivarnord = 0
	Ivarsud  = 0

	COEFSUD =0.
	COEFNORD=0.

        allocate(hperte(nx),stat=ierror)
        if (ierror.ne.0) go to 9000
        hperte=0.0

!cth Parois
      nparest=0
      nparouest=0
      nparnord=0
      nparsud=0
      nficest=0
      nficouest=0
      nficnord=0
      nficsud=0
      do ip=1,nparoi
        if (oriparoi(ip).eq.'W') then
!cth Ouest
          nparouest=nparouest+1
          if (desparoi(ip).eq.'F') nficouest=nficouest+1
          if (typparoi(ip).eq.'I'.or.typparoi(ip).eq.'*') then
            typecondouest=1
            hpouest=hparoi(ip)
            tpouest=tparoi(ip)
          else if (typparoi(ip).eq.'T') then
            typecondouest=2
            hpouest=0.0
            tpouest=0.0
            tpw1=tparoi(ip)
          endif
        else if (oriparoi(ip).eq.'E') then
!cth Est
          nparest=nparest+1
          if (desparoi(ip).eq.'F') nficest=nficest+1
          if (typparoi(ip).eq.'I'.or.typparoi(ip).eq.'*') then
            typecondest=1
            hpest=hparoi(ip)
            tpest=tparoi(ip)
          else if (typparoi(ip).eq.'T') then
            typecondest=2
            hpest=0.0
            tpest=0.0
            tpe1=tparoi(ip)
          endif
        else if (oriparoi(ip).eq.'N') then
!cth Nord
          nparnord=nparnord+1
          if (desparoi(ip).eq.'F') nficnord=nficnord+1
          typecond=1
          if (typparoi(ip).eq.'T') typecond=2
          do i=idparoi(ip),ifparoi(ip)
            typecondnord(i)=typecond
            Ivarnord(i)=0
            epsp(i)=epsparoi(ip)
            if (typecond.eq.1) then
              ncoefnord(i)=1
              coefnord(1,i)=hparoi(ip)
              Tsup(i)=tparoi(ip)
            else if (typecond.eq.2) then
              ncoefnord(i)=1
              coefnord(1,i)=0.0
              Tsup(i)=0.0
              tpn1(i)=tparoi(ip)
            endif
          enddo
!cth          if (.not.qposparoi(ip)) then
          if (desparoi(ip).eq.'F') then
            filename=ficparoi(ip)
            lname=longcar(filename)
            if (typparoi(ip).eq.'I') then
              call read_bcd_file(filename,nx,x,hperte)
              do i=1,nx
                coefnord(1,i)=hperte(i)
              enddo
            else if (typparoi(ip).eq.'T') then
              call read_bcd_file(filename,nx,x,tpn1)
            endif
          endif
        else if (oriparoi(ip).eq.'S') then
!cth Sud
          nparsud=nparsud+1
          if (desparoi(ip).eq.'F') nficsud=nficsud+1
          typecond=1
          if (typparoi(ip).eq.'T') typecond=2
          do i=idparoi(ip),ifparoi(ip)
            typecondsud(i)=typecond
            Ivarsud(i)=0
            epsv(i)=epsparoi(ip)
            if (typecond.eq.1) then
              ncoefsud(i)=1
              coefsud(1,i)=hparoi(ip)
              Tinf(i)=tparoi(ip)
            else if (typecond.eq.2) then
              ncoefsud(i)=1
              coefsud(1,i)=0.0
              Tinf(i)=0.0
              tps1(i)=tparoi(ip)
            endif
          enddo
          if (desparoi(ip).eq.'F') then
            filename=ficparoi(ip)
            lname=longcar(filename)
            if (typparoi(ip).eq.'I') then
              call read_bcd_file(filename,nx,x,hperte)
              do i=1,nx
                coefsud(1,i)=hperte(i)
              enddo
            else if (typparoi(ip).eq.'T') then
              call read_bcd_file(filename,nx,x,tps1)
            endif
          endif
        endif
      enddo

      if (nparouest.ne.1) then
        print *,' SET_BOUNDARY_CONDITION : NOMBRE DE PAROI OUEST ?? '
        print *,' Nombre : ',nparouest
        stop ' MIEUX VAUT S ARRETER '
      endif
      if (nficouest.ne.0) then
        print *,' SET_BOUNDARY_CONDITION : PAROI OUEST ?? '
        print *,' DESCRIPTEUR FICHIER ?? '
        stop ' MIEUX VAUT S ARRETER '
      endif
      if (nparest.ne.1) then
        print *,' SET_BOUNDARY_CONDITION : NOMBRE DE PAROI EST ?? '
        print *,' Nombre : ',nparest
        stop ' MIEUX VAUT S ARRETER '
      endif
      if (nficest.ne.0) then
        print *,' SET_BOUNDARY_CONDITION : PAROI EST ?? '
        print *,' DESCRIPTEUR FICHIER ?? '
        stop ' MIEUX VAUT S ARRETER '
      endif
      if (nparnord.eq.0) then
        print *,' SET_BOUNDARY_CONDITION : PAS DE PAROI NORD ?? '
        stop ' MIEUX VAUT S ARRETER '
      endif
      if (nficnord.gt.1) then
        print *,' SET_BOUNDARY_CONDITION : PAROI NORD ?? '
        print *,' PLUSIEUR DESCRIPTEURS FICHIER ?? '
        stop ' MIEUX VAUT S ARRETER '
      endif
      if (nparsud.eq.0.and.code_calcul.eq.code_four_only) then
        print *,' SET_BOUNDARY_CONDITION : PAS DE PAROI SUD ?? '
        stop ' MIEUX VAUT S ARRETER '
      endif
      if (nparsud.ne.0.and.code_calcul.ne.code_four_only) then
        print *,' SET_BOUNDARY_CONDITION : PAROI SUD AVEC COUPLAGE ?? '
        stop ' MIEUX VAUT S ARRETER '
      endif
      if (nficsud.gt.1) then
        print *,' SET_BOUNDARY_CONDITION : PAROI SUD ?? '
        print *,' PLUSIEUR DESCRIPTEURS FICHIER ?? '
        stop ' MIEUX VAUT S ARRETER '
      endif

      deallocate(hperte)

        if (ires(1).ge.1) then
          print *,' SET_BOUNDARY_CONDITION PAROI : '
          if (qparoiall) then
            print *,' --> ALL   : ',epsparoiall,hparoiall,tparoiall
          endif
          print *,' --> NORD  : ',typecondnord(1),epsp(1),COEFnord(1,1),Tsup(1)
          print *,' -->       : ',ncoefnord(1),Ivarnord(1)
          if (nparsud.ne.0) then
            print *,' --> SUD   : ',typecondsud(1),epsv(1),COEFSUD(1,1),Tinf(1)
            print *,' -->       : ',ncoefsud(1),Ivarsud(1)
          endif
          print *,' --> OUEST : ',typecondouest,hpouest,tpouest
          print *,' --> EST   : ',typecondouest,hpouest,tpouest
        endif

!cth Couplage
        if (qcoupall) then
!cth Sud
          typecond=2
          do i=1,nx
	    Tinf(i)=0.0
	    epsv(i)=epscoupall
	    Ivarsud(i)=0
	    typecondsud(i)=typecond
	    ncoefsud(i)=1
            COEFSUD(1,i)=0.0
          enddo
        endif

        if (ires(1).ge.1) then
          if (qcoupall) then
            print *,' LECTHPERTE COUPLAGE : ',qcoupall,epscoupall
            print *,' --> SUD   : ',typecondsud(1),epsv(1),COEFSUD(1,1),Tinf(1)
            print *,' -->       : ',ncoefsud(1),Ivarsud(1)
          else
            print *,' SET_BOUNDARY_CONDITION : PAS COUPLAGE ',qcoupall
          endif
        endif

!cth Affectation des tableaux parois pour le rayonnement

	DO i=1,nx             ! Cote Sud 
          emis(i)=epsV(I)
        ENDDO

        DO ip=nxp1+1,nxp1+nyp1 ! Cote Est
	  emis(ip)=epsp(NX)
	ENDDO

        ip=nxp1+nyp1+1         ! Cote Nord ->  possibilite de pentes.
	IF (.NOT.RECTANGULAIRE) THEN	!seul cas ou des pentes sont possibles
	  count=1
	  do while (.not.interdit (nx,count+1))
	    count=count+1
	    if (count==ny) exit
	  enddo
	  DO i=nx,1,-1
	    j=1
	    DO WHILE (.not.interdit(i,j+1))
	      j=j+1
	      IF (j==ny) exit
	    ENDDO
	    IF (count>j) THEN	! On a trouve un coin qui descend
	      DO nbinter=count-j,1,-1
		emis(ip)=epsp(I)
		ip=ip+1
	      ENDDO
	    ELSEIF (count<j) then     ! On a trouve un coin qui monte
	      DO nbinter=1,J-COUNT
		emis(ip)=epsp(I)				
		ip=ip+1
	      ENDDO
	    ENDIF
	    emis(ip)=epsp(I) ! Avancement horizontal
	    count=j
	    ip=ip+1
	  ENDDO

	ELSE

	  DO I=NX,1,-1
	    EMIS(IP)=EPSP(I)
	    IP=IP+1
	  ENDDO
	ENDIF


	IF (.not.ip==nxp1+nxp2+nyp1+1) THEN
          print*,'------------------------------------------------------------------'
          print*,'      ARRET dans set_boundary_condition.f90'
	print *,'ERROR set_angular_meshes: error pendant la construction'& 
                 &'du maillage exterieur pour le rayonnement a la paroi nord'
	print *,'ERROR set_angular_meshes: nxp1+nyp1+nxp2+1=',nxp1+nyp1+nxp2+1,' ip=',ip
	print*,'------------------------------------------------------------------'
	  stop
	ENDIF
	
	i=0                      ! Cote Ouest
	DO ip=np,np-nyp2+1,-1
          i=i+1
          emis(ip)=epsp(1)
        ENDDO
!
! Fin du programme
!-----------------

        return

!cth Erreur allocation
9000    continue
        print *,' SET_BOUNDARY_CONDITION : '
        print *,' Erreur allocation : ',ierror
        stop ' FIN ANORMALE '

	END


