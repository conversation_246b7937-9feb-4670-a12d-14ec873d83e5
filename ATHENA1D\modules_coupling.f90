
      MODULE modcouplage

      SAVE

      INTEGER :: ITERCOUP0,ITER<PERSON>UPT
      REAL :: critercoup, fctflux
      REAL :: RELAXCDON

!cth Pour expert
      INTEGER :: ITMIN,ITMAX
      INTEGER :: NBITERELAX
      INTEGER :: N<PERSON><PERSON><PERSON><PERSON>AX
      INTEGER :: NIT<PERSON>FRELAX
      INTEGER :: tracecouplage
      REAL :: R<PERSON>AXCFLUX,R<PERSON><PERSON><PERSON>CFMIN,RELA<PERSON>CFDON
      REAL :: RELAXCTEMP,RELAXCTMIN,RELAXCTDON

      END MODULE

      MODULE modshared

      SAVE

      INTEGER, PARAMETER :: NO_CODE=0
      INTEGER, PARAMETER :: CODE_GLASS=1
      INTEGER, PARAMETER :: CODE_REHEAT=2
      INTEGER, PARAMETER :: CODE_SMR=3
      INTEGER, PARAMETER :: CODE_GTM=4
      INTEGER, PARAMETER :: CODE_MAX=10
      INTEGER, PARAMETER :: CODE_NONE=CODE_MAX
      INTEGER, PARAMETER :: CODE_FOUR=CODE_MAX

      INTEGER, PARAMETER :: CODE_FOUR_ONLY=CODE_NONE
      INTEGER, PARAMETER :: CODE_GLASS_ONLY=-CODE_GLASS
      INTEGER, PARAMETER :: CODE_REHEAT_ONLY=-CODE_REHEAT
      INTEGER, PARAMETER :: CODE_SMR_ONLY=-CODE_SMR
      INTEGER, PARAMETER :: CODE_COUPLAGE=CODE_MAX+1

      INTEGER, PARAMETER :: MODU_FOUR1=1
      INTEGER, PARAMETER :: MODU_FOUR2=2
      INTEGER, PARAMETER :: MODU_CHARGE=3

      INTEGER, PARAMETER :: CODE_FOUR1=MODU_FOUR1
      INTEGER, PARAMETER :: CODE_FOUR2=MODU_FOUR2

      INTEGER, PARAMETER :: CHAMP_TEMP=1
      INTEGER, PARAMETER :: CHAMP_FLUX=2
      INTEGER, PARAMETER :: CHAMP_DEGAZ=3
      INTEGER, PARAMETER :: CHAMP_TEMPW=4
      INTEGER, PARAMETER :: CHAMP_TEMPE=5
      INTEGER, PARAMETER :: CHAMP_FRINC=6
      INTEGER, PARAMETER :: CHAMP_FRSPE=7
      INTEGER, PARAMETER :: CHAMP_FCONV=8

      INTEGER :: CODE_SOFT=NO_CODE
      INTEGER :: CODE_CHARGE=NO_CODE
      INTEGER :: TYPECHARGE=NO_CODE
      INTEGER :: CODE_CALCUL=NO_CODE
      INTEGER :: ITMINRELAX
      INTEGER :: RESTART

!cth Pour nb four couples 1 ou 2
      INTEGER, PARAMETER :: MAXFOUR=2
      INTEGER :: NBFOUR

!cth Arguments
      INTEGER, PARAMETER :: DIMARG=5
      INTEGER, PARAMETER :: MAXARG=1
      INTEGER :: nbcmdarg
      CHARACTER*5 :: cmdarg(0:DIMARG)

!cth Conformite maillage four/charge
      INTEGER :: mconfcoup

!cth  Version de l'exe par
      CHARACTER*8 CVERSION
!cth	CHARACTER*8, PARAMETER  :: CVERSOUT='v1.1-201'
      CHARACTER*8 :: CVERSOUT='v1.1-201'
      CHARACTER*8 :: CFULLTAG

!cth Pour nom de module et de programme
      CHARACTER*7 MODUNAME(CODE_MAX)
      CHARACTER*12 PROGNAME(CODE_MAX)

!cth Pour nom de fichier a partir du nom du cas
      INTEGER, PARAMETER :: LMAXCASE=35
      INTEGER, PARAMETER :: LMAXNAME=50
      INTEGER :: LCASENAME,LNAMELOAD,LNAMEFOUR(MAXFOUR)
      CHARACTER*(LMAXCASE) CASENAME
      CHARACTER*(LMAXNAME) NAMELOAD
      CHARACTER*(LMAXNAME) NAMEFOUR(MAXFOUR)

!cth  Version = TAG CVS de ce fichier
      CHARACTER*80 ALLTAG(CODE_MAX)
      CHARACTER*80 ALLREVISION(CODE_MAX)
      CHARACTER*80, PARAMETER  :: COUPTAG='$Name:  $'
      CHARACTER*80, PARAMETER  :: COUPREV='$Revision: 1.5 $'
      CHARACTER*8 COUPVERS

      
      DATA MODUNAME/ 'glass  ', &
     &               'bille  ', &
     &               'tubes  ', &
     &               'gtm    ', &
     &               '       ', &
     &               '       ', &
     &               '       ', &
     &               '       ', &
     &               '       ', &
     &               'comb   '/

      DATA PROGNAME/ 'GLASS1D     ', &
     &               'REHEAT1D    ', &
     &               'SMR1D       ', &
     &               'ATHENA1D-GTM', &
     &               '            ', &
     &               '            ', &
     &               '            ', &
     &               '            ', &
     &               '            ', &
     &               'ATHENA1D    '/

      DATA ALLTAG      / 'NOVALUE     ', &
     &                   'NOVALUE     ', &
     &                   'NOVALUE     ', &
     &                   'NOVALUE     ', &
     &                   'NOVALUE     ', &
     &                   'NOVALUE     ', &
     &                   'NOVALUE     ', &
     &                   'NOVALUE     ', &
     &                   'NOVALUE     ', &
     &                   'NOVALUE     '/

      DATA ALLREVISION / 'NOVALUE     ', &
     &                   'NOVALUE     ', &
     &                   'NOVALUE     ', &
     &                   'NOVALUE     ', &
     &                   'NOVALUE     ', &
     &                   'NOVALUE     ', &
     &                   'NOVALUE     ', &
     &                   'NOVALUE     ', &
     &                   'NOVALUE     ', &
     &                   'NOVALUE     '/

      END MODULE
