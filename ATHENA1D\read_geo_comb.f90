    	 subroutine read_geo_comb(basefour,lbasefour,baseload,lbaseload,&
     &     lxmax,lymax,lzmax,nx,ny,nxnoeud,nynoeud,nlongzone,nposbrul,&
     &     maxhaut,nlargfour,nhautfour,nlargload,nhautload,&
     &     xlargfour,xhautfour,xlargload,xhautload,&
     &     vlargfour,vhautfour,vlargload,vhautload,&
     &     maxpointx,gridx,maxpointy,gridy,longfour,&
     &     maxzone,qzone,longzone,qbrul,posbrul,&
     &     qnxzone,qdxzone,nxzone,dxzone,qnxbrul,qdxbrul,nxbrul,dxbrul)
!****************************************************************************
!
!	Lecture du fichier .geo d un four
!	Seuls arguments d entree basename et lbasename pour nom de fichier
!		
!****************************************************************************
 
      USE modshared

      IMPLICIT NONE
!
! Declaration des variables
!--------------------------
      CHARACTER*(*) basefour
      INTEGER :: lbasefour
      CHARACTER*(*) baseload
      INTEGER :: lbaseload
      real :: lxmax,lymax,lzmax
      integer :: nx,ny,nxnoeud,nynoeud,nlongzone,nposbrul
      integer :: maxhaut,nlargfour,nhautfour,nlargload,nhautload
      real, dimension(maxhaut) :: xlargfour,xhautfour,xlargload,xhautload
      real, dimension(maxhaut) :: vlargfour,vhautfour,vlargload,vhautload
      integer :: maxpointx,maxpointy
      real, dimension(maxpointx) :: gridx
      real, dimension(maxpointy) :: gridy
      real :: longfour
      integer :: maxzone
      logical :: qzone,qbrul
      real, dimension(maxzone) :: longzone,posbrul
      integer, dimension(maxzone) :: nxzone,nxbrul
      real, dimension(maxzone) :: dxzone,dxbrul
      logical :: qnxzone,qdxzone
      logical :: qnxbrul,qdxbrul

      CHARACTER*8 cvers
      INTEGER :: longcar
      CHARACTER*130 filename
      INTEGER :: lname
      INTEGER :: nunit
      CHARACTER*130 cline
      INTEGER :: lline
      CHARACTER*20 code
      INTEGER :: lcode
      logical :: qcgot,qvgot,qerr
      integer :: indtab(maxpointx+1)
      INTEGER :: nvaleur
      INTEGER :: i
      real, dimension(maxhaut) :: posval,valpos

      REAL :: diffval

!cth Niveau d impression
      INTEGER  ires(3)
      COMMON/impr/ires

!
! Debut du programme
!-------------------

      cvers=' '

      nunit=99

      filename=' '
      filename=basefour(1:lbasefour)//'.igeo'
      lname=longcar(filename)

      open(unit=nunit,file=filename,STATUS='OLD',ERR=9997)

!cth 1ere ligne = numero de version
 170  FORMAT(2x,a8)
      READ(nunit,170,ERR=9998,END=9999) cvers
      if (cvers.ne.cversion) then
        print *,' READ_GEO_COMB : FILE : VERSION ERROR'
        print *,' FILE NAME : ',filename(1:lname)
        print *,' VERSION : EXPECTED : ',cversion
        print *,' VERSION : GOT      : ',cvers
        stop ' CORRECT THIS, AND TRY AGAIN '
      endif

      lline=longcar(cline)
100   format(a)

!cth Longueur max du four
      code=' '
      code='LXMAX'
      lcode=longcar(code)
      call get_code_infile(nunit,code,nvaleur,qcgot,qvgot)
      if (.not.qcgot) go to 9000
      if (qvgot.and.nvaleur.ne.1) go to 9010
      read(nunit,*,ERR=9998,END=9999) lxmax
      longfour=lxmax
!cth Hauteur max du four
      code=' '
      code='LYMAX'
      lcode=longcar(code)
      call get_code_infile(nunit,code,nvaleur,qcgot,qvgot)
      if (.not.qcgot) go to 9000
      if (qvgot.and.nvaleur.ne.1) go to 9010
      read(nunit,*,ERR=9998,END=9999) lymax
!cth largeur max du four
      code=' '
      code='LZMAX'
      lcode=longcar(code)
      call get_code_infile(nunit,code,nvaleur,qcgot,qvgot)
      if (.not.qcgot) go to 9000
      if (qvgot.and.nvaleur.ne.1) go to 9010
      read(nunit,*,ERR=9998,END=9999) lzmax
!cth Maillage en X
      code=' '
      code='XMAIL'
      lcode=longcar(code)
      call get_code_infile(nunit,code,nvaleur,qcgot,qvgot)
      if (qcgot) then
        if (.not.qvgot) go to 9020
        nxnoeud=nvaleur
        nx=nxnoeud-1
        call read_int_real(nunit,nxnoeud,indtab,gridx,qerr)
        if (qerr) go to 9030
        diffval=abs(lxmax-gridx(nxnoeud))
        if (abs(gridx(1)).gt.1.0e-4) then
          print *,' '
          print *,' WARNING XMAIL FIRST POINT : '
          print *,' ==> ',1,gridx(1)
          print *,' '
        endif
        if (diffval.gt.1.0e-4) then
          print *,' '
          print *,' WARNING XMAIL LAST POINT : '
          print *,' ==> ',nxnoeud,gridy(nxnoeud),lxmax
          print *,' '
        endif
      else
        nxnoeud=0
        nx=0
      endif
!cth Maillage en Y
      code=' '
      code='YMAIL'
      lcode=longcar(code)
      call get_code_infile(nunit,code,nvaleur,qcgot,qvgot)
      if (qcgot) then
        if (.not.qvgot) go to 9020
        nynoeud=nvaleur
        ny=nynoeud-1
        call read_int_real(nunit,nynoeud,indtab,gridy,qerr)
        if (qerr) go to 9030
        diffval=abs(lymax-gridy(nynoeud))
        if (abs(gridy(1)).gt.1.0e-4) then
          print *,' '
          print *,' WARNING YMAIL FIRST POINT : '
          print *,' ==> ',1,gridy(1)
          print *,' '
        endif
        if (diffval.gt.1.0e-4) then
          print *,' '
          print *,' WARNING YMAIL LAST POINT : '
          print *,' ==> ',nynoeud,gridy(nynoeud),lymax
          print *,' '
        endif
      else
        nynoeud=0
        ny=0
      endif
!cth Position bruleurs pour definition zones
      code=' '
      code='XBRULEUR'
      lcode=longcar(code)
      call get_code_infile(nunit,code,nvaleur,qcgot,qvgot)
      if (qcgot) then
        if (.not.qvgot) go to 9020
        nposbrul=nvaleur
        qbrul=.true.
        call read_int_real(nunit,nposbrul,indtab,posbrul,qerr)
        if (qerr) go to 9030
      else
        qbrul=.false.
        nposbrul=0
      endif
!cth Definition directe des zones par leur longueur
      code=' '
      code='LZONE'
      lcode=longcar(code)
      call get_code_infile(nunit,code,nvaleur,qcgot,qvgot)
      if (qcgot) then
        if (.not.qvgot) go to 9020
        nlongzone=nvaleur
        qzone=.true.
        call read_int_real(nunit,nlongzone,indtab,longzone,qerr)
        if (qerr) go to 9030
      else
        qzone=.false.
        nlongzone=0
      endif
!cth Remaillage nx par bruleur
      if (nposbrul.ne.0) then
        code=' '
        code='NXBRUL'
        lcode=longcar(code)
        call get_code_infile(nunit,code,nvaleur,qcgot,qvgot)
        if (qcgot) then
          if (.not.qvgot) go to 9020
          if (nvaleur.ne.nposbrul) then
            print *,' READ_GEO_COMB : NOMBRE DE VALEUR ERRONE '
            print *,' NVAL : ',nvaleur,nposbrul,code(1:lcode)
            stop ' MIEUX VAUT S ARRETER '
          endif
          call read_int_int(nunit,nposbrul,indtab,nxbrul,qerr)
          if (qerr) go to 9030
          qnxbrul=.true.
        else
          qnxbrul=.false.
        endif
!cth Remaillage dx par bruleur
        code=' '
        code='DXBRUL'
        lcode=longcar(code)
        call get_code_infile(nunit,code,nvaleur,qcgot,qvgot)
        if (qcgot) then
          if (.not.qvgot) go to 9020
          if (nvaleur.ne.nposbrul) then
            print *,' READ_GEO_COMB : NOMBRE DE VALEUR ERRONE '
            print *,' NVAL : ',nvaleur,nposbrul,code(1:lcode)
            stop ' MIEUX VAUT S ARRETER '
          endif
          call read_int_real(nunit,nposbrul,indtab,dxbrul,qerr)
          if (qerr) go to 9030
          qdxbrul=.true.
        else
          qdxbrul=.false.
        endif
      endif
!cth Remaillage nx par zone
      if (nlongzone.ne.0) then
        code=' '
        code='NXZONE'
        lcode=longcar(code)
        call get_code_infile(nunit,code,nvaleur,qcgot,qvgot)
        if (qcgot) then
          if (.not.qvgot) go to 9020
          if (nvaleur.ne.nlongzone) then
            print *,' READ_GEO_COMB : NOMBRE DE VALEUR ERRONE '
            print *,' NVAL : ',nvaleur,nlongzone,code(1:lcode)
            stop ' MIEUX VAUT S ARRETER '
          endif
          call read_int_int(nunit,nlongzone,indtab,nxzone,qerr)
          if (qerr) go to 9030
          qnxzone=.true.
        else
          qnxzone=.false.
        endif
!cth Remaillage dx par zone
        code=' '
        code='DXZONE'
        lcode=longcar(code)
        call get_code_infile(nunit,code,nvaleur,qcgot,qvgot)
        if (qcgot) then
          if (.not.qvgot) go to 9020
          if (nvaleur.ne.nlongzone) then
            print *,' READ_GEO_COMB : NOMBRE DE VALEUR ERRONE '
            print *,' NVAL : ',nvaleur,nlongzone,code(1:lcode)
            stop ' MIEUX VAUT S ARRETER '
          endif
          call read_int_real(nunit,nlongzone,indtab,dxzone,qerr)
          if (qerr) go to 9030
          qdxzone=.true.
        else
          qdxzone=.false.
        endif
      endif
!cth Largeur four en fonction de la position
      code=' '
      code='XLARG'
      lcode=longcar(code)
      call get_code_infile(nunit,code,nvaleur,qcgot,qvgot)
      if (qcgot) then
        if (.not.qvgot) go to 9020
        nlargfour=nvaleur
        call read_real_real(nunit,nlargfour,xlargfour,vlargfour,qerr)
        if (qerr) go to 9030
      else
        nlargfour=0
      endif
!cth Hauteur four en fonction de la position
      code=' '
      code='XHAUT'
      lcode=longcar(code)
      call get_code_infile(nunit,code,nvaleur,qcgot,qvgot)
      if (qcgot) then
        if (.not.qvgot) go to 9020
        nhautfour=nvaleur
        call read_real_real(nunit,nhautfour,xhautfour,vhautfour,qerr)
        if (qerr) go to 9030
      else
        nhautfour=0
      endif

      close(unit=nunit)

      if (nhautfour.eq.1) then
        xhautfour(nhautfour)=longfour
        vhautfour(nhautfour)=lymax
      endif

!cth Lecture fichier geo de la charge
      filename=' '
      filename=baseload(1:lbaseload)//'.igeo'
      lname=longcar(filename)

      open(unit=nunit,file=filename,STATUS='OLD',ERR=9997)

!cth 1ere ligne = commentaire
      read(nunit,100,ERR=9998,END=9999) cline

!cth Largeur load en fonction de la position
      code=' '
      code='XLARG'
      lcode=longcar(code)
      call get_code_infile(nunit,code,nvaleur,qcgot,qvgot)
      if (qcgot) then
        if (.not.qvgot) go to 9020
        nlargload=nvaleur
        call read_real_real(nunit,nlargload,posval,valpos,qerr)
        if (qerr) go to 9030
      else
        nlargload=0
      endif

!cth      if(posval(nlargload).ne.longfour) posval = posval-(posval(nlargload)-longfour)
      if(posval(nlargload).ne.longfour) then
        if(posval(nlargload).gt.longfour) then
          print *,' '
          print *,' ----------------------------------------------------------------'
          print *,' READ_GEO_LOAD : XLARG : LOAD AND FURNACE LENGTH SEEM NO COHERENT'
          print *,' Nb LARG : ',nlargload
          print *,' LAST POS : ',posval(nlargload)
          print *,' LENGTH FURNACE : ',longfour
          print *,' XLARG : '
          do i=1,nlargload
            print *,i,posval(i),valpos(i)
          enddo
          posval = posval-(posval(nlargload)-longfour)
          print *,' TRANSLATION WOULD BE CERTAINLY WRONG : '
          do i=1,nlargload
            print *,i,posval(i),valpos(i)
          enddo
          print *,' RESULT IN FRURNACE COORD. WOULD BE : '
          do i=1,nlargload
            xlargload(i) = longfour-posval(nlargload-i)
            vlargload(i)= valpos(nlargload-i+1)
            print *,i,xlargload(i),vlargload(i)
          enddo
          print *,' '
          STOP ' FATAL ERROR '
        endif
        posval = posval-(posval(nlargload)-longfour)
      endif

      do i=1,nlargload-1
        xlargload(i) = longfour-posval(nlargload-i)
        vlargload(i)= valpos(nlargload-i+1)
      enddo
      if (nlargload.gt.0) then
        xlargload(nlargload)= longfour
        vlargload(nlargload)= valpos(1)
      else
        nlargload=1
        xlargload(nlargload)= longfour
        vlargload(nlargload)= lzmax
      endif
!cth hauteur load en fonction de la position
      code=' '
      code='XHAUT'
      lcode=longcar(code)
      call get_code_infile(nunit,code,nvaleur,qcgot,qvgot)
      if (qcgot) then
        if (.not.qvgot) go to 9020
        nhautload=nvaleur
        call read_real_real(nunit,nhautload,posval,valpos,qerr)
        if (qerr) go to 9030
      else
        nhautload=0
      endif
      do i=1,nhautload-1
        xhautload(i) = longfour-posval(nhautload-i)
        vhautload(i)= valpos(nhautload-i+1)
      enddo
      if (nhautload.gt.0) then
      xhautload(nhautload)= longfour
      vhautload(nhautload)= valpos(1)
      endif

      close(unit=nunit)

      if (ires(1).ge.1) then
        print *,' READ_GEO_COMB : '
        print *,' LXMAX : ',lxmax
        print *,' LYMAX : ',lymax
        print *,' LZMAX : ',lzmax
        print *,' HAUTEUR FOUR : ',nhautfour
        do i=1,nhautfour
          print *,' --> ',i,xhautfour(i),vhautfour(i)
        enddo
        print *,' LARGEUR FOUR : ',nlargfour
        do i=1,nlargfour
          print *,' --> ',i,xlargfour(i),vlargfour(i)
        enddo
        print *,' POS BRULEUR : ',nposbrul
        do i=1,nposbrul
          print *,' --> ',i,posbrul(i)
        enddo
        if (qnxbrul) then
          print *,' NX BRULEUR : ',nposbrul
          do i=1,nposbrul
            print *,' --> ',i,nxbrul(i)
          enddo
        endif
        if (qdxbrul) then
          print *,' DX BRULEUR : ',nposbrul
          do i=1,nposbrul
            print *,' --> ',i,dxbrul(i)
          enddo
        endif
        print *,' ZONE : ',nlongzone
        do i=1,nlongzone
          print *,' --> ',i,longzone(i)
        enddo
        if (qnxzone) then
          print *,' NX ZONE : ',nlongzone
          do i=1,nlongzone
            print *,' --> ',i,nxzone(i)
          enddo
        endif
        if (qdxzone) then
          print *,' DX ZONE : ',nlongzone
          do i=1,nlongzone
            print *,' --> ',i,dxzone(i)
          enddo
        endif
        print *,' HAUTEUR LOAD : ',nhautload
        do i=1,nhautload
          print *,' --> ',i,xhautload(i),vhautload(i),posval(nhautload-i+1),valpos(nhautload-i+1)
        enddo
        print *,' LARGEUR LOAD : ',nlargload
        do i=1,nlargload
          print *,' --> ',i,xlargload(i),vlargload(i)
        enddo
      endif

!cth Test coherence 
      do i=1,nposbrul
        if (posbrul(i).lt.0.0.or.posbrul(i).gt.lxmax) then
          print *,' READ_GEO_COMB : POSBRUL ? : ',i,posbrul(i),lxmax
          stop ' ERROR '
        endif
      enddo

      return

9000  CONTINUE
!cth Erreur code
      print *,' READ_GEO_COMB : '
      PRINT 200,' Code non trouve : ',code(1:lcode),filename(1:lname)
200   format(a,10(1x,a))
      STOP ' MIEUX VAUT S ARRETER '

9010  CONTINUE
!cth Erreur nvaleur
      print *,' READ_GEO_COMB : '
      PRINT 300,' Nombre de valeur ?? : ',nvaleur,code(1:lcode),filename(1:lname)
300   format(a,1x,i3,10(1x,a))
      STOP ' MIEUX VAUT S ARRETER '

9020  CONTINUE
!cth Erreur nvaleur non trouve
      print *,' READ_GEO_COMB : '
      PRINT 300,' Nombre de valeur non trouve : ',nvaleur,code(1:lcode),filename(1:lname)
      STOP ' MIEUX VAUT S ARRETER '

9030  CONTINUE
!cth Erreur lecture valeurs
      print *,' READ_GEO_COMB : '
      PRINT 200,' Erreur lecture valeur : ',code(1:lcode),filename(1:lname)
      STOP ' MIEUX VAUT S ARRETER '

9997  CONTINUE
!cth Erreur ouverture fichier
      print *,' READ_GEO_COMB : '
      PRINT *,' Erreur ouverture fichier : ',filename(1:lname)
      STOP ' MIEUX VAUT S ARRETER '

9998  CONTINUE
!cth Erreur lecture
      print *,' READ_GEO_COMB : '
      PRINT *,' Erreur lecture sur : ',filename(1:lname)
      STOP ' MIEUX VAUT S ARRETER '

9999  CONTINUE
!cth Fin de fichier
      print *,' READ_GEO_COMB : '
      PRINT *,' Fin de fichier sur : ',filename(1:lname)
      STOP ' MIEUX VAUT S ARRETER '

      END
