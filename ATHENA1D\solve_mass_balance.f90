	SUBROUTINE solve_mass_balance(nx,mb,dr,sor,deg,ybN,ybO,ybCO2,ybH2O,ybAr&
               &,ybF,dp,yN,yO,yCO2,yH2O,yAr,yF,yNDEG,yODEG,yCO2DEG,yH2ODEG&
               &,yArDEG,yFDEG,debitglob,debitconv,xfumeemol,WMfum)

!********************************************************************************
! 
!       On calcule les produits de combustion aux bruleurs avec reacbrul. Ils sont les
!       variables aj.
!       On calcule les produits de combustion de la tranche precedent avec reacfour. Ils
!       sont les variables ent
!       Apres on fait le bilan massique dans la tranche avec les variables aj et ent. 
!	On fait le bilan massique et le bilan des especes
!	On suppose qu'on n'ajoute pas des bruleurs dans la derniere tranche
!
!                                 ATTENTION
!       Il faut ajouter le debit qu on retire de chaque tranche mper.
!       mper sera la adittion de une entree qu il faut ajouter au logiciel, plus
!       la pert de oxygene pour la existence de calamine et qui est calcule par la partie 
!       conduction (oper).
!********************************************************************************

        USE modespece
	USE modshared

	IMPLICIT NONE

        INCLUDE 'prophy.cmn'

!
        CHARACTER*130  filename
	INTEGER, INTENT(IN) :: NX
        REAL, INTENT(IN), DIMENSION (NX) :: mb,yNDEG,yODEG,yCO2DEG,yH2ODEG
        REAL, INTENT(IN), DIMENSION (NX) :: yArDEG,yFDEG,DEG
	REAL, INTENT(OUT), DIMENSION(0:NX+1) :: DP 	!DEBIT PRINCIPAL
	REAL, INTENT(INOUT), DIMENSION(0:NX+1) :: DR 	!DEBIT RECIRCULE
	REAL, INTENT(OUT), DIMENSION(NX) :: debitglob 	!DEBIT GLOBAL
	REAL, INTENT(OUT), DIMENSION(NX) :: debitconv 	!DEBIT POUR CONVECTION
	REAL, INTENT(INOUT), DIMENSION(NX) :: SOR	!SORTIES (CHEMINEES)
	REAL, intent (INOUT), dimension (NX) ::  ybO,ybCO2,ybH2O,ybAr,ybF,YbN

        REAL, dimension(*) :: xfumeemol

	REAL, INTENT(OUT), DIMENSION (NX) :: yN,yO
        REAL, INTENT(OUT), DIMENSION (NX) :: yCO2,yH2O,yAr,yF
        REAL, INTENT(OUT), DIMENSION (NX) :: WMfum

	REAL :: yNaj,yOaj,yCO2aj,yH2Oaj,yAraj,yFaj
	REAL, DIMENSION (NX) :: yN1,yO1
        REAL, DIMENSION (NX) :: yCO21,yH2O1,yAr1,yF1
	REAL :: amfu,amch4 !FRACTIONS DE COMBUSTIBLE IMBRULE BRULABLE ET IMBRULABLE
	REAL :: SMALL,ERREUR
	INTEGER :: J1,J2

	INTEGER :: i,J, iadj, ideb, ifin, iamont, iaval, iincr, icourant

        REAL, DIMENSION(0:nx+1) :: dpreel,dpjuste,drjuste
        REAL :: enttot,sortot,mbtot,degtot
        REAL :: bilantot,errtot
        REAL :: ratiotot
        LOGICAL :: nosortie

        INTEGER :: ii
        INTEGER :: ndebit
        REAL :: soment,somsor,somme
        REAL :: debitp,debitr
	REAL :: dbeN,dbeO,dbeCO2,dbeH2O,dbeAr,dbeF
	REAL :: dboN,dboO,dboCO2,dboH2O,dboAr,dboF
	REAL :: bilN,bilO,bilCO2,bilH2O,bilAr,bilF
	REAL :: rbilN,rbilO,rbilCO2,rbilH2O,rbilAr,rbilF

        INTEGER :: iesp
        INTEGER :: iterbil
	INTEGER :: longcar

!cth Impression
        INTEGER :: ires(3)
        COMMON/impr/ires

	REAL :: reacbrul,reacfour
	COMMON/combustion/reacbrul,reacfour
	REAL :: PCI,WM,R_O2_FUEL,R_CO2_FUEL
	REAL :: R_H2O_FUEL,R_N2_FUEL,INVWM,R_H2O_CO2,R_CO2_O2,R_N2_O2
	COMMON/combustible/PCI,WM,R_O2_FUEL,R_CO2_FUEL&
     &  ,R_H2O_FUEL,R_N2_FUEL,INVWM,R_H2O_CO2,R_CO2_O2,R_N2_O2

!
! Debut du programme
!-------------------
	SMALL=1.E-6

	yN=0. 
	yO=0. 
	YCO2=0.
	YH2O=0.
	YAR=0.
	YF=0.
	YN1=YN
	YO1=YO
	YCO21=YCO2
	YH2O1=YH2O
	YAR1=YAR
	YF1=YF
	DP=0.
	WMfum=0.

!cth Calcul du bilan massique global
!cth       Recherche sortie
        nosortie=.true.
        do i=1,nx
          if (abs(sor(i)).ge.1.0e-6) nosortie=.false. 
        enddo
!cth       Calcul debit bruleurs
        mbtot=0.0        
        do i=1,nx
	  mbtot=mbtot+mb(I)
        enddo
!cth       Calcul debit degazage
        degtot=0.0        
        do i=1,nx
	  degtot=degtot+deg(I)
        enddo
!cth       Debit global
        enttot=mbtot+degtot
!cth       Calcul debit sortie
        if (nosortie) then
          sor(nx)=enttot
        endif
        sortot=0.0        
        do i=1,nx
	  sortot=sortot+sor(i)
        enddo
        if (abs(sortot).lt.1.0e-6) then
          print *,' SOLVE_MASS : SORTOT : ',sortot
          print *,'   ENTTOT = ',enttot,mbtot,degtot
          do i=1,nx
            print *,' SOR(i) : ',i,sor(i),mb(i),deg(i)
          enddo
          STOP ' INPUT/OUTPUT FLOW ??? '
        endif
!cth       Bilan global
        bilantot=enttot-sortot
        errtot=abs(bilantot)/enttot
        ratiotot=enttot/sortot
        sortot=0.0        
        do i=1,nx
          sor(i)=sor(i)*ratiotot
	  sortot=sortot+sor(i)
        enddo

        if (ires(1).ge.1) then
          if (nosortie) then
            print *,' SORTIE UNIQUE EN NX : ',nx,sor(nx)
          endif
          print *,' DEBIT GLOBAUX : ',mbtot,degtot,enttot,sortot
          print *,' BILAN GLOBAL  : ',bilantot,errtot,ratiotot
        endif

        dpreel=0.0
        dpjuste=0.0
        drjuste=0.0
!cth       Calcul debit reel
        do i=1,nx
	  DPREEL(I)=MB(I)+DEG(I)+DPREEL(i-1)-SOR(I)
        enddo
!cth       Calcul debit et recirc juste
        do i=1,nx-1
	  dpjuste(i)=max(dpreel(i),0.0)
	  drjuste(i+1)=min(dpreel(i),0.0)
	  drjuste(i+1)=abs(drjuste(i+1))
        enddo
!cth       Calcul standard debit avec recirc
        do i=1,nx
	  DP(I)=MB(I)+DEG(I)+DR(i+1)+DP(i-1)-DR(I)-SOR(I)
        enddo

        if (ires(1).ge.1) then
!cth ecriture fichier verif

	    filename=casename(1:lcasename)//'_'//moduname(code_max)(1:longcar(moduname(code_max)))//'.flow_m'
        open(unit=27,file=filename)
        write(27,150) '  Set Furnace mass flow rate '
150     format(1x,a)
        write(27,150) ' Iter  Dbrul    Ddega    Dsort    Dpreel    Dpjuste    Drjuste    Drecy    Dtran'
        do i=1,NX
         write(27,110) i,mb(i),deg(i),sor(i),dpreel(i),dpjuste(i),drjuste(i),dr(i),dp(i)
        enddo
        close(unit=27)
110     format(i4,15(1x,f9.5))
        endif

!cth       Calcul en tenant compte d une recirc imposee
        do i=1,nx-1
	  dpjuste(i)= dpjuste(i)+dr(i+1)
        enddo
        do i=2,nx
	  drjuste(i)=drjuste(i)+dr(i)
        enddo
!cth       Correction du dr pour etre juste
        do i=2,nx
	  dr(i)=drjuste(i)
        enddo
!cth       Calcul standard debit avec nouvelle recirc
        dp=0.0
        do i=1,nx
	  DP(I)=MB(I)+DEG(I)+DR(i+1)+DP(i-1)-DR(I)-SOR(I)
        enddo

        if (ires(1).ge.1) then
!cth ecriture fichier verif
	    filename=casename(1:lcasename)//'_'//moduname(code_max)(1:longcar(moduname(code_max)))//'.flow_mco'
        open(unit=27,file=filename)
        write(27,150) '  Set Furnace mass flow rate '
        write(27,150) ' Iter  Dbrul    Ddega    Dsort    Dpreel    Dpjuste    Drjuste    Drecy    Dtran'
        do i=1,NX
         write(27,110) i,mb(i),deg(i),sor(i),dpreel(i),dpjuste(i),drjuste(i),dr(i),dp(i)
        enddo
        close(unit=27)
        endif

        dp=0.0

!
!bilan de la reaction de combustion
!----------------------------------
!	dans chaque tranche, on determine la composition des gaz apres
!	combustion (yaj), puis le debit pricipal qui sort de la tranche i
!	et sa composition (y(i)). on stocke dans yb(i) la composition des gaz
!	issus de la combustioN
!	on doit faire des iterations car on ne connait pas a priori
!	la composition chimique de chaque tranche

! Premiere iteration

        icourant =0

        if(icourant.eq.0) then
          I = 1
	  iadj = 2
	else
          I = NX
	  iadj = nx - 1
	endif

	CALL compute_modpre (reacbrul,YNAJ,ybF(I),ybO(I)&
                     &,amfu,amch4,yOaj,yCO2aj,yH2Oaj)
	yFaj=amfu+amch4
	yAraj=ybAr(I)
	yNaj=ybN(I)+YNAJ
	yCO2aj=yCO2aj+ybco2(I)
	yH2Oaj=yH2Oaj+ybh2o(I)

	DP(I)=MB(I)+DEG(I)+DR(iadj)-SOR(I) !BILAN TOTAL
	yO(I)=(yOaj*mb(I)+YODEG(I)*DEG(I)+YO(iadj)*DR(iadj))/(SMALL+DP(I)+SOR(I)) !OXYGENE
	yCO2(I)=(yCO2aj*mb(I)+YCO2DEG(I)*DEG(I)+YCO2(iadj)*DR(iadj))/(SMALL+DP(I)+SOR(I)) !CO2
	yH2O(I)=(yH2Oaj*mb(I)+YH2ODEG(I)*DEG(I)+YH2O(iadj)*DR(iadj))/(SMALL+DP(I)+SOR(I)) !BILAN DE H2O
	yAr(I)=(yARaj*mb(I)+YARDEG(I)*DEG(I)+YAR(iadj)*DR(iadj))/(SMALL+DP(I)+SOR(I)) !BILAN D ARGON
	yF(I)=(yFaj*mb(I)+YFDEG(I)*DEG(I)+YF(iadj)*DR(iadj))/(SMALL+DP(I)+SOR(I)) !BILAN DE COMBUSTIBLE
        yN(i)  = 1.-yF(i)-yO(i)-yCO2(i)-yH2O(i)-yAr(i)
        if (yN(i).lt.1.0e-6) yN(i)=0.0
	
	ybf(I)=yfaj
	ybo(I)=yoaj
	ybco2(I)=yco2aj
	ybh2o(I)=yh2oaj
	YBN(I)=YNAJ

        if(icourant.eq.0) then
          ideb = 2
	  ifin = nx - 1
	  iincr = 1
	else
	  ideb = nx - 1
	  ifin = 2
	  iincr = -1
	endif

	DO I=ideb, ifin, iincr

          if(icourant.eq.0) then
	    iamont = i-1
	    iaval =  i+1
	  else
            iamont = i+1
	    iaval =  i-1
	  endif

	  CALL compute_modpre (reacbrul,YNAJ,ybF(I),ybO(I),amfu,amch4,yOaj,yCO2aj,yH2Oaj)
	  yFaj=amfu+amch4
	  yAraj=ybAr(i)
	  yNaj=YNAJ+ybN(i)

	  yCO2aj=yCO2aj+ybco2(i)
	  yH2Oaj=yH2Oaj+ybh2o(i)

	  DP(I)=MB(I)+DEG(I)+DR(iaval)+DP(iamont)-DR(I)-SOR(I)

	  yO(I)=(yOaj*mb(I)+YODEG(I)*DEG(I)+YO(iaval)*DR(iaval)+YO(iamont)*DP(iamont))&
                &/(SMALL+DP(I)+DR(I)+SOR(I))
	  yCO2(I)=(yCO2aj*mb(I)+YCO2DEG(I)*DEG(I)+YCO2(iaval)*DR(iaval)+YCO2(iamont)*DP(iamont))&
                &/(SMALL+DP(I)+DR(I)+SOR(I))
	  yH2O(I)=(yH2Oaj*mb(I)+YH2ODEG(I)*DEG(I)+YH2O(iaval)*DR(iaval)+YH2O(iamont)*DP(iamont))&
                &/(SMALL+DP(I)+DR(I)+SOR(I))
	  yAr(I)=(yARaj*mb(I)+YARDEG(I)*DEG(I)+YAR(iaval)*DR(iaval)+YAR(iamont)*DP(iamont))&
                &/(SMALL+DP(I)+DR(I)+SOR(I))
	  yF(I)=(yFaj*mb(I)+YFDEG(I)*DEG(I)+YF(iaval)*DR(iaval)+YF(iamont)*DP(iamont))&
                &/(SMALL+DP(I)+DR(I)+SOR(I))
          yN(i)  = 1.-yF(i)-yO(i)-yCO2(i)-yH2O(i)-yAr(i)
          if (yN(i).lt.1.0e-6) yN(i)=0.0

	  ybf(i)=yfaj
	  ybo(i)=yoaj
	  ybco2(i)=yco2aj
	  ybh2o(i)=yh2oaj
	  ybN(i)=yNaj
	ENDDO
   
        if(icourant.eq.0) then
          I = NX
	  iadj = NX - 1
	else
	  i = 1
	  iadj = 2
	endif

	CALL compute_modpre (reacbrul,YNAJ,ybF(I),ybO(I),amfu,amch4,yOaj,yCO2aj,yH2Oaj)
	yFaj=amfu+amch4
	yAraj=ybAr(I)
	yNaj=YNAJ+ybN(I)
	yCO2aj=yCO2aj+ybco2(I)
	yH2Oaj=yH2Oaj+ybh2o(I)

           soment=0.0
           somsor=0.0
           do ii=1,nx
           soment=soment+MB(ii)+DEG(ii)
           somsor=somsor+SOR(ii)
           enddo

	SOR(I)=MB(I)+DEG(I)+DP(iadj)-DR(I)

	yO(I)=(yOaj*mb(I)+YODEG(I)*DEG(I)+YO(iadj)*DP(iadj))&
              &/(SMALL+DR(I)+SOR(I))
	yCO2(I)=(yCO2aj*mb(I)+YCO2DEG(I)*DEG(I)+YCO2(iadj)*DP(iadj))&
              &/(SMALL+DR(I)+SOR(I))
	yH2O(I)=(yH2Oaj*mb(I)+YH2ODEG(I)*DEG(I)+YH2O(iadj)*DP(iadj))&
              &/(SMALL+DR(I)+SOR(I))
	yAr(I)=(yARaj*mb(I)+YARDEG(I)*DEG(I)+YAR(iadj)*DP(iadj))&
              &/(SMALL+DR(I)+SOR(I))
	yF(I)=(yFaj*mb(I)+YFDEG(I)*DEG(I)+YF(iadj)*DP(iadj))&
              &/(SMALL+DR(I)+SOR(I))
          yN(i)  = 1.-yF(i)-yO(i)-yCO2(i)-yH2O(i)-yAr(i)
          if (yN(i).lt.1.0e-6) yN(i)=0.0

	ybf(I)=yfaj
	ybo(I)=yoaj
	ybco2(I)=yco2aj
	ybh2o(I)=yh2oaj
	YBN(I)=YNAJ

! iterations suivantes
!---------------------

        iterbil=1
        dr(0)=0.0
        dr(1)=0.0
        dr(nx+1)=0.0
        dp(0)=0.0
        dp(nx)=0.0
        dp(nx+1)=0.0

	DO I=1,NX
          somme=yO(I)+yCO2(I)+yH2O(I)+yAr(I)+yF(I)+yN(I)
          if (somme.gt.1.1) then
            print *,' SOLVE_MASS_BALANCE : somme > 1.1 ',i,somme
            print *,mb(I),DP(iamont),DR(iaval)
            print *,DP(I),DR(I),SOR(I)
            print *,yN(I-1),yO(I-1),yAr(I-1),yH2O(I-1),yCO2(I-1),yF(I-1)
            print *,yN(I),yO(I),yAr(I),yH2O(I),yCO2(I),yF(I)
          endif
          if (somme.gt.0.0) then
	    yO(I)=yO(I)/somme
	    yCO2(I)=yCO2(I)/somme
	    yH2O(I)=yH2O(I)/somme
	    yAr(I)=yAr(I)/somme
	    yF(I)=yF(I)/somme
	    yN(I)=yN(I)/somme
          else
            print *,' SOLVE_MASS_BALANCE : somme = 0.0 ',i,somme
          endif
	ENDDO

        icourant=0

 100	CONTINUE

	YN1=YN
	YO1=YO
	YCO21=YCO2
	YH2O1=YH2O
	YAR1=YAR
	YF1=YF

        icourant=0
        if(icourant.eq.0) then
          ideb = 1
	  ifin = nx
	  iincr = 1
	else
	  ideb = nx
	  ifin = 1
	  iincr = -1
	endif

	DO I=ideb, ifin, iincr

	    iamont = i-1
	    iaval =  i+1

          if (DP(i).gt.0.0) then

	  yO(I)=(YBO(I)*mb(I)+YODEG(I)*DEG(I)+YO(iaval)*DR(iaval)+YO(iamont)*DP(iamont))&
                &/(SMALL+DP(I)+DR(I)+SOR(I))
	  yCO2(I)=(YBCO2(I)*mb(I)+YCO2DEG(I)*DEG(I)+YCO2(iaval)*DR(iaval)+YCO2(iamont)*DP(iamont))&
                &/(SMALL+DP(I)+DR(I)+SOR(I))
	  yH2O(I)=(YBH2O(I)*mb(I)+YH2ODEG(I)*DEG(I)+YH2O(iaval)*DR(iaval)+YH2O(iamont)*DP(iamont))&
                &/(SMALL+DP(I)+DR(I)+SOR(I))
	  yAr(I)=(YBAR(I)*mb(I)+YARDEG(I)*DEG(I)+YAR(iaval)*DR(iaval)+YAR(iamont)*DP(iamont))&
                &/(SMALL+DP(I)+DR(I)+SOR(I))
	  yF(I)=(YBF(I)*mb(I)+YFDEG(I)*DEG(I)+YF(iaval)*DR(iaval)+YF(iamont)*DP(iamont))&
                &/(SMALL+DP(I)+DR(I)+SOR(I))
	  yN(I)=(YBN(I)*mb(I)+YNDEG(I)*DEG(I)+YN(iaval)*DR(iaval)+YN(iamont)*DP(iamont))&
                &/(SMALL+DP(I)+DR(I)+SOR(I))
!cth          yN(i)  = 1.-yF(i)-yO(i)-yCO2(i)-yH2O(i)-yAr(i)

          somme=yO(I)+yCO2(I)+yH2O(I)+yAr(I)+yF(I)+yN(I)
          if (somme.gt.0.0) then
            yO(I)=yO(I)/somme
            yCO2(I)=yCO2(I)/somme
            yH2O(I)=yH2O(I)/somme
            yAr(I)=yAr(I)/somme
            yF(I)=yF(I)/somme
            yN(I)=yN(I)/somme
          else
            print *,' SOLVE_MASS_BALANCE : DO somme = 0.0 ',i,somme
          endif

          endif

	ENDDO

        icourant=1
        if(icourant.eq.0) then
          ideb = 1
          ifin = nx
          iincr = 1
        else
          ideb = nx
          ifin = 1
          iincr = -1
        endif

        DO I=ideb, ifin, iincr

            iamont = i-1
            iaval =  i+1

          if (DR(i).gt.0.0) then

          yO(I)=(YBO(I)*mb(I)+YODEG(I)*DEG(I)+YO(iaval)*DR(iaval)+YO(iamont)*DP(iamont))&
                &/(SMALL+DP(I)+DR(I)+SOR(I))
          yCO2(I)=(YBCO2(I)*mb(I)+YCO2DEG(I)*DEG(I)+YCO2(iaval)*DR(iaval)+YCO2(iamont)*DP(iamont))&
                &/(SMALL+DP(I)+DR(I)+SOR(I))
          yH2O(I)=(YBH2O(I)*mb(I)+YH2ODEG(I)*DEG(I)+YH2O(iaval)*DR(iaval)+YH2O(iamont)*DP(iamont))&
                &/(SMALL+DP(I)+DR(I)+SOR(I))
          yAr(I)=(YBAR(I)*mb(I)+YARDEG(I)*DEG(I)+YAR(iaval)*DR(iaval)+YAR(iamont)*DP(iamont))&
                &/(SMALL+DP(I)+DR(I)+SOR(I))
          yF(I)=(YBF(I)*mb(I)+YFDEG(I)*DEG(I)+YF(iaval)*DR(iaval)+YF(iamont)*DP(iamont))&
                &/(SMALL+DP(I)+DR(I)+SOR(I))
          yN(I)=(YBN(I)*mb(I)+YNDEG(I)*DEG(I)+YN(iaval)*DR(iaval)+YN(iamont)*DP(iamont))&
                &/(SMALL+DP(I)+DR(I)+SOR(I))

          somme=yO(I)+yCO2(I)+yH2O(I)+yAr(I)+yF(I)+yN(I)
          if (somme.gt.0.0) then
            yO(I)=yO(I)/somme
            yCO2(I)=yCO2(I)/somme
            yH2O(I)=yH2O(I)/somme
            yAr(I)=yAr(I)/somme
            yF(I)=yF(I)/somme
            yN(I)=yN(I)/somme
          else
            print *,' SOLVE_MASS_BALANCE : DO somme = 0.0 ',i,somme
          endif

          endif

        ENDDO

        icourant=0
        if(icourant.eq.0) then
          ideb = 1
          ifin = nx
          iincr = 1
        else
          ideb = nx
          ifin = 1
          iincr = -1
        endif

        DO I=ideb, ifin, iincr

            iamont = i-1
            iaval =  i+1

          if (DP(I).le.0.0.and.DR(i).le.0.0) then

          yO(I)=(YBO(I)*mb(I)+YODEG(I)*DEG(I)+YO(iaval)*DR(iaval)+YO(iamont)*DP(iamont))&
                &/(SMALL+DP(I)+DR(I)+SOR(I))
          yCO2(I)=(YBCO2(I)*mb(I)+YCO2DEG(I)*DEG(I)+YCO2(iaval)*DR(iaval)+YCO2(iamont)*DP(iamont))&
                &/(SMALL+DP(I)+DR(I)+SOR(I))
          yH2O(I)=(YBH2O(I)*mb(I)+YH2ODEG(I)*DEG(I)+YH2O(iaval)*DR(iaval)+YH2O(iamont)*DP(iamont))&
                &/(SMALL+DP(I)+DR(I)+SOR(I))
          yAr(I)=(YBAR(I)*mb(I)+YARDEG(I)*DEG(I)+YAR(iaval)*DR(iaval)+YAR(iamont)*DP(iamont))&
                &/(SMALL+DP(I)+DR(I)+SOR(I))
          yF(I)=(YBF(I)*mb(I)+YFDEG(I)*DEG(I)+YF(iaval)*DR(iaval)+YF(iamont)*DP(iamont))&
                &/(SMALL+DP(I)+DR(I)+SOR(I))
          yN(I)=(YBN(I)*mb(I)+YNDEG(I)*DEG(I)+YN(iaval)*DR(iaval)+YN(iamont)*DP(iamont))&
                &/(SMALL+DP(I)+DR(I)+SOR(I))

          somme=yO(I)+yCO2(I)+yH2O(I)+yAr(I)+yF(I)+yN(I)
          if (somme.gt.0.0) then
            yO(I)=yO(I)/somme
            yCO2(I)=yCO2(I)/somme
            yH2O(I)=yH2O(I)/somme
            yAr(I)=yAr(I)/somme
            yF(I)=yF(I)/somme
            yN(I)=yN(I)/somme
          else
            print *,' SOLVE_MASS_BALANCE : DO somme = 0.0 ',i,somme
          endif

          endif

        ENDDO

	DO I=1,NX
          somme=yO(I)+yCO2(I)+yH2O(I)+yAr(I)+yF(I)+yN(I)
          if (somme.gt.1.1) then
            print *,' SOLVE_MASS_BALANCE : somme > 1.1 ',i,somme
            print *,mb(I),DP(iamont),DR(iaval)
            print *,DP(I),DR(I),SOR(I)
            print *,yN(I-1),yO(I-1),yAr(I-1),yH2O(I-1),yCO2(I-1),yF(I-1)
            print *,yN(I),yO(I),yAr(I),yH2O(I),yCO2(I),yF(I)
          endif
          if (somme.gt.0.0) then
	    yO(I)=yO(I)/somme
	    yCO2(I)=yCO2(I)/somme
	    yH2O(I)=yH2O(I)/somme
	    yAr(I)=yAr(I)/somme
	    yF(I)=yF(I)/somme
	    yN(I)=yN(I)/somme
          else
            print *,' SOLVE_MASS_BALANCE : somme = 0.0 ',i,somme
          endif
	ENDDO
	ERREUR=0.  !calcul de l erreur (norme sup)
	DO I=1,NX
	  ERREUR=MAX(ERREUR,ABS(YN(I)-YN1(I)))
	  ERREUR=MAX(ERREUR,ABS(YO(I)-YO1(I)))
	  ERREUR=MAX(ERREUR,ABS(YCO2(I)-YCO21(I)))
	  ERREUR=MAX(ERREUR,ABS(YH2O(I)-YH2O1(I)))
	  ERREUR=MAX(ERREUR,ABS(YAR(I)-YAR1(I)))
	  ERREUR=MAX(ERREUR,ABS(YF(I)-YF1(I)))
	ENDDO
	IF (ERREUR.GT.SMALL) THEN
          iterbil=iterbil+1
          
          GOTO 100
        ENDIF

! existe-t-il des tranches ou toutes les concentrations sont nulles?
!-------------------------------------------------------------------
	DO I=1,NX
	  IF (DP(I)+DR(I)+SOR(I).LT.SMALL) THEN
	    PRINT*,'LA TRANCHE',I,'EST ISOLEE'
	    J1=0
	    J2=0
	    IF (I.EQ.1) GOTO 10
	    J=I
	    DO WHILE ((SOR(J).LT.SMALL).AND.(J.GT.0))
	      J=J-1
	    ENDDO
	    J1=J
10	    CONTINUE
	    IF (I.EQ.NX) GOTO 20
	    J=I
	    DO WHILE ((SOR(J).LT.SMALL).AND.(J.LT.NX+1))
	      J=J+1
	    ENDDO
	    J2=J
	    IF (J2.GT.NX) J2=0
20	    CONTINUE
	    IF (J1.EQ.0) THEN
	      yN(I)=YN(J2)
	      YO(I)=YO(J2)
	      YCO2(I)=YCO2(J2)
	      YH2O(I)=YH2O(J2)
	      YAR(I)=YAR(J2)
	      YF(I)=YF(J2)
	    ELSEIF (J2.EQ.0) THEN
	      yN(I)=YN(J1)
	      YO(I)=YO(J1)
	      YCO2(I)=YCO2(J1)
	      YH2O(I)=YH2O(J1)
	      YAR(I)=YAR(J1)
	      YF(I)=YF(J1)
	    ELSE
	      yN(I)=(YN(J1)+YN(J2))*0.5
	      YO(I)=(YO(J1)+YO(J2))*0.5
	      YCO2(I)=(YCO2(J1)+YCO2(J2))*0.5
	      YH2O(I)=(YH2O(J1)+YH2O(J2))*0.5
	      YAR(I)=(YAR(J1)+YAR(J2))*0.5
	      YF(I)=(YF(J1)+YF(J2))*0.5
	    ENDIF
	  ENDIF
        ENDDO

!gm  Calcul de la masse molaire des fumees
        do i=1,nx
          WMfum(i) = 1.0/(yN(i)/WMN2+yO(i)/WMO2+yCO2(i)/WMCO2+yH2O(i)/WMH2O+yAr(i)/WMAr+yF(i)/WM)
        enddo

!cth Bilan par espece
        dbeN=0.0
        dbeO=0.0
        dbeCO2=0.0
        dbeH2O=0.0
        dbeAr=0.0
        dbeF=0.0
        dboN=0.0
        dboO=0.0
        dboCO2=0.0
        dboH2O=0.0
        dboAr=0.0
        dboF=0.0
        bilN=0.0
        bilO=0.0
        bilCO2=0.0
        bilH2O=0.0
        bilAr=0.0
        bilF=0.0
        rbilN=0.0
        rbilO=0.0
        rbilCO2=0.0
        rbilH2O=0.0
        rbilAr=0.0
        rbilF=0.0
        do i=1,nx
          dbeN=dbeN+mb(i)*ybN(i)
          dbeO=dbeO+mb(i)*ybO(i)
          dbeCO2=dbeCO2+mb(i)*ybCO2(i)
          dbeH2O=dbeH2O+mb(i)*ybH2O(i)
          dbeAr=dbeAr+mb(i)*ybAr(i)
          dbeF=dbeF+mb(i)*ybF(i)
          dboN=dboN+sor(i)*yN(i)
          dboO=dboO+sor(i)*yO(i)
          dboCO2=dboCO2+sor(i)*yCO2(i)
          dboH2O=dboH2O+sor(i)*yH2O(i)
          dboAr=dboAr+sor(i)*yAr(i)
          dboF=dboF+sor(i)*yF(i)
        enddo
        bilN=abs(dboN-dbeN)
        bilO=abs(dboO-dbeO)
        bilCO2=abs(dboCO2-dbeCO2)
        bilH2O=abs(dboH2O-dbeH2O)
        bilAr=abs(dboAr-dbeAr)
        bilF=abs(dboF-dbeF)
        if (dbeN.gt.0.0) rbilN=bilN/dbeN
        if (dbeO.gt.0.0) rbilO=bilO/dbeO
        if (dbeCO2.gt.0.0) rbilCO2=bilCO2/dbeCO2
        if (dbeH2O.gt.0.0) rbilH2O=bilH2O/dbeH2O
        if (dbeAr.gt.0.0) rbilAr=bilAr/dbeAr
        if (dbeF.gt.0.0) rbilF=bilF/dbeF
		if(ires(2).eq.2) then
          print *,' SOLVE_MASS_BALANCE : BILAN PAR ESPECE : '
          print *,' --> N   : ',dbeN,dboN,bilN,rbilN
          print *,' --> O   : ',dbeO,dboO,bilO,rbilO
          print *,' --> CO2 : ',dbeCO2,dboCO2,bilCO2,rbilCO2
          print *,' --> H2O : ',dbeH2O,dboH2O,bilH2O,rbilH2O
          print *,' --> Ar  : ',dbeAr,dboAr,bilAr,rbilAr
          print *,' --> F   : ',dbeF,dboF,bilF,rbilF
        endif

!cth Composition volumique des fumees en NX
        do iesp=1,ncodesp
          xfumeemol(iesp)=0.0
        enddo
        xfumeemol(icodn2)=yN(nx)/wmn2
        xfumeemol(icodo2)=YO(nx)/wmo2
        xfumeemol(icodco2)=YCO2(nx)/wmco2
        xfumeemol(icodh2o)=YH2O(nx)/wmh2o
        xfumeemol(icodar)=YAR(nx)/wmar
        xfumeemol(icodch4)=YF(nx)/wm

!cth Calcul debit sortant pour chaque tranche
        do i=1,nx
          debitglob(i)=DP(i)+DR(i)+SOR(i)
        enddo
!cth Calcul debit pour convection pour chaque tranche
        do i=1,nx
!cth          debitp=0.0
!cth          debitr=0.0
          debitp=1.0e-15
          debitr=1.0e-15
          ndebit=0
          if (dp(I).gt.0.0) then
            ndebit=ndebit+1
            debitp=debitp+1.0
          endif
          if (dr(I+1).gt.0.0) then
            ndebit=ndebit+1
            debitr=debitr+1.0
          endif
          if (ndebit.ge.1) then
            debitconv(i)=(dp(I)+dr(I+1))/(debitp+debitr)
          endif
        enddo
!cth        debitp=0.0
!cth        debitr=0.0
        debitp=1.0e-15
        debitr=1.0e-15
        ndebit=0
        if (dp(nx-1).gt.0.0) then
          ndebit=ndebit+1
          debitp=debitp+1.0
        endif
        if (dr(nx).gt.0.0) then
          ndebit=ndebit+1
          debitr=debitr+1.0
        endif
        if (ndebit.ge.1) then
          debitconv(nx)=(dp(nx-1)+dr(nx))/(debitp+debitr)
        endif

!cth         print *,' SOLVE_MASS : DEBIT CONVECTION : '
!cth        do i=1,nx
!cth          print *,i,debitconv(i),dp(i),dr(i),dpreel(i)
!cth        enddo
!cth          print *,' '

! ecriture des resultats
!-----------------------
        if(ires(1).eq.1) then
	  filename=casename(1:lcasename)//'_'//moduname(code_max)(1:longcar(moduname(code_max)))//'.compo'
	  open (unit=18,file=filename)
180	  format (i4,3x,f7.2,10(3x,f9.6))
	  write (18,*) 'Debit en sortie de chaque maille-sens direct et recirculation'
	  write (18,*) ' Iter     Debit       yN2       yO2',&
        &'       yCO2       yH2O       yAr       yF    Som'
          somme=0.0
	  DO i=1,NX
           somme=yN(i)+yO(i)+yCO2(i)+yH2O(i)+yAr(i)+yF(i)
	   write (18,180) I,DP(I),yN(i),yO(i),yCO2(i),yH2O(i),yAr(i),yF(i),somme
	  ENDDO
	  write (18,*) 'Debit des bruleurs de chaque maille-sens direct et recirculation'
	  write (18,*) ' Iter     Debit       yN2       yO2',&
        &'       yCO2       yH2O       yAr       yF    Som'
          somme=0.0
	  DO i=1,NX
           somme=ybN(i)+ybO(i)+ybCO2(i)+ybH2O(i)+ybAr(i)+ybF(i)
	   write (18,180) I,DP(I),ybN(i),ybO(i),ybCO2(i),ybH2O(i),ybAr(i),ybF(i),somme
	  ENDDO
	  close (18)
	endif
!
! Fin du programme
!-----------------
	END
